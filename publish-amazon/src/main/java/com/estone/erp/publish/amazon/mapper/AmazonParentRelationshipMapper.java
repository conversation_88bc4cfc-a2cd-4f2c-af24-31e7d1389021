package com.estone.erp.publish.amazon.mapper;

import com.estone.erp.publish.amazon.model.AmazonParentRelationship;
import com.estone.erp.publish.amazon.model.AmazonParentRelationshipExample;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface AmazonParentRelationshipMapper {
    int countByExample(AmazonParentRelationshipExample example);

    int deleteByPrimaryKey(List<Long> ids);

    int insert(AmazonParentRelationship record);

    AmazonParentRelationship selectByPrimaryKey(Long id);

    List<AmazonParentRelationship> selectByExample(AmazonParentRelationshipExample example);

    int updateByExampleSelective(@Param("record") AmazonParentRelationship record, @Param("example") AmazonParentRelationshipExample example);

    int updateByPrimaryKeySelective(AmazonParentRelationship record);

    int batchInsert(@Param("list") List<AmazonParentRelationship> list);

    void batchUpdate(@Param("list") List<AmazonParentRelationship> list);

    List<AmazonParentRelationship> selectCustomColumnByExample(AmazonParentRelationshipExample example);
}
package com.estone.erp.publish.amazon.model;

import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR> amazon_publish_fail_type_kanban
 * 2023-12-19 15:27:47
 */
public class AmazonPublishFailTypeKanbanCriteria extends AmazonPublishFailTypeKanban {
    private static final long serialVersionUID = 1L;

    public AmazonPublishFailTypeKanbanExample getExample() {
        AmazonPublishFailTypeKanbanExample example = new AmazonPublishFailTypeKanbanExample();
        AmazonPublishFailTypeKanbanExample.Criteria criteria = example.createCriteria();
        if (StringUtils.isNotBlank(this.getSite())) {
            criteria.andSiteEqualTo(this.getSite());
        }
        if (StringUtils.isNotBlank(this.getErrorType())) {
            criteria.andErrorTypeEqualTo(this.getErrorType());
        }
        if (StringUtils.isNotBlank(this.getSolutionType())) {
            criteria.andSolutionTypeEqualTo(this.getSolutionType());
        }
        if (this.getCountNumber() != null) {
            criteria.andCountNumberEqualTo(this.getCountNumber());
        }
        if (this.getPublishDate() != null) {
            criteria.andPublishDateEqualTo(this.getPublishDate());
        }
        if (this.getCreatedTime() != null) {
            criteria.andCreatedTimeEqualTo(this.getCreatedTime());
        }
        if (this.getUpdatedTime() != null) {
            criteria.andUpdatedTimeEqualTo(this.getUpdatedTime());
        }
        return example;
    }
}
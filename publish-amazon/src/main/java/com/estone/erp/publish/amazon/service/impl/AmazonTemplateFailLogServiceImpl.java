package com.estone.erp.publish.amazon.service.impl;

import com.estone.erp.common.model.api.CQuery;
import com.estone.erp.common.model.api.CQueryResult;
import com.estone.erp.publish.amazon.mapper.AmazonTemplateFailLogMapper;
import com.estone.erp.publish.amazon.model.AmazonTemplateFailLog;
import com.estone.erp.publish.amazon.model.AmazonTemplateFailLogCriteria;
import com.estone.erp.publish.amazon.model.AmazonTemplateFailLogExample;
import com.estone.erp.publish.amazon.service.AmazonTemplateFailLogService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR> amazon_template_fail_log
 * 2023-08-16 14:14:29
 */
@Service("amazonTemplateFailLogService")
@Slf4j
public class AmazonTemplateFailLogServiceImpl implements AmazonTemplateFailLogService {
    @Resource
    private AmazonTemplateFailLogMapper amazonTemplateFailLogMapper;

    @Override
    public int countByExample(AmazonTemplateFailLogExample example) {
        Assert.notNull(example, "example is null!");
        return amazonTemplateFailLogMapper.countByExample(example);
    }

    @Override
    public CQueryResult<AmazonTemplateFailLog> search(CQuery<AmazonTemplateFailLogCriteria> cquery) {
        Assert.notNull(cquery, "cquery is null!");
        AmazonTemplateFailLogCriteria query = cquery.getSearch();
        AmazonTemplateFailLogExample example = query.getExample();
        long total = 0;
        int totalPages = 0;
        // 是否分页
        if (cquery.isPageReqired()) {
            total = amazonTemplateFailLogMapper.countByExample(example);
            totalPages = (int) Math.ceil((double) total / cquery.getLimit());
            example.setLimit(cquery.getLimit());
            example.setOffset(cquery.getOffset());
        }
        List<AmazonTemplateFailLog> amazonTemplateFailLogs = amazonTemplateFailLogMapper.selectByExample(example);
        // 组装结果
        CQueryResult<AmazonTemplateFailLog> result = new CQueryResult<>();
        result.setTotal(total);
        result.setTotalPages(totalPages);
        result.setRows(amazonTemplateFailLogs);
        return result;
    }

    @Override
    public AmazonTemplateFailLog selectByPrimaryKey(Integer id) {
        Assert.notNull(id, "id is null!");
        return amazonTemplateFailLogMapper.selectByPrimaryKey(id);
    }

    @Override
    public List<AmazonTemplateFailLog> selectByExample(AmazonTemplateFailLogExample example) {
        Assert.notNull(example, "example is null!");
        return amazonTemplateFailLogMapper.selectByExample(example);
    }

    @Override
    public int insert(AmazonTemplateFailLog record) {
        Assert.notNull(record, "record is null!");
        // 默认加时间和人
        return amazonTemplateFailLogMapper.insert(record);
    }

    @Override
    public int updateByPrimaryKeySelective(AmazonTemplateFailLog record) {
        Assert.notNull(record, "record is null!");
        // 默认加修改时间和修改人
        return amazonTemplateFailLogMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public int updateByExampleSelective(AmazonTemplateFailLog record, AmazonTemplateFailLogExample example) {
        Assert.notNull(record, "record is null!");
        // 默认加修改时间和修改人
        return amazonTemplateFailLogMapper.updateByExampleSelective(record, example);
    }

    @Override
    public int deleteByPrimaryKey(List<Integer> ids) {
        Assert.notNull(ids, "ids is null!");
        return amazonTemplateFailLogMapper.deleteByPrimaryKey(ids);
    }

    @Override
    public void addFailLog(Integer templateId, Long processReportId, String errorMsg) {
        if (templateId == null) {
            return;
        }
        AmazonTemplateFailLog amazonTemplateFailLog = new AmazonTemplateFailLog();
        amazonTemplateFailLog.setTemplateId(templateId);
        amazonTemplateFailLog.setReportId(processReportId);
        amazonTemplateFailLog.setMessage(errorMsg);
        amazonTemplateFailLog.setCreatedTime(Timestamp.valueOf(LocalDateTime.now()));
        amazonTemplateFailLogMapper.insert(amazonTemplateFailLog);
    }
}
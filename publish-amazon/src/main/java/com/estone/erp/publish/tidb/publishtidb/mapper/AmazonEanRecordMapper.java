package com.estone.erp.publish.tidb.publishtidb.mapper;

import com.estone.erp.publish.tidb.publishtidb.model.AmazonEanRecord;
import com.estone.erp.publish.tidb.publishtidb.model.AmazonEanRecordExample;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface AmazonEanRecordMapper {
    int countByExample(AmazonEanRecordExample example);

    int deleteByPrimaryKey(List<Long> ids);

    int insert(AmazonEanRecord record);

    AmazonEanRecord selectByPrimaryKey(Long id);

    List<AmazonEanRecord> selectByExample(AmazonEanRecordExample example);

    int updateByExampleSelective(@Param("record") AmazonEanRecord record, @Param("example") AmazonEanRecordExample example);

    int updateByPrimaryKeySelective(AmazonEanRecord record);

    List<String> selectExistEanByExample(AmazonEanRecordExample example);

    int batchInsert(@Param("list")List<AmazonEanRecord> amazonEanRecordList);
}
package com.estone.erp.publish.amazon.mapper;

import com.estone.erp.publish.amazon.model.AmazonAccountConfig;
import com.estone.erp.publish.amazon.model.AmazonAccountConfigExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface AmazonAccountConfigMapper {
    int countByExample(AmazonAccountConfigExample example);

    int deleteByPrimaryKey(List<Integer> ids);

    int insert(AmazonAccountConfig record);

    AmazonAccountConfig selectByPrimaryKey(Integer id);

    List<AmazonAccountConfig> selectByExample(AmazonAccountConfigExample example);

    int updateByExampleSelective(@Param("record") AmazonAccountConfig record, @Param("example") AmazonAccountConfigExample example);

    int updateByPrimaryKeySelective(AmazonAccountConfig record);
}
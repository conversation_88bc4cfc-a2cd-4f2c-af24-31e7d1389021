package com.estone.erp.publish.amazon.service.impl;

import com.estone.erp.common.model.api.CQuery;
import com.estone.erp.common.model.api.CQueryResult;
import com.estone.erp.common.util.DateUtils;
import com.estone.erp.publish.amazon.enums.AmaoznPublishStatusEnum;
import com.estone.erp.publish.amazon.mapper.SystemTimingQueueStatisticsMapper;
import com.estone.erp.publish.amazon.model.*;
import com.estone.erp.publish.amazon.service.AmazonAccountConfigService;
import com.estone.erp.publish.amazon.service.AmazonAccountRelationService;
import com.estone.erp.publish.amazon.service.AmazonTemplateService;
import com.estone.erp.publish.amazon.service.SystemTimingQueueStatisticsService;

import java.sql.Timestamp;
import java.util.Date;
import java.util.List;
import javax.annotation.Resource;

import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.common.enums.PublishRoleEnum;
import com.estone.erp.publish.platform.service.TemplateQueueService;
import com.estone.erp.publish.system.scheduler.util.RecordStatus;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

/**
 * <AUTHOR> system_timing_queue_statistics
 * 2024-01-17 14:07:48
 */
@Service("systemTimingQueueStatisticsService")
@Slf4j
public class SystemTimingQueueStatisticsServiceImpl implements SystemTimingQueueStatisticsService {
    @Resource
    private SystemTimingQueueStatisticsMapper systemTimingQueueStatisticsMapper;

    @Resource
    private TemplateQueueService templateQueueService;

    @Resource
    private AmazonAccountConfigService amazonAccountConfigService;

    @Resource
    private AmazonAccountRelationService amazonAccountRelationService;

    @Resource
    private AmazonTemplateService amazonTemplateService;

    @Override
    public int countByExample(SystemTimingQueueStatisticsExample example) {
        Assert.notNull(example, "example is null!");
        return systemTimingQueueStatisticsMapper.countByExample(example);
    }

    @Override
    public CQueryResult<SystemTimingQueueStatistics> search(CQuery<SystemTimingQueueStatisticsCriteria> cquery) {
        Assert.notNull(cquery, "cquery is null!");
        SystemTimingQueueStatisticsCriteria query = cquery.getSearch();
        SystemTimingQueueStatisticsExample example = query.getExample();
        long total = 0;
        int totalPages = 0;
        // 是否分页
        if (cquery.isPageReqired()) {
            total = systemTimingQueueStatisticsMapper.countByExample(example);
            totalPages = (int) Math.ceil((double) total / cquery.getLimit());
            example.setLimit(cquery.getLimit());
            example.setOffset(cquery.getOffset());
        }
        example.setOrderByClause("day desc");
        List<SystemTimingQueueStatistics> systemTimingQueueStatisticss = systemTimingQueueStatisticsMapper.selectByExample(example);
        // 组装结果
        CQueryResult<SystemTimingQueueStatistics> result = new CQueryResult<>();
        result.setTotal(total);
        result.setTotalPages(totalPages);
        result.setRows(systemTimingQueueStatisticss);
        return result;
    }

    @Override
    public SystemTimingQueueStatistics selectByPrimaryKey(Integer id) {
        Assert.notNull(id, "id is null!");
        return systemTimingQueueStatisticsMapper.selectByPrimaryKey(id);
    }

    @Override
    public List<SystemTimingQueueStatistics> selectByExample(SystemTimingQueueStatisticsExample example) {
        Assert.notNull(example, "example is null!");
        return systemTimingQueueStatisticsMapper.selectByExample(example);
    }

    @Override
    public int insert(SystemTimingQueueStatistics record) {
        Assert.notNull(record, "record is null!");
        // 默认加时间和人
        return systemTimingQueueStatisticsMapper.insert(record);
    }

    @Override
    public int updateByPrimaryKeySelective(SystemTimingQueueStatistics record) {
        Assert.notNull(record, "record is null!");
        // 默认加修改时间和修改人
        return systemTimingQueueStatisticsMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public int updateByExampleSelective(SystemTimingQueueStatistics record, SystemTimingQueueStatisticsExample example) {
        Assert.notNull(record, "record is null!");
        // 默认加修改时间和修改人
        return systemTimingQueueStatisticsMapper.updateByExampleSelective(record, example);
    }

    @Override
    public int deleteByPrimaryKey(List<Integer> ids) {
        Assert.notNull(ids, "ids is null!");
        return systemTimingQueueStatisticsMapper.deleteByPrimaryKey(ids);
    }

    @Override
    public void syncStatistics(AmazonAccountRelation accountRelation, String day) {
        String site = accountRelation.getAccountCountry();
        String accountNumber = accountRelation.getAccountNumber();

        Date currentDay = DateUtils.parseDate(day, "yyyy-MM-dd");
        SystemTimingQueueStatisticsExample example = new SystemTimingQueueStatisticsExample();
        example.createCriteria()
                .andAccountNumberEqualTo(accountNumber)
                .andSiteEqualTo(site)
                .andDayEqualTo(currentDay);
        List<SystemTimingQueueStatistics> systemTimingQueueStatisticsList = systemTimingQueueStatisticsMapper.selectByExample(example);
        SystemTimingQueueStatistics statistics = null;
        if (systemTimingQueueStatisticsList.size() > 0) {
            statistics = systemTimingQueueStatisticsList.get(0);
        } else {
            statistics = new SystemTimingQueueStatistics();
            statistics.setAccountNumber(accountNumber);
            statistics.setDay(currentDay);
            statistics.setSite(site);

            statistics.setPublishIntervalTime(accountRelation.getPublishIntervalTime());
            statistics.setPublishQuantity(accountRelation.getPublishQuantity());
            statistics.setMinPublishMount(accountRelation.getMinPublishMount());
        }
        // 生成的定时队列数量
        int generateQueue = templateQueueService.statisticsDayGenerateQueue(SaleChannel.CHANNEL_AMAZON, accountNumber, day, PublishRoleEnum.ADMIN);
        int publishingQueue =  templateQueueService.statisticsDayPublishingQueue(SaleChannel.CHANNEL_AMAZON, accountNumber, day, PublishRoleEnum.ADMIN, List.of(RecordStatus.PUBLISHING, RecordStatus.FAIL, RecordStatus.SUCCESS));

        statistics.setTimingQueueCount(generateQueue);
        statistics.setPublishCount(publishingQueue);

        // 刊登成功和刊登失败的模版
        int successTemplate = amazonTemplateService.statisticsDayTemplate(accountNumber, day, PublishRoleEnum.ADMIN, AmaoznPublishStatusEnum.PUBLISH_SUCCESS);
        int failTemplate = amazonTemplateService.statisticsDayTemplate(accountNumber, day, PublishRoleEnum.ADMIN, AmaoznPublishStatusEnum.PUBLISH_FAIL);
        statistics.setPublishSuccessCount(successTemplate);
        statistics.setPublishFailCount(failTemplate);

        if (statistics.getId() == null) {
            statistics.setCreateTime(new Timestamp(new Date().getTime()));
            statistics.setCreateBy("admin");
            systemTimingQueueStatisticsMapper.insert(statistics);
        } else {
            statistics.setUpdateTime(new Timestamp(new Date().getTime()));
            statistics.setUpdateBy("admin");
            systemTimingQueueStatisticsMapper.updateByPrimaryKeySelective(statistics);
        }
    }

    @Override
    public int deleteByExample(SystemTimingQueueStatisticsExample example) {
        if (example == null) {
            throw new RuntimeException("example 不能为null");
        }
        return systemTimingQueueStatisticsMapper.deleteByExample(example);
    }
}
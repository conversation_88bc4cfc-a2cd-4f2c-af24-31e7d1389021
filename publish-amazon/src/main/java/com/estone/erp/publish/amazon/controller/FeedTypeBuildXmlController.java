package com.estone.erp.publish.amazon.controller;

import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.util.SpringUtils;
import com.estone.erp.publish.amazon.bo.AmazonTemplateBO;
import com.estone.erp.publish.amazon.call.model.OperationType;
import com.estone.erp.publish.amazon.call.process.submit.PublishData;
import com.estone.erp.publish.amazon.call.process.submit.TemplateSubmitFeedXmlStrategy;
import com.estone.erp.publish.amazon.enums.AmaoznPublishStatusEnum;
import com.estone.erp.publish.amazon.enums.AmazonTemplateTableEnum;
import com.estone.erp.publish.amazon.model.AmazonTemplateExample;
import com.estone.erp.publish.amazon.model.request.FeedXmlRequest;
import com.estone.erp.publish.amazon.model.request.FeedXmlResponce;
import com.estone.erp.publish.amazon.service.AmazonTemplateService;
import com.estone.erp.publish.amazon.util.AmazonAccountUtil;
import com.estone.erp.publish.base.pms.model.AmazonAccount;
import com.estone.erp.publish.base.pms.service.AmazonAccountService;
import io.swagger.client.enums.SpFeedType;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@Slf4j
@RequestMapping("feedTypeXml")
public class FeedTypeBuildXmlController {

    private final static String ESTONE_SERVICE = "https://api.ezshopping.top";

    @Resource
    private AmazonTemplateService amazonTemplateService;
    @Resource
    private AmazonAccountService amazonAccountService;

    @PostMapping("/getFeedTypeXml")
    public ApiResult<?> testSku(@RequestBody FeedXmlRequest feedXmlRequest){
        String marketplaceId = feedXmlRequest.getMarketplaceId();
        String spu = feedXmlRequest.getSpu();
        if (StringUtils.isEmpty(marketplaceId) || StringUtils.isEmpty(spu)){
            return ApiResult.newError("请求参数 spu 和 marketplaceId 必传，请检查参数");
        }
        FeedXmlResponce feedXmlResponce = new FeedXmlResponce();
        try {
            String site = AmazonAccountUtil.findMarketplaceId2Site(marketplaceId,3);


            AmazonTemplateExample example = new AmazonTemplateExample();
            example.createCriteria().andParentSkuEqualTo(feedXmlRequest.getSpu())
                    .andCountryEqualTo(site)
                    .andPublishStatusEqualTo(AmaoznPublishStatusEnum.PUBLISH_SUCCESS.getStatusCode());
            example.setOrderByClause("last_update_date desc");
            example.setLimit(1);
            example.setTable(AmazonTemplateTableEnum.AMAZON_TEMPLATE.getCode());
            List<AmazonTemplateBO> amazonTemplateBOList =  amazonTemplateService.selectAmazonTemplateBOsByExampleWithBLOBs(example);
            if (CollectionUtils.isEmpty(amazonTemplateBOList)){
                return ApiResult.newError("未查询到刊登成功的模板");
            }
            AmazonAccount account = amazonAccountService.queryAmazonAccountByAccountNumber(amazonTemplateBOList.get(0).getSellerId());
            TemplateSubmitFeedXmlStrategy templateSubmitFeedXmlStrategy = SpringUtils.getBean(TemplateSubmitFeedXmlStrategy.class);
            PublishData<AmazonTemplateBO> unitPublishData = new PublishData<>();
            unitPublishData.setAccount(account);
            unitPublishData.setFeedType(SpFeedType.POST_PRODUCT_DATA.getValue());
            unitPublishData.setOperationType(OperationType.Update);
            unitPublishData.setUnitDatas(amazonTemplateBOList);

            String xml = templateSubmitFeedXmlStrategy.transferProduct2Xml(unitPublishData);
            Map<String, String> feedtypeAndXml = new HashMap<>();
            feedtypeAndXml.put("POST_PRODUCT_DATA", xml);
            feedXmlResponce.setFeedtypeAndXml(feedtypeAndXml);
        }catch (Exception e){
            return ApiResult.newError("请求出错；" + e.getMessage());
        }
        return ApiResult.newSuccess(feedXmlResponce);
    }
}

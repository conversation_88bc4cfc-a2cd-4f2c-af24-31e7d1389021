package com.estone.erp.publish.amazon.service;

import com.estone.erp.common.model.api.CQuery;
import com.estone.erp.common.model.api.CQueryResult;
import com.estone.erp.publish.amazon.model.AmazonBullpointTemp;
import com.estone.erp.publish.amazon.model.AmazonBullpointTempCriteria;
import com.estone.erp.publish.amazon.model.AmazonBullpointTempExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR> amazon_bullpoint_temp
 * 2024-01-29 11:38:55
 */
public interface AmazonBullpointTempService {
    int countByExample(AmazonBullpointTempExample example);

    CQueryResult<AmazonBullpointTemp> search(CQuery<AmazonBullpointTempCriteria> cquery);

    List<AmazonBullpointTemp> selectByExample(AmazonBullpointTempExample example);

    AmazonBullpointTemp selectByPrimaryKey(Integer id);

    int insert(AmazonBullpointTemp record);

    int updateByPrimaryKeySelective(AmazonBullpointTemp record);

    int updateByExampleSelective(AmazonBullpointTemp record, AmazonBullpointTempExample example);

    int deleteByPrimaryKey(List<Integer> ids);
}
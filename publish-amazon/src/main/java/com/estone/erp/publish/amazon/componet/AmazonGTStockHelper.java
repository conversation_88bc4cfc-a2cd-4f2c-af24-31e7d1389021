package com.estone.erp.publish.amazon.componet;

import com.estone.erp.common.elasticsearch.util.PageInfo;
import com.estone.erp.publish.amazon.bo.AmazonVariantBO;
import com.estone.erp.publish.amazon.call.process.submit.PublishAmazonProductProcesser;
import com.estone.erp.publish.amazon.enums.AmazonExceptionStatusEnum;
import com.estone.erp.publish.amazon.enums.TemplateInterfaceTypeEnums;
import com.estone.erp.publish.amazon.model.AmazonVariant;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.common.context.DataContextHolder;
import com.estone.erp.publish.common.executors.AmazonExecutors;
import com.estone.erp.publish.elasticsearch.model.EsAmazonProductListing;
import com.estone.erp.publish.elasticsearch.model.EsSkuBind;
import com.estone.erp.publish.elasticsearch.model.beanrequest.EsAmazonProductListingRequest;
import com.estone.erp.publish.elasticsearch.model.beanrequest.EsSaleAccountRequest;
import com.estone.erp.publish.elasticsearch.model.beanrequest.EsSkuBindRequest;
import com.estone.erp.publish.elasticsearch.service.EsAmazonProductListingService;
import com.estone.erp.publish.elasticsearch.service.EsSkuBindService;
import com.estone.erp.publish.elasticsearch.service.SaleAccountService;
import com.estone.erp.publish.platform.enums.SkuDataSourceEnum;
import com.estone.erp.publish.platform.util.Platform;
import com.estone.erp.publish.publishAmazon.model.AmazonProductListing;
import com.estone.erp.publish.publishAmazon.service.AmazonJSONListingFeedService;
import com.estone.erp.publish.system.param.model.SystemParam;
import com.estone.erp.publish.system.param.service.SystemParamService;
import com.estone.erp.publish.system.product.ProductInfo;
import com.estone.erp.publish.system.product.ProductUtils;
import com.estone.erp.publish.system.product.bean.gt.GtProductDetail;
import io.swagger.client.enums.SpFeedType;
import jodd.util.ThreadUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020/11/19 14:17
 * @description 冠通库存管理
 */
@Slf4j
@Component
public class AmazonGTStockHelper {

    @Resource
    private EsSkuBindService esSkuBindService;
    @Resource
    private SystemParamService systemParamService;
    @Resource
    private EsAmazonProductListingService esAmazonProductListingService;
    @Resource
    private SaleAccountService saleAccountService;
    @Resource
    private AmazonJSONListingFeedService amazonJSONListingFeedService;

    public void handStock(List<String> sellerSkuList, Integer interfaceType) {
        /*
        1-库存状态为5个时，将对应listing库存状态都调整为0，5做成配置的方式。
        2-库存大于等于5时，把在线listing调回冠通实时库存。
        一天执行2次，中午12点，凌晨0点。
         */
        SystemParam system = systemParamService.querySystemParamByCodeKey("AMAZON.AMAZON_GT_STOCK_CONF");
        if(system == null ){
            log.error("亚马逊冠通库存配置未配置，请先到系统参数配置，结束执行冠通库存更新！");
            return;
        }

        List<String> removeAccountList = new ArrayList<>();

        //过滤掉 TRO 账号
        EsSaleAccountRequest request = new EsSaleAccountRequest();
        request.setSaleChannel(SaleChannel.CHANNEL_AMAZON);
        request.setExceptionStatus(AmazonExceptionStatusEnum.TRO.getCode());
        List<String> gbcList = saleAccountService.getAccountList(request);
        if (CollectionUtils.isNotEmpty(gbcList)){
            removeAccountList.addAll(gbcList);
        }

        //过滤掉 TRO 账号
        EsSaleAccountRequest requestSubscibeMsgStatus = new EsSaleAccountRequest();
        requestSubscibeMsgStatus.setSaleChannel(SaleChannel.CHANNEL_AMAZON);
        requestSubscibeMsgStatus.setSubscibeMsgStatus(true);
        List<String> subscibeMsgStatusList = saleAccountService.getAccountList(request);
        if (CollectionUtils.isNotEmpty(subscibeMsgStatusList)){
            removeAccountList.addAll(subscibeMsgStatusList);
        }
        int handConfStock = Integer.parseInt(system.getParamValue());

        //先查询sku 绑定表
        int page = 0, limit = 100;

        //货号_站点 --> 库存
        Map<String, Integer> skuSite2Stock = new HashMap<>();
        //账号 --> 站点
        Map<String, String> account2Site = new HashMap<>();
        //改库存标识
        List<String> feedTypes = new ArrayList<>(1);
        feedTypes.add(SpFeedType.POST_INVENTORY_AVAILABILITY_DATA.getValue());

        int execSize = 0;
        do {
            EsSkuBindRequest skuBindRequest = new EsSkuBindRequest();
            skuBindRequest.setPlatform(Platform.Amazon.name());
            skuBindRequest.setSkuDataSource(SkuDataSourceEnum.GUAN_TONG_SYSTEM.getCode());
            if(CollectionUtils.isNotEmpty(sellerSkuList)){
                skuBindRequest.setBindSkus(sellerSkuList);
            }
            skuBindRequest.setPageIndex(page++);
            skuBindRequest.setPageSize(limit);
            skuBindRequest.setOrderBy("id");
            skuBindRequest.setSequence("ASC");
            PageInfo<EsSkuBind> pageInfo = esSkuBindService.pageInfo(skuBindRequest);
            if(pageInfo == null || CollectionUtils.isEmpty(pageInfo.getContents())){
                //数据不存在了，跳出循环
                break;
            }

            //根据listing查询在线列表数据
            List<String> listingList = pageInfo.getContents().stream().map(o -> o.getBindSku()).collect(Collectors.toList());
            /*AmazonVariantExample example = new AmazonVariantExample();
            example.createCriteria().andSellerSkuIn(listingList).andIsOnlineEqualTo(true);
            List<AmazonVariant> itemList = amazonVariantService.selectByExample(example);*/
            EsAmazonProductListingRequest esAmazonProductListingRequest = new EsAmazonProductListingRequest();
            esAmazonProductListingRequest.setIsOnline(true);
            esAmazonProductListingRequest.setSellerSkuList(listingList);
            List<EsAmazonProductListing> esAmazonProductListingList = esAmazonProductListingService.getEsAmazonProductListing(esAmazonProductListingRequest);
            //过滤掉TRO账号和实验账号
            if(CollectionUtils.isNotEmpty(removeAccountList) && CollectionUtils.isNotEmpty(esAmazonProductListingList)){
                esAmazonProductListingList = esAmazonProductListingList.stream()
                        .filter(o -> !removeAccountList.contains(o.getAccountNumber())).collect(Collectors.toList());
            }
            if(CollectionUtils.isEmpty(esAmazonProductListingList)){
                continue;
            }
            List<AmazonVariant> itemList = new ArrayList<>(esAmazonProductListingList.size());
            for (EsAmazonProductListing esAmazonProductListing : esAmazonProductListingList){
                AmazonVariant amazonVariant = new AmazonVariant();
                amazonVariant.setAccountNumber(esAmazonProductListing.getAccountNumber());
                amazonVariant.setArticleNumber(esAmazonProductListing.getArticleNumber());
                amazonVariant.setSellerSku(esAmazonProductListing.getSellerSku());
                amazonVariant.setCountry(esAmazonProductListing.getSite());
                amazonVariant.setQuantity(esAmazonProductListing.getQuantity());
                itemList.add(amazonVariant);
            }

            try {
                //清空缓存
                skuSite2Stock.clear();

                //执行数据更新
                int size = execData(handConfStock, skuSite2Stock, account2Site, feedTypes, itemList, interfaceType);
                execSize += size;

                //执行中的数量大于 600，睡眠5分钟
                if(execSize > 600){
                    execSize = 0;
                    ThreadUtil.sleep((long)5 * 60 * 1000);
                }

            }catch (Exception e){
                log.error("执行库存更新出错：", e);
            }
        }while (true);
    }

    /**
     * @param handConfStock 系统配置库存值
     * @param skuSite2Stock 货号_站点 --> 库存
     * @param account2Site  账号 --> 站点
     * @param feedTypes     更新类型
     * @param itemList      listing
     * @param interfaceType
     */
    private int execData(int handConfStock, Map<String, Integer> skuSite2Stock, Map<String, String> account2Site, List<String> feedTypes, List<AmazonVariant> itemList, Integer interfaceType) {
        //查询产品sku 库存信息
        List<String> articleNumberList = itemList.stream().map(o -> o.getArticleNumber()).distinct().collect(Collectors.toList());
        findSkuProductInfo(articleNumberList, skuSite2Stock);

        //过滤出要修改的listing
        Iterator<AmazonVariant> iterator = itemList.iterator();
        while (iterator.hasNext()){
            AmazonVariant item = iterator.next();
            if(!account2Site.containsKey(item.getAccountNumber())){
                //获取账号信息
                String site = item.getCountry();
                if(StringUtils.isNotBlank(site)){
                    account2Site.put(item.getAccountNumber(), site);
                }
            }

            String site = account2Site.get(item.getAccountNumber());
            if(StringUtils.isBlank(site)){
                iterator.remove();
                log.info(String.format("账号：%s，sellerSku：%s，无法确定站点，不执行!", item.getAccountNumber(), item.getSellerSku()));
                continue;
            }

            //sku站点库存
            Integer qty = skuSite2Stock.get(String.format("%s_%s", item.getArticleNumber(), site));
            if(qty == null || qty < 0){
                iterator.remove();
                log.info(String.format("sellerSku：%s, 货号：%s，库存：%s，不合法不执行!", item.getSellerSku(), item.getArticleNumber(), qty));
                continue;
            }

            //如果要更新的库存与 listing库存一样，不更新
            if(qty.equals(item.getQuantity())){
                iterator.remove();
                continue;
            }

            //库存规则
            if(qty < handConfStock){
                //小于配置库存 库存改0
                item.setQuantity(0);
            }else{
                item.setQuantity(qty);
            }
        }

        if (TemplateInterfaceTypeEnums.XSD.isTrue(interfaceType)) {
            //按账号分组
            Map<String, List<AmazonVariantBO>> accountSkuMap = itemList.stream()
                    .map(o -> {
                        AmazonVariantBO bo = new AmazonVariantBO();
                        BeanUtils.copyProperties(o, bo);
                        return bo;
                    })
                    .collect(Collectors.groupingBy(AmazonVariant::getAccountNumber));
            for (Map.Entry<String, List<AmazonVariantBO>> entry : accountSkuMap.entrySet()) {
                AmazonExecutors.executeUnqualifiedSku(() -> {
                    try {
                        //因为在回调之前会取出当前用户，可能为空或是其他设置用户，所以这里单独重新设置一下admin
                        DataContextHolder.setUsername("admin");

                        PublishAmazonProductProcesser processer = new PublishAmazonProductProcesser(entry.getKey());
                        processer.batchPublish(entry.getValue(), feedTypes);

                    } catch (Exception e) {
                        log.error("冠通改库存执行异常", e);
                    } finally {
                        DataContextHolder.setUsername(null);
                    }
                });
            }
        }

        if (TemplateInterfaceTypeEnums.JSON.isTrue(interfaceType)) {
            List<AmazonProductListing> itemListFixed = itemList.stream().map(item -> {
                AmazonProductListing listing = new AmazonProductListing();
                listing.setAccountNumber(item.getAccountNumber());
                listing.setArticleNumber(item.getArticleNumber());
                listing.setSellerSku(item.getSellerSku());
                listing.setSite(item.getCountry());
                listing.setQuantity(item.getQuantity());
                return listing;
            }).collect(Collectors.toList());
            amazonJSONListingFeedService.batchUpdateInventory(itemListFixed);
        }
        return itemList.size();
    }


    private void findSkuProductInfo(List<String> articleNumberList, Map<String, Integer> skuSite2Stock) {
        List<ProductInfo> productInfoList = null;
        int tryNum = 3;
        do {
            try {
                productInfoList = ProductUtils.findProductInfos(articleNumberList);
                break;
            }catch (Exception e){
                ThreadUtil.sleep(2000);
                log.error("请求产品信息出错：", e);
            }
        }while (--tryNum > 0);

        if(CollectionUtils.isNotEmpty(productInfoList)){
            for (ProductInfo prod : productInfoList) {
                GtProductDetail other = prod.getOther();
                if(other != null){
                    Map<String, Integer> site2Stock = other.getSite2Stock();
                    if(site2Stock != null && site2Stock.size() > 0){
                        site2Stock.forEach((size, qty) ->{
                            //货号_站点 --> 库存
                            skuSite2Stock.put(String.format("%s_%s", other.getSku(), size), qty);
                        });
                    }
                }
            }
        }
    }

}

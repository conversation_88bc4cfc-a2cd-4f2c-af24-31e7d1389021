package com.estone.erp.publish.amazon.service;

import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.model.api.CQuery;
import com.estone.erp.common.model.api.CQueryResult;
import com.estone.erp.publish.amazon.model.CategoryOperationsPublishConfig;
import com.estone.erp.publish.amazon.model.CategoryOperationsPublishConfigCriteria;
import com.estone.erp.publish.amazon.model.CategoryOperationsPublishConfigExample;
import com.estone.erp.publish.amazon.model.CategoryOperationsTeamConfig;
import com.estone.erp.publish.amazon.model.dto.CatOperationPublishConfigDO;
import com.estone.erp.publish.amazon.model.dto.CatOperationPublishConfigVO;
import com.estone.erp.publish.amazon.model.request.SaleUserCatOperationRequest;

import java.util.List;

/**
 * <AUTHOR>
 * 2024-03-21 15:20:41
 */
public interface CategoryOperationsPublishConfigService {
    int countByExample(CategoryOperationsPublishConfigExample example);

    List<String> queryAuthPermission();

    CQueryResult<CatOperationPublishConfigVO> search(CQuery<CategoryOperationsPublishConfigCriteria> cquery);

    List<CategoryOperationsPublishConfig> selectByExample(CategoryOperationsPublishConfigExample example);

    List<CatOperationPublishConfigVO> pageQuery(CategoryOperationsPublishConfigExample example, Boolean status);

    CategoryOperationsPublishConfig selectByPrimaryKey(Integer id);

    int insert(CategoryOperationsPublishConfig record);

    int updateByPrimaryKeySelective(CategoryOperationsPublishConfig record);

    int updateByExampleSelective(CategoryOperationsPublishConfig record, CategoryOperationsPublishConfigExample example);

    int deleteByPrimaryKey(List<Integer> ids);

    void mappingPublishConfigByCategoryConfig(List<CategoryOperationsTeamConfig> categoryConfigs);

    ApiResult<String> updateData(List<CatOperationPublishConfigDO> updateParam);

    CatOperationPublishConfigVO getSalePublishConfig(String saleId, String categoryFullPathCode);

    ApiResult<String> batchUpdateData(CatOperationPublishConfigDO updateParam);

    CatOperationPublishConfigVO getRootPublishConfig(String fullPathCode);

    ApiResult<String> batchUpdateSaleData(SaleUserCatOperationRequest request);
}
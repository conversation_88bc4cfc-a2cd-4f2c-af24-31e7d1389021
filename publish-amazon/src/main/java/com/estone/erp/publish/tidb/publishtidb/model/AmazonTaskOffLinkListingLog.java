package com.estone.erp.publish.tidb.publishtidb.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * amazon任务下架链接记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-07
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("amazon_task_off_link_listing_log")
public class AmazonTaskOffLinkListingLog implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 店铺
     */
    private String accountNumber;

    /**
     * 站点
     */
    private String site;

    /**
     * 状态 0 待下架、1 可下架、2 已下架、3、下架失败
     */
    private Integer status;

    /**
     * asin
     */
    private String asin;

    /**
     * 货号
     */
    private String articleNumber;

    /**
     * sellerSku
     */
    private String sellerSku;

    /**
     * 总销量
     */
    private Integer salesTotalCount;

    /**
     * 最新上架时间
     */
    private LocalDateTime openTime;

    /**
     * 统计日期
     */
    private LocalDateTime statisticsDate;

    /**
     * 创建时间
     */
    private LocalDateTime createdTime;

    /**
     * 修改时间
     */
    private LocalDateTime updatedTime;

    /**
     * 任务类型
     */
    private Integer taskType;

    /**
     * 父asin
     */
    private String parentAsin;

    /**
     * 扩展数据
     */
    private String extraData;


}

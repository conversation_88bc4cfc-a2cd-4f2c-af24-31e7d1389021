package com.estone.erp.publish.amazon.jobHandler;

import com.estone.erp.common.constant.StrConstant;
import com.estone.erp.common.jobHandler.AbstractJobHandler;
import com.estone.erp.common.util.PagingUtils;
import com.estone.erp.publish.amazon.bo.AmazonVariantBO;
import com.estone.erp.publish.amazon.call.process.submit.PublishAmazonProductProcesser;
import com.estone.erp.publish.amazon.util.AmazonSpLocalUtils;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.common.context.DataContextHolder;
import com.estone.erp.publish.common.executors.AmazonExecutors;
import com.estone.erp.publish.elasticsearch.model.EsAmazonProductListing;
import com.estone.erp.publish.elasticsearch.model.beanrequest.EsAmazonProductListingRequest;
import com.estone.erp.publish.elasticsearch.service.EsAmazonProductListingService;
import com.estone.erp.publish.platform.service.PmsSkuService;
import com.estone.erp.publish.system.account.AccountUtils;
import com.estone.erp.publish.system.account.SaleAccountAndBusinessResponse;
import com.estone.erp.publish.system.product.util.SkuStockUtils;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import io.swagger.client.AmazonSpAccount;
import io.swagger.client.enums.SpFeedType;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * Amazon恢复附件SKU在线列表库存为系统库存 一次性任务
 * <AUTHOR>
 * @date 2022/4/20 15:22
 */
@Slf4j
@Component
public class AmazonUpdateInventoryBySkuJobHandler extends AbstractJobHandler {

    private String [] fields = {"accountNumber","site","sonAsin","sellerSku","articleNumber","quantity"};

    @Resource
    private PmsSkuService pmsSkuService;

    @Resource
    private EsAmazonProductListingService esAmazonProductListingService;

    public AmazonUpdateInventoryBySkuJobHandler() {
        super(AmazonUpdateInventoryBySkuJobHandler.class.getName());
    }

    @Override
    @XxlJob("AmazonUpdateInventoryBySkuJobHandler")
    public ReturnT<String> run(String param) throws Exception {
        XxlJobLogger.log("恢复附件SKU在线列表库存为系统库存 START");

        int offset = 0;
        int limit = 500;
        while (true) {
            List<String> skuList = pmsSkuService.selectTemporarySku(limit, offset);
            if (CollectionUtils.isEmpty(skuList)) {
                break;
            }

            for (String sku : skuList) {
                AmazonExecutors.executeUpdateInventory(()-> {
                    try {
                        executeUpdateInventory(sku);
                    } catch (Exception e) {
                        log.error(e.getMessage());
                    }
                });
            }

            offset += limit;
        }

        XxlJobLogger.log("恢复附件SKU在线列表库存为系统库存 END");
        return ReturnT.SUCCESS;
    }

    private void executeUpdateInventory(String sku) {
        // 根据sku查询在线列表数据
        EsAmazonProductListingRequest esAmazonProductListingRequest = new EsAmazonProductListingRequest();
        esAmazonProductListingRequest.setArticleNumber(sku);
        esAmazonProductListingRequest.setIsOnline(true);
        esAmazonProductListingRequest.setFields(fields);
        List<EsAmazonProductListing> esAmazonProductListingList = esAmazonProductListingService.getEsAmazonProductListing(esAmazonProductListingRequest);
        if (CollectionUtils.isEmpty(esAmazonProductListingList)) {
            return;
        }

        List<AmazonVariantBO> amazonVariantList = new ArrayList<>();

        for (EsAmazonProductListing esAmazonProductListing : esAmazonProductListingList) {
            try {
                SaleAccountAndBusinessResponse account = AccountUtils.getSaleAccountByAccountNumber(SaleChannel.CHANNEL_AMAZON, esAmazonProductListing.getAccountNumber(), true);
                AmazonSpAccount amazonSpAccount = AmazonSpLocalUtils.ToAmazonSpAccount(account);
                if ((BooleanUtils.isTrue(account.getColBool1()))
                        ||AmazonSpLocalUtils.checkAmazonSpAccountMsgError(amazonSpAccount)) {
                    XxlJobLogger.log("未授权到sp-api,账号信息不全,请检查 marketplaceId，appName 该 accountNumber:" + esAmazonProductListing.getAccountNumber());
                    continue;
                }

                // 查询sku系统库存
                /*String systemStock = PublishRedisClusterUtils.get(RedisKeyConstant.PRODUCT_SKU_SAVE_AVALIBLE_STOCK + esAmazonProductListing.getArticleNumber().toUpperCase());
                if (StringUtils.isBlank(systemStock)) {
                    continue;
                }

                // 如果当前库存等于系统库存 不更新
                int updateStock = Double.valueOf(systemStock).intValue() < 0 ? 0 : Double.valueOf(systemStock).intValue();*/
                Integer updateStock = SkuStockUtils.getSkuSystemStock(esAmazonProductListing.getArticleNumber());
                if (null == updateStock || updateStock == esAmazonProductListing.getQuantity()) {
                    continue;
                }

                AmazonVariantBO amazonVariantBO = new AmazonVariantBO();
                amazonVariantBO.setAccountNumber(esAmazonProductListing.getAccountNumber());
                amazonVariantBO.setArticleNumber(esAmazonProductListing.getArticleNumber());
                amazonVariantBO.setSellerSku(esAmazonProductListing.getSellerSku());
                amazonVariantBO.setQuantity(updateStock);
                amazonVariantBO.setAsin(esAmazonProductListing.getSonAsin());
                amazonVariantBO.setCountry(esAmazonProductListing.getSite());
                amazonVariantBO.setPreviousQuantityValue(String.valueOf(esAmazonProductListing.getQuantity()));
                amazonVariantBO.setAfterQuantityValue(String.valueOf(updateStock));
                amazonVariantList.add(amazonVariantBO);
            } catch (Exception e) {
                XxlJobLogger.log(String.format("Sku%s报错：%s", esAmazonProductListing.getArticleNumber(), e.getMessage()));
                log.error(String.format("Sku%s报错：%s", esAmazonProductListing.getArticleNumber(), e.getMessage()));
            }
        }

        if (CollectionUtils.isEmpty(amazonVariantList)) {
            return;
        }

        List<String> feedTypes = new ArrayList<>(1);
        feedTypes.add(SpFeedType.POST_INVENTORY_AVAILABILITY_DATA.getValue());

        List<List<AmazonVariantBO>> lists = PagingUtils.pagingList(amazonVariantList, 1);
        for (List<AmazonVariantBO> variantList : lists) {
            try {
                DataContextHolder.setUsername(StrConstant.ADMIN);
                PublishAmazonProductProcesser processer = new PublishAmazonProductProcesser(variantList.get(0).getAccountNumber());
                processer.batchPublish(variantList, feedTypes);
            } catch (Exception e) {
                XxlJobLogger.log(String.format("店铺%s SKU%s 修改库存报错：%s", variantList.get(0).getAccountNumber(), sku, e.getMessage()));
                log.error(String.format("店铺%s SKU%s 修改库存报错：%s", variantList.get(0).getAccountNumber(), sku, e.getMessage()));
            } finally {
                DataContextHolder.setUsername(null);
            }
        }

        XxlJobLogger.log(String.format("SKU%s 修改库存完成", sku));
    }
}

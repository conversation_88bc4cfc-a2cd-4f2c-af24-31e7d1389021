package com.estone.erp.publish.amazon.model;

import com.estone.erp.common.annotation.CheckColumn;
import lombok.Data;

import java.io.Serializable;
import java.sql.Timestamp;

@Data
public class AmazonFormTypeProductConf implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     *  database column amazon_form_type_product_conf.id
     */
    private Integer id;

    /**
     * 站点 database column amazon_form_type_product_conf.site
     */
    @CheckColumn(columnName = "站点", maxLength = 2)
    private String site;

    /**
     * 美国英文分类路径 database column amazon_form_type_product_conf.type_path_en
     */
    @CheckColumn(columnName = "英文分类路径", maxLength = 255)
    private String typePathEn;

    /**
     * productType database column amazon_form_type_product_conf.product_type
     */
    @CheckColumn(columnName = "ProductType", maxLength = 150)
    private String productType;

    /**
     * 是否启用（1：启用，0：禁用） database column amazon_form_type_product_conf.enable
     */
    private Boolean enable;

    /**
     * 创建人 database column amazon_form_type_product_conf.create_by
     */
    private String createBy;

    /**
     *  database column amazon_form_type_product_conf.create_time
     */
    private Timestamp createTime;

    /**
     *  database column amazon_form_type_product_conf.update_by
     */
    private String updateBy;

    /**
     *  database column amazon_form_type_product_conf.update_time
     */
    private Timestamp updateTime;
}
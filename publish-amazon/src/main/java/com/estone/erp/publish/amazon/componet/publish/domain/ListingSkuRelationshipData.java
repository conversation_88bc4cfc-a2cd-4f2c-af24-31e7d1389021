package com.estone.erp.publish.amazon.componet.publish.domain;

import com.estone.erp.publish.amazon.componet.publish.util.AmazonListingApiUtil;
import com.google.common.collect.Maps;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.util.Pair;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 变体关系
 *
 * <AUTHOR>
 * @date 2024-11-22 14:32
 */
@Getter
public class ListingSkuRelationshipData {
    private final static String child = "child";
    private final static String parent = "parent";
    private final static String variation = "variation";
    private final static String parent_sku = "parent_sku";
    private final static String variation_theme = "variation_theme";
    private final static String parentage_level = "parentage_level";
    private final static String child_relationship_type = "child_relationship_type";
    private final static String child_parent_sku_relationship = "child_parent_sku_relationship";

    private Map<String, Object> data;


    private ListingSkuRelationshipData(Map<String, Object> data) {
        this.data = data;
    }

    private ListingSkuRelationshipData() {
    }


    /**
     * 创建变体关系数据
     * {
     * "parentage_level": [
     * {
     * "marketplace_id": "ATVPDKIKX0DER",
     * "value": "child"
     * }
     * ],
     * "child_parent_sku_relationship": [
     * {
     * "marketplace_id": "ATVPDKIKX0DER",
     * "child_relationship_type": "variation",
     * "value": "11JJ603878_X3UliNany",
     * "parent_sku": "11JJ603878_X3UliNany"
     * }
     * ]
     * }
     *
     * @param parentSku 父SKU
     * @return 变体关系数据
     */
    public static ListingSkuRelationshipData of(String parentSku) {
        Map<String, Object> dataMap = new HashMap<>();

        Map<String, Object> parentageLevel = AmazonListingApiUtil.buildNestedArrayStructure(parentage_level,
                Pair.of(ListingFiledConstants.VALUE, child)
        );

        Map<String, Object> relationship = AmazonListingApiUtil.buildNestedArrayStructure(child_parent_sku_relationship,
                Pair.of(parent_sku, parentSku),
                Pair.of(child_relationship_type, variation)
        );
        AmazonListingApiUtil.putProperty(dataMap, parentageLevel);
        AmazonListingApiUtil.putProperty(dataMap, relationship);
        return new ListingSkuRelationshipData(dataMap);
    }

    /**
     * 创建变体关系数据
     * {
     * "parentage_level": [
     * {
     * "marketplaceId": "ATVPDKIKX0DER",
     * "value": "parent"
     * }
     * ],
     * "child_parent_sku_relationship": [
     * {
     * "child_relationship_type": "variation",
     * "marketplace_id": "ATVPDKIKX0DER",
     * "parent_sku": null
     * }
     * ],
     * "variation_theme": [
     * {
     * "name": "COLOR"
     * }
     * ],
     * "color": [
     * {
     * "language_tag": "en_US",
     * "marketplace_id": "ATVPDKIKX0DER",
     * "value": "Red"
     * }
     * ]
     * }
     *
     * @param theme 变体主题
     * @return 变体关系数据
     */
    public static ListingSkuRelationshipData ofParent(String theme, Map<String, Object> variantAttribute) {
        Map<String, Object> dataMap = new HashMap<>();

        Map<String, Object> parentageLevel = AmazonListingApiUtil.buildNestedArrayStructure(parentage_level,
                Pair.of(ListingFiledConstants.VALUE, parent)
        );

        List<Map<String, Object>> relationshipList = new ArrayList<>();
        Map<String, Object> relationshipMap = Maps.newHashMap();
        relationshipMap.put(child_relationship_type, variation);
        relationshipMap.put(parent_sku, "null");
        relationshipList.add(relationshipMap);

        dataMap.put(child_parent_sku_relationship, relationshipList);

        Map<String, Object> variationTheme = AmazonListingApiUtil.buildNestedArrayStructure(variation_theme,
                Pair.of(ListingFiledConstants.NAME, StringUtils.upperCase(theme))
        );
        AmazonListingApiUtil.putProperty(dataMap, parentageLevel);
        AmazonListingApiUtil.putProperty(dataMap, variantAttribute);
        AmazonListingApiUtil.putProperty(dataMap, variationTheme);
        return new ListingSkuRelationshipData(dataMap);
    }
}

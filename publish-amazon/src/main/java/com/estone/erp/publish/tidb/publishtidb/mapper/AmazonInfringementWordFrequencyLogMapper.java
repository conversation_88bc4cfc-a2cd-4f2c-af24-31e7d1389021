package com.estone.erp.publish.tidb.publishtidb.mapper;

import java.util.List;
import java.util.Map;

import com.estone.erp.publish.tidb.publishtidb.model.AmazonInfringementWordFrequencyLog;
import com.estone.erp.publish.tidb.publishtidb.model.AmazonInfringementWordFrequencyLogExample;
import org.apache.ibatis.annotations.Param;

public interface AmazonInfringementWordFrequencyLogMapper {
    int countByExample(AmazonInfringementWordFrequencyLogExample example);

    int deleteByPrimaryKey(List<Long> ids);

    int insert(AmazonInfringementWordFrequencyLog record);

    AmazonInfringementWordFrequencyLog selectByPrimaryKey(Long id);

    List<AmazonInfringementWordFrequencyLog> selectByExample(AmazonInfringementWordFrequencyLogExample example);

    int updateByExampleSelective(@Param("record") AmazonInfringementWordFrequencyLog record, @Param("example") AmazonInfringementWordFrequencyLogExample example);

    int updateByPrimaryKeySelective(AmazonInfringementWordFrequencyLog record);

    void batchInsert(@Param("list") List<AmazonInfringementWordFrequencyLog> list);

    List<Map<Object, Object>> getPageMetaListByExample(AmazonInfringementWordFrequencyLogExample example);
}
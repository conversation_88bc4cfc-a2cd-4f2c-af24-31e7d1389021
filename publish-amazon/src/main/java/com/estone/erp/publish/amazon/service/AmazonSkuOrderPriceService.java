package com.estone.erp.publish.amazon.service;

import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.model.api.CQuery;
import com.estone.erp.common.model.api.CQueryResult;
import com.estone.erp.publish.amazon.model.AmazonSkuOrderPrice;
import com.estone.erp.publish.amazon.model.AmazonSkuOrderPriceCriteria;
import com.estone.erp.publish.amazon.model.AmazonSkuOrderPriceExample;
import com.estone.erp.publish.amazon.model.request.SaleOrderUpdatePriceRequest;

import java.util.List;

/**
 * <AUTHOR> amazon_sku_order_price
 * 2024-03-15 11:49:28
 */
public interface AmazonSkuOrderPriceService {
    int countByExample(AmazonSkuOrderPriceExample example);

    CQueryResult<AmazonSkuOrderPrice> search(CQuery<AmazonSkuOrderPriceCriteria> cquery);

    List<AmazonSkuOrderPrice> selectByExample(AmazonSkuOrderPriceExample example);

    AmazonSkuOrderPrice selectByPrimaryKey(Integer id);

    int insert(AmazonSkuOrderPrice record);

    int updateByPrimaryKeySelective(AmazonSkuOrderPrice record);

    int updateByExampleSelective(AmazonSkuOrderPrice record, AmazonSkuOrderPriceExample example);

    int deleteByPrimaryKey(List<Integer> ids);

    void batchInsert(List<AmazonSkuOrderPrice> partList);

    void batchUpdate(List<AmazonSkuOrderPrice> partList);

    ApiResult<String> updateSaleOrderUpdatePrice(SaleOrderUpdatePriceRequest request);

    List<String> loadDistinctArticleNumber(int offset, int limit);
}
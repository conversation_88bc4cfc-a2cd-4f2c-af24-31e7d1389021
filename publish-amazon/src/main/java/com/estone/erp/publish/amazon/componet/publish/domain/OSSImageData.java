package com.estone.erp.publish.amazon.componet.publish.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * Amazon 上传图片到 OSSImageData
 *
 * <AUTHOR>
 * @date 2024-11-21 17:08
 */
@Data
public class OSSImageData {
    private String ossUrl;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime expireTime;

    public OSSImageData(String ossUrl, LocalDateTime expireTime) {
        this.ossUrl = ossUrl;
        this.expireTime = expireTime;
    }
    

}

package com.estone.erp.publish.amazon.jobHandler.offline;

import com.alibaba.fastjson.JSON;
import com.estone.erp.common.jobHandler.AbstractJobHandler;
import com.estone.erp.publish.amazon.enums.AmazonListingitemtypeEnum;
import com.estone.erp.publish.amazon.enums.AmazonOfflineEnums;
import com.estone.erp.publish.amazon.model.*;
import com.estone.erp.publish.amazon.model.dto.DeleteAmazonListingDto;
import com.estone.erp.publish.amazon.service.AmazonAccountRelationService;
import com.estone.erp.publish.amazon.service.OverLimitAccountOffLinkLogService;
import com.estone.erp.publish.amazon.service.OverLimitNoSalesSkuService;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.common.executors.AmazonExecutors;
import com.estone.erp.publish.elasticsearch.model.EsAmazonProductListing;
import com.estone.erp.publish.elasticsearch.model.SaleAccount;
import com.estone.erp.publish.elasticsearch.model.beanrequest.EsSaleAccountRequest;
import com.estone.erp.publish.elasticsearch.model.beanresponse.AmazonAsinSaleCountDO;
import com.estone.erp.publish.elasticsearch.service.SaleAccountService;
import com.estone.erp.publish.elasticsearch.service.impl.EsAmazonProductListingServiceImpl;
import com.estone.erp.publish.platform.enums.SkuDataSourceEnum;
import com.estone.erp.publish.system.order.OrderUtils;
import com.google.common.collect.Lists;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.StopWatch;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.springframework.data.elasticsearch.core.ElasticsearchRestTemplate;
import org.springframework.data.elasticsearch.core.SearchHitSupport;
import org.springframework.data.elasticsearch.core.SearchScrollHits;
import org.springframework.data.elasticsearch.core.query.NativeSearchQuery;
import org.springframework.data.elasticsearch.core.query.NativeSearchQueryBuilder;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.Semaphore;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.BiConsumer;
import java.util.function.Predicate;
import java.util.stream.Collectors;

/**
 * 指定SPU下架
 * <AUTHOR>
 * @date 2024-05-25 下午2:52
 */
@Slf4j
@Component
public class AmazonSpecifiedSpuOfflineJobHandler extends AbstractJobHandler {
    private static final int MAX_CONCURRENT_THREADS = 60; // 可以根据需要调整并发线程数
    private static final Semaphore SKU_SEMAPHORE = new Semaphore(MAX_CONCURRENT_THREADS);
    private static final List<Predicate<AmazonAsinSaleCountDO>> ASIN_SALE_COUNT_COMPARE_RULES;

    static {
        ASIN_SALE_COUNT_COMPARE_RULES = List.of(
                // 总销量为0
                asinSaleCountDO -> {
                    Integer saleTotalCount = asinSaleCountDO.getSale_total_count();
                    return saleTotalCount == null || saleTotalCount == 0;
                }
        );
    }


    public AmazonSpecifiedSpuOfflineJobHandler() {
        super("AmazonSpecifiedSpuOfflineJobHandler");
    }

    @Resource
    private AmazonAccountRelationService amazonAccountRelationService;
    @Resource
    private OverLimitAccountOffLinkLogService offLinkLogService;
    @Resource
    private SaleAccountService saleAccountService;
    @Resource
    private ElasticsearchRestTemplate elasticsearchRestTemplate1;
    @Resource
    private OverLimitNoSalesSkuService noSalesSkuService;

    @Data
    private static class InnerParam {
        private String type;
        private List<String> sites;
        private List<String> accountNumbers;
        private String statisticsDate;
        private Double limitRatio;
        private Integer startId;
        private Integer endId;
    }




    @Override
    @XxlJob("AmazonSpecifiedSpuOfflineJobHandler")
    public ReturnT<String> run(String param) throws Exception {
        InnerParam innerParam = passParam(param, InnerParam.class);
        if (innerParam == null) {
            return ReturnT.FAIL;
        }

        // 统计侵权词无销量链接
        if ("statistics".equals(innerParam.type)) {
            try {
                statisticsExecute(innerParam);
            } catch (Exception e) {
                log.error("统计异常:{}", e.getMessage(), e);
                XxlJobLogger.log("统计异常:{}", e.getMessage());
            }
            return ReturnT.SUCCESS;
        }


        // 统计侵权词无销量链接
        if ("statistics_acc".equals(innerParam.type)) {
            try {
                statisticsAccountExecute(innerParam);
            } catch (Exception e) {
                log.error("统计异常:{}", e.getMessage(), e);
                XxlJobLogger.log("统计异常:{}", e.getMessage());
            }
            return ReturnT.SUCCESS;
        }

        // 下架
        if ("offline".equals(innerParam.type)) {
            try {
                offlineExecute(innerParam);
                while (true) {
                    if (SKU_SEMAPHORE.availablePermits() == MAX_CONCURRENT_THREADS) {
                        XxlJobLogger.log("指定spu下[{}]无销量的listing链接,下架完成", innerParam.getStatisticsDate());
                        break;
                    }
                    try {
                        TimeUnit.MILLISECONDS.sleep(2);
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                    }
                }
            } catch (Exception e) {
                log.error("下架异常:{}", e.getMessage(), e);
                XxlJobLogger.log("下架异常:{}", e.getMessage());
            }
        }
        return ReturnT.SUCCESS;
    }

    private void statisticsAccountExecute(InnerParam innerParam) {
        LocalDate statisticalDate;
        if (StringUtils.isNotBlank(innerParam.getStatisticsDate())) {
            statisticalDate = LocalDate.parse(innerParam.getStatisticsDate());
        } else {
            statisticalDate = LocalDate.now();
        }

        List<AmazonAccountRelation> amazonAccountRelationList = getAmazonAccount(innerParam.getSites(), innerParam.getAccountNumbers());
        // 2.按店铺处理可下架数据
        statisticsOffLinkByAccountNumber(statisticalDate, amazonAccountRelationList, innerParam.getLimitRatio());

    }

    private void offlineExecute(InnerParam innerParam) {
        if (StringUtils.isBlank(innerParam.getStatisticsDate())) {
            XxlJobLogger.log("执行下架统计日期为空不处理");
            return;
        }

        DeleteAmazonListingDto deleteAmazonListingDto = new DeleteAmazonListingDto();
        deleteAmazonListingDto.setAmazonOfflineEnumType(AmazonOfflineEnums.Type.SPECIFIED_SPU_DELETE);
        deleteAmazonListingDto.setUserName("admin");

        // 查询亚马逊所有账号 按照merchantId 分组
        EsSaleAccountRequest request = new EsSaleAccountRequest();
        request.setSaleChannel(SaleChannel.CHANNEL_AMAZON);
        request.setAccountSites(innerParam.getSites());
        request.setAccountNumberList(innerParam.getAccountNumbers());
        List<SaleAccount> saleAccountList = saleAccountService.getAccountInfoList(request, "accountNumber", "merchantId");
        // 按照merchantId 分组 同一个merchantId为套账 共用一个限流
        Map<String, List<String>> merchantIdAccountsMap = saleAccountList.stream()
                .filter(account -> (StringUtils.isNotBlank(account.getAccountNumber()) && StringUtils.isNotBlank(account.getMerchantId())))
                .collect(Collectors.groupingBy(SaleAccount::getMerchantId, Collectors.mapping(SaleAccount::getAccountNumber, Collectors.toList())));

        merchantIdAccountsMap.forEach((merchantId, accountNumbers) -> {
            try {
                // 使用信号量控制执行速率
                SKU_SEMAPHORE.acquire();
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
            // 同一套账,同一时间只允许一个店铺执行下架
            AmazonExecutors.ACCOUNT_LIMIT_POOL.execute(() -> {
                try {
                    XxlJobLogger.log("{},merchantId下架开始 链接账号：{}", merchantId, JSON.toJSONString(accountNumbers));
                    List<AmazonAccountRelation> amazonAccountRelationList = getAmazonAccount(innerParam.getSites(), accountNumbers);
                    if (CollectionUtils.isEmpty(amazonAccountRelationList)) {
                        XxlJobLogger.log(merchantId + " :merchantId下查询无启用店铺！");
                        return;
                    }
                    for (AmazonAccountRelation accountRelation : amazonAccountRelationList) {
                        try {
                            offLinkLogService.offlineAccountLinkByType(accountRelation, innerParam.getStatisticsDate(), deleteAmazonListingDto, ASIN_SALE_COUNT_COMPARE_RULES);
                        } catch (Exception e) {
                            XxlJobLogger.log("{},按账号下架链接异常：{}", accountRelation.getAccountNumber(), e.getMessage());
                        }
                    }
                } catch (Exception e) {
                    XxlJobLogger.log("{},按merchantId下架链接异常：{}", merchantId, e.getMessage());
                } finally {
                    SKU_SEMAPHORE.release();
                    XxlJobLogger.log("{},merchantId下架结束 链接账号：{}", merchantId, JSON.toJSONString(accountNumbers));
                }
            });
        });
        
    }

    private void statisticsExecute(InnerParam innerParam) {
        StopWatch started = StopWatch.createStarted();
        // 获取所有asin
        Set<String> fbaAsinList = OrderUtils.getAllAsinCodeCache();
        if (CollectionUtils.isEmpty(fbaAsinList)) {
            XxlJobLogger.log("获取所有FBA asin为空");
            return;
        }

        List<String> spuSets = loadAllSpu(innerParam);
        if (CollectionUtils.isEmpty(spuSets)) {
            XxlJobLogger.log("获取所有spu为空");
            return;
        }
        List<List<String>> spuPartitions = Lists.partition(spuSets, 1000);
        CountDownLatch countDownLatch = new CountDownLatch(spuPartitions.size());
        for (List<String> spuPartition : spuPartitions) {
            AmazonExecutors.ACCOUNT_LIMIT_POOL.execute(() -> {
                try {
                    // 1.指定spu下无销量的listing链接
                    statisticsOffLinkByArticleNumber(innerParam.getAccountNumbers(), fbaAsinList, spuPartition, innerParam.getSites());
                } catch (Exception e) {
                    log.error("统计指定spu下无销量的listing链接异常：{}", e.getMessage(), e);
                    XxlJobLogger.log("统计指定spu下无销量的listing链接异常：{}", e.getMessage());
                } finally {
                    XxlJobLogger.log("批次统计完成, 当前剩余：{}", countDownLatch.getCount());
                    countDownLatch.countDown();
                }
            });
        }

        try {
            countDownLatch.await();
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        XxlJobLogger.log("所有批次统计完成, 耗时:{}", started.formatTime());
        statisticsAccountExecute(innerParam);
        started.stop();
        XxlJobLogger.log("统计完成:{}", started.formatTime());
    }

    private void statisticsOffLinkByAccountNumber(LocalDate statisticalDate, List<AmazonAccountRelation> amazonAccountRelationList, Double limitRatio) {
        if (CollectionUtils.isEmpty(amazonAccountRelationList)) {
            XxlJobLogger.log("查询Amazon所有启用的店铺为空");
            return;
        }
        for (AmazonAccountRelation accountRelation : amazonAccountRelationList) {
            try {
                // 使用信号量控制执行速率
                SKU_SEMAPHORE.acquire();
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
            AmazonExecutors.ACCOUNT_LIMIT_POOL.execute(() -> {
                try {
                    offLinkLogService.statisticsAccountOffLinkLimitRate(accountRelation, statisticalDate, AmazonOfflineEnums.Type.SPECIFIED_SPU_DELETE.name(), limitRatio);
                } catch (Exception e) {
                    XxlJobLogger.log("{},按店铺统计可下架链接异常：{}", accountRelation.getAccountNumber(), e.getMessage());
                } finally {
                    SKU_SEMAPHORE.release();
                }
            });
        }
    }

    /**
     * 按ArticleNumber分配所有链接至链接池
     */
    private void statisticsOffLinkByArticleNumber(List<String> accountNumbers, Set<String> fbaAsinList, List<String> spuList, List<String> sites) {
        NativeSearchQueryBuilder queryBuilder = new NativeSearchQueryBuilder();
        // 无销量或者总销量为0
        BoolQueryBuilder shouldBoolQuery = QueryBuilders.boolQuery();
        shouldBoolQuery.should(QueryBuilders.boolQuery().must(QueryBuilders.rangeQuery("order_num_total").to(0)));
        shouldBoolQuery.should(QueryBuilders.boolQuery().mustNot(QueryBuilders.existsQuery("order_num_total")));
        // 变体、单体、在线
        List<Integer> itemType = List.of(AmazonListingitemtypeEnum.Monomer_Item.getStatusCode(), AmazonListingitemtypeEnum.Vriant_Item.getStatusCode());
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery()
                .filter(QueryBuilders.termQuery("isOnline", true))
                .filter(QueryBuilders.termQuery("skuDataSource", SkuDataSourceEnum.PRODUCT_SYSTEM.getCode()))
                .filter(QueryBuilders.termsQuery("itemType", itemType))
                .filter(QueryBuilders.termsQuery("mainSku", spuList))
                .must(shouldBoolQuery);

        if (CollectionUtils.isNotEmpty(accountNumbers)) {
            boolQueryBuilder.filter(QueryBuilders.termsQuery("accountNumber", accountNumbers));
        }

        if (CollectionUtils.isNotEmpty(sites)) {
            boolQueryBuilder.filter(QueryBuilders.termsQuery("site", sites));
        }

        NativeSearchQuery searchQuery = queryBuilder.withQuery(boolQueryBuilder)
                .withFields("accountNumber", "sellerSku", "articleNumber", "itemType", "skuDataSource", "mainSku", "isOnline", "openDate", "parentAsin", "sonAsin", "order_num_total", "site", "openDate")
                .build();

        String scrollId = null;
        List<String> scrollIdList = new ArrayList<>();
        long scrollTimeInMillis = 10 * 60 * 1000;
        while (true) {
            SearchScrollHits<EsAmazonProductListing> searchScrollHits = null;
            if (scrollId == null) {
                searchScrollHits = elasticsearchRestTemplate1.searchScrollStart(scrollTimeInMillis, searchQuery, EsAmazonProductListing.class, EsAmazonProductListingServiceImpl.amazonProductListingIndexCoordinates);
            } else {
                searchScrollHits = elasticsearchRestTemplate1.searchScrollContinue(scrollId, scrollTimeInMillis, EsAmazonProductListing.class, EsAmazonProductListingServiceImpl.amazonProductListingIndexCoordinates);
            }

            scrollId = searchScrollHits.getScrollId();
            scrollIdList.add(scrollId);
            if (!searchScrollHits.hasSearchHits()) {
                elasticsearchRestTemplate1.searchScrollClear(scrollIdList);
                break;
            }
            List<EsAmazonProductListing> dataList = (List) SearchHitSupport.unwrapSearchHits(searchScrollHits);
            if (CollectionUtils.isEmpty(dataList)) {
                break;
            }
            List<EsAmazonProductListing> spuListings = dataList.stream()
                    .filter(listing -> StringUtils.isNotBlank(listing.getMainSku()) && StringUtils.isNotBlank(listing.getSonAsin()))
                    .filter(listing -> !fbaAsinList.contains(listing.getSonAsin())) // 排除fba asin
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(spuListings)) {
                continue;
            }

            AtomicInteger atomicInteger = new AtomicInteger();
            List<List<EsAmazonProductListing>> partition = Lists.partition(spuListings, 1000);
            CountDownLatch countDownLatch = new CountDownLatch(partition.size());
            for (List<EsAmazonProductListing> listings : partition) {
                AmazonExecutors.OFFLINE_LIMIT_POOL.execute(() -> {
                    try {
                        int linkSize = statisticsOffLinkList(listings);
                        atomicInteger.addAndGet(linkSize);
                    } catch (Exception e) {
                        log.error("统计指定spu下无销量的listing链接异常：{}", e.getMessage(), e);
                        XxlJobLogger.log("统计指定spu下无销量的listing链接异常：{}", e.getMessage());
                    } finally {
                        countDownLatch.countDown();
                    }
                });
            }
            try {
                countDownLatch.await();
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
            XxlJobLogger.log("当前批次统计完成, 总数：{}, 链接数：{}", dataList.size(), atomicInteger.get());
        }
    }

    private int statisticsOffLinkList(List<EsAmazonProductListing> listings) {
        if (CollectionUtils.isEmpty(listings)) {
            return 0;
        }
        List<EsAmazonProductListing> offLinkList = new ArrayList<>(listings);
        // 校验全站的asin销量是否为0
        offLinkLogService.removeHasAsinsSaleCountListing(offLinkList, ASIN_SALE_COUNT_COMPARE_RULES);
        if (CollectionUtils.isEmpty(offLinkList)) {
            return 0;
        }
        // 记录扩展额外数据
        BiConsumer<EsAmazonProductListing, OverLimitAccountOffLinkLog> addExtraDataFunc = (listing, offLinkLog) -> {
            offLinkLogService.addExtraData(offLinkLog, Map.of("spu", listing.getMainSku()));
        };
        offLinkLogService.addListingToOffLinkPool(offLinkList, LocalDateTime.of(LocalDate.now(), LocalTime.MIN), AmazonOfflineEnums.Type.SPECIFIED_SPU_DELETE.getCode(), addExtraDataFunc);
        return offLinkList.size();
    }

    /**
     * 查询Amazon所有启用的店铺
     *
     * @return
     */
    private List<AmazonAccountRelation> getAmazonAccount(List<String> sites, List<String> accountNumbers) {
        // 获取全部账号
        AmazonAccountRelationExample amazonAccountRelationExample = new AmazonAccountRelationExample();
        AmazonAccountRelationExample.Criteria criteria = amazonAccountRelationExample.createCriteria()
                .andSaleAccountStatusEqualTo(1);
        if (CollectionUtils.isNotEmpty(accountNumbers)) {
            criteria.andAccountNumberIn(accountNumbers);
        }
        if (CollectionUtils.isNotEmpty(sites)) {
            criteria.andAccountCountryIn(sites);
        }
        String oneColumns = "account_number, account_status, account_country";
        amazonAccountRelationExample.setFiledColumns(oneColumns);
        return amazonAccountRelationService.selectFiledColumnsByExample(amazonAccountRelationExample);
    }

    private List<String> loadAllSpu(InnerParam innerParam) {
        Set<String> spuSet = new HashSet<>();
        int startId = innerParam.getStartId();
        while (true) {
            OverLimitNoSalesSkuExample example = new OverLimitNoSalesSkuExample();
            example.setLimit(1000);
            example.setOrderByClause("id asc");
            OverLimitNoSalesSkuExample.Criteria criteria = example.createCriteria();
            criteria.andIdGreaterThan(startId);

            List<OverLimitNoSalesSku> overLimitNoSalesSkus = noSalesSkuService.selectByExample(example);
            if (CollectionUtils.isEmpty(overLimitNoSalesSkus)) {
                break;
            }
            for (OverLimitNoSalesSku limitNoSalesSkus : overLimitNoSalesSkus) {
                if (limitNoSalesSkus.getId() < innerParam.getEndId()) {
                    spuSet.add(limitNoSalesSkus.getArticleNumber());
                } else {
                    break;
                }
            }
            startId = overLimitNoSalesSkus.get(overLimitNoSalesSkus.size() - 1).getId();
        }
        XxlJobLogger.log("加载所有待下架spu完成,数量:{}", spuSet.size());
        return new ArrayList<>(spuSet);

    }

}

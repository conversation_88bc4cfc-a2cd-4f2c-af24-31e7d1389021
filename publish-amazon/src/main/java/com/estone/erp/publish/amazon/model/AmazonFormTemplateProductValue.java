package com.estone.erp.publish.amazon.model;

import java.io.Serializable;
import java.sql.Timestamp;

import com.estone.erp.common.annotation.CheckColumn;
import lombok.Data;

@Data
public class AmazonFormTemplateProductValue implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     *  database column amazon_form_template_product_value.id
     */
    private Integer id;

    /**
     * 模板站点 database column amazon_form_template_product_value.template_site
     */
    @CheckColumn(columnName = "模板站点", maxLength = 2)
    private String templateSite;

    /**
     * 模板类型 database column amazon_form_template_product_value.template_type
     */
    @CheckColumn(columnName = "模板类型", maxLength = 50)
    private String templateType;

    /**
     * productType database column amazon_form_template_product_value.product_type
     */
    @CheckColumn(columnName = "ProductType", maxLength = 150)
    private String productType;

    /**
     * 默认值 database column amazon_form_template_product_value.default_value
     */
    @CheckColumn(columnName = "默认值")
    private String defaultValue;

    /**
     * 是否启用（1：启用，0：禁用） database column amazon_form_template_product_value.enable
     */
    private Boolean enable;

    /**
     * 创建人 database column amazon_form_template_product_value.create_by
     */
    private String createBy;

    /**
     *  database column amazon_form_template_product_value.create_time
     */
    private Timestamp createTime;

    /**
     *  database column amazon_form_template_product_value.update_by
     */
    private String updateBy;

    /**
     *  database column amazon_form_template_product_value.update_time
     */
    private Timestamp updateTime;
}
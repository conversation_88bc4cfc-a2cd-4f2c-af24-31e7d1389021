package com.estone.erp.publish.amazon.controller;

import com.alibaba.fastjson.TypeReference;
import com.estone.erp.common.model.api.ApiRequestParam;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.model.api.CQuery;
import com.estone.erp.common.model.api.CQueryResult;
import com.estone.erp.common.util.Asserts;
import com.estone.erp.publish.common.ErrorCode;
import com.estone.erp.publish.tidb.publishtidb.model.AmazonEanPool;
import com.estone.erp.publish.tidb.publishtidb.model.AmazonEanPoolCriteria;
import com.estone.erp.publish.tidb.publishtidb.service.AmazonEanPoolService;
import org.apache.commons.lang.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * 2024-08-20 15:50:38
 */
@RestController
@RequestMapping("amazonEanPool")
public class AmazonEanPoolController {
    @Resource
    private AmazonEanPoolService amazonEanPoolService;

    @PostMapping
    public ApiResult<?> postAmazonEanPool(@RequestBody(required = true) ApiRequestParam<String> requestParam) {
        String method = requestParam.getMethod();
        if (StringUtils.isNotBlank(method)) {
            switch (method) {
                case "searchAmazonEanPool": // 查询列表
                    CQuery<AmazonEanPoolCriteria> cquery = requestParam.getArgsValue(new TypeReference<CQuery<AmazonEanPoolCriteria>>() {});
                    Asserts.isTrue(cquery != null, ErrorCode.PARAM_EMPTY_ERROR);
                    CQueryResult<AmazonEanPool> results = amazonEanPoolService.search(cquery);
                    return results;
                case "addAmazonEanPool": // 添加
                    AmazonEanPool amazonEanPool = requestParam.getArgsValue(new TypeReference<AmazonEanPool>() {});
                    amazonEanPoolService.insert(amazonEanPool);
                    return ApiResult.newSuccess(amazonEanPool);
                }
        }
        return ApiResult.newSuccess();
    }

    @GetMapping(value = "/{id}")
    public ApiResult<?> getAmazonEanPool(@PathVariable(value = "id", required = true) Integer id) {
        AmazonEanPool amazonEanPool = amazonEanPoolService.selectByPrimaryKey(id);
        return ApiResult.newSuccess(amazonEanPool);
    }

    @PutMapping(value = "/{id}")
    public ApiResult<?> putAmazonEanPool(@PathVariable(value = "id", required = true) Integer id, @RequestBody ApiRequestParam<String> requestParam) {
        String method = requestParam.getMethod();
        if (StringUtils.isNotBlank(method)) {
            switch (method) {
                case "updateAmazonEanPool": // 单个修改
                    AmazonEanPool amazonEanPool = requestParam.getArgsValue(new TypeReference<AmazonEanPool>() {});
                    amazonEanPoolService.updateByPrimaryKeySelective(amazonEanPool);
                    return ApiResult.newSuccess(amazonEanPool);
                }
        }
        return ApiResult.newSuccess();
    }
}
package com.estone.erp.publish.amazon.service;

import com.estone.erp.common.model.api.CQuery;
import com.estone.erp.common.model.api.CQueryResult;
import com.estone.erp.publish.amazon.model.AmazonFormLog;
import com.estone.erp.publish.amazon.model.AmazonFormLogCriteria;
import com.estone.erp.publish.amazon.model.AmazonFormLogExample;
import java.util.List;
import java.util.Map;
import java.util.function.Consumer;

import org.apache.ibatis.annotations.Param;
import org.springframework.web.multipart.MultipartFile;

/**
 * <AUTHOR> amazon_form_log
 * 2020-09-08 17:45:43
 */
public interface AmazonFormLogService {
    int countByExample(AmazonFormLogExample example);

    CQueryResult<AmazonFormLog> search(CQuery<AmazonFormLogCriteria> cquery);

    List<AmazonFormLog> selectByExample(AmazonFormLogExample example);

    AmazonFormLog selectByPrimaryKey(Long id);

    int insert(AmazonFormLog record);

    int updateByPrimaryKeySelective(AmazonFormLog record);

    int updateByExampleSelective(AmazonFormLog record, AmazonFormLogExample example);

    int deleteByPrimaryKey(List<Long> ids);

    void uploadFileLog(MultipartFile file, Map<String, String> msgMap, Consumer<AmazonFormLog> beforeConsumer, Consumer<AmazonFormLog> afterConsumer);

    int addLog(Object oldBean, Object newBean, Consumer<AmazonFormLog> consumer, Consumer<AmazonFormLog> afterConsumer);
}
package com.estone.erp.publish.amazon.model;

import lombok.Data;

import java.io.Serializable;
import java.sql.Timestamp;

/**
 * 新品推荐
 * <AUTHOR>
 */
@Data
public class AmazonNewRemind implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     *  database column amazon_new_remind.id
     */
    private Integer id;

    /**
     * 货号 database column amazon_new_remind.spu
     */
    private String spu;

    /**
     * 店铺 database column amazon_new_remind.account
     */
    private String account;

    /**
     * 销售 database column amazon_new_remind.sale_man
     */
    private String saleMan;

    /**
     * 销售组长 database column amazon_new_remind.sale_leader_man
     */
    private String saleLeaderMan;

    /**
     * 头图 database column amazon_new_remind.first_image
     */
    private String firstImage;

    /**
     * 标题 database column amazon_new_remind.title
     */
    private String title;

    /**
     * 可刊登分类 database column amazon_new_remind.category
     */
    private String category;

    /**
     * 编辑完成时间 database column amazon_new_remind.edit_finish_time
     */
    private Timestamp editFinishTime;

    /**
     * 开发录入时间 database column amazon_new_remind.create_at
     */
    private Timestamp createAt;

    /**
     * 推送时间 database column amazon_new_remind.push_time
     */
    private Timestamp pushTime;

    /**
     * 是否有成功模板 database column amazon_new_remind.is_success_temp
     */
    private Boolean isSuccessTemp;

    /**
     * 模板创建时间 database column amazon_new_remind.temp_finish_time
     */
    private Timestamp tempFinishTime;

    /**
     * 备注 database column amazon_new_remind.remarks
     */
    private String remarks;

    /**
     * 创建人 database column amazon_new_remind.create_by
     */
    private String createBy;

    /**
     * 创建时间 database column amazon_new_remind.create_date
     */
    private Timestamp createDate;

    /**
     * 修改人 database column amazon_new_remind.last_update_by
     */
    private String lastUpdateBy;

    /**
     * 修改时间 database column amazon_new_remind.last_update_date
     */
    private Timestamp lastUpdateDate;

    /**
     * 模板id database column amazon_new_remind.template_id
     */
    private Integer templateId;

    /**
     * 是否多站点刊登: 1是 0 否 database column amazon_new_remind.is_site_publish
     */
    private Boolean isSitePublish;

    /**
     * 步骤[模板]刊登成功 database column amazon_new_remind.step_template_status
     */
    private Boolean stepTemplateStatus;

    /**
     * 店铺国家 database column amazon_new_remind.account_country
     */
    private String accountCountry;

    /**
     * 刊登详细状态: 1待刊登 2刊登中 8刊登成功 9刊登失败
     */
    private Integer publishStatus;

    /**
     * 文案
     */
    private String editor;

    /**
     * 刊登角色: 0其他 1销售 2文案
     */
    private Integer publishRole;

    /**
     * 数据源 1编辑完成时间为昨天的产品 2编辑完成时间为7天前的产品
     */
    private Integer dataSourceType;

    /**
     * 重分配时间
     */
    private Timestamp reassignTime;

    /**
     * 重分配人
     */
    private String reassignBy;

    /**
     * 重分配状态
     */
    private Integer reassignStatus;

    /**
     * 重分配失败备注
     */
    private String reassignFailMsg;

    /**
     * 重分配重试次数
     */
    private Integer reassignRetryCount;
}
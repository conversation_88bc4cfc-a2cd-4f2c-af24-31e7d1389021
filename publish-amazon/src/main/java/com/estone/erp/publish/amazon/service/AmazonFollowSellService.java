package com.estone.erp.publish.amazon.service;

import com.estone.erp.common.model.api.CQuery;
import com.estone.erp.common.model.api.CQueryResult;
import com.estone.erp.publish.amazon.bo.AmazonFollowSellBO;
import com.estone.erp.publish.amazon.bo.AmazonFollowSellSuperiorBO;
import com.estone.erp.publish.amazon.model.AmazonFollowSell;
import com.estone.erp.publish.amazon.model.AmazonFollowSellExample;
import com.estone.erp.publish.amazon.model.dto.AmazonFollowSellCriteria;

import java.util.List;

public interface AmazonFollowSellService {
    
    void insert(AmazonFollowSell amazonFollowSell);
    
    void update(AmazonFollowSell amazonFollowSell);

    void update(List<AmazonFollowSellBO> amazonFollowSell);

    AmazonFollowSell findById(Integer id);
    
    List<AmazonFollowSell> findByExample(AmazonFollowSellExample example);
    
    void deleteById(Integer id);
    
    void batchLogicDelete(List<AmazonFollowSell> dataList);
    
    CQueryResult<AmazonFollowSell> search(CQuery<AmazonFollowSellCriteria> cquery);

    void batchUpdateAmazonFollowSell2OffLineDelete(List<AmazonFollowSellBO> entityList);


    int batchInsert(List<AmazonFollowSellSuperiorBO> list);

    /**
     * 处理刊登价格库存 产品刊登成功的
     */
    void handlePublishPrInvData();

    /**
     * 处理跟卖列表状态
     */
    void handlePublishStatus();

    long countByExample(AmazonFollowSellExample exampleFollow);
}

package com.estone.erp.publish.amazon.service;

import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.model.api.CQuery;
import com.estone.erp.common.model.api.CQueryResult;
import com.estone.erp.publish.amazon.model.AmazonCategorySpProductType;
import com.estone.erp.publish.amazon.model.AmazonCategorySpProductTypeCriteria;
import com.estone.erp.publish.amazon.model.AmazonCategorySpProductTypeExample;
import com.estone.erp.publish.amazon.model.request.DefinitionsProductTypeRequest;
import com.estone.erp.publish.amazon.mq.model.ProductTypeKeyResultMessage;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * 2024-11-08 11:30:25
 */
public interface AmazonCategorySpProductTypeService {
    int countByExample(AmazonCategorySpProductTypeExample example);

    CQueryResult<AmazonCategorySpProductType> search(CQuery<AmazonCategorySpProductTypeCriteria> cquery);

    List<AmazonCategorySpProductType> selectByExample(AmazonCategorySpProductTypeExample example);

    AmazonCategorySpProductType selectByPrimaryKey(Integer id);

    int insert(AmazonCategorySpProductType record);

    int updateByPrimaryKeySelective(AmazonCategorySpProductType record);

    int updateByExampleSelective(AmazonCategorySpProductType record, AmazonCategorySpProductTypeExample example);

    int deleteByPrimaryKey(List<Integer> ids);

    List<AmazonCategorySpProductType> selectFiledColumnsByExample(AmazonCategorySpProductTypeExample example);


    AmazonCategorySpProductType syncUpdateProductType(DefinitionsProductTypeRequest request);

    String uploadProductTypeFile2Seaweed(String schmeUrl,String site,String productType);

    AmazonCategorySpProductType getSchemaUrlProductType(String site, String productType);

    String getProductTypeSchemaLocalCache(String schemaUrl);

    void updateProductTypeKeys(ProductTypeKeyResultMessage productTypeKeyResultMessage);

    void syncPropertiesKeys(String ids);

    ApiResult<Map<String, String>> findPropertiesProductType(String properties);
}
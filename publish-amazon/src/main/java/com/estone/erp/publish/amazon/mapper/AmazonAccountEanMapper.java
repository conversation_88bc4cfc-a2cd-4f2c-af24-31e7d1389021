package com.estone.erp.publish.amazon.mapper;

import com.estone.erp.publish.amazon.model.AmazonAccountEan;
import com.estone.erp.publish.amazon.model.AmazonAccountEanExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface AmazonAccountEanMapper {
    int countByExample(AmazonAccountEanExample example);

    int deleteByExample(AmazonAccountEanExample example);

    int deleteByPrimaryKey(Integer id);

    int insert(AmazonAccountEan record);

    int insertSelective(AmazonAccountEan record);

    List<AmazonAccountEan> selectByExample(AmazonAccountEanExample example);

    AmazonAccountEan selectByPrimaryKey(Integer id);

    int updateByExampleSelective(@Param("record") AmazonAccountEan record, @Param("example") AmazonAccountEanExample example);

    int updateByExample(@Param("record") AmazonAccountEan record, @Param("example") AmazonAccountEanExample example);

    int updateByPrimaryKeySelective(AmazonAccountEan record);

    int updateByPrimaryKey(AmazonAccountEan record);
}
package com.estone.erp.publish.tidb.publishtidb.domain.offline;

import com.estone.erp.common.model.api.PListDto;
import lombok.Data;

import java.util.List;

@Data
public class AmazonOfflineSearchDTO extends PListDto {
    /**
     * 规则名称
     */
    private String ruleName;

    /**
     * 类型: 1 链接包含侵权词汇下架 2 指定SKU下架 3 禁售产品下架 4 保留链接数
     */
    private List<Integer> typeList;

    /**
     * 适用店铺
     */
    private List<String> accounts;

    /**
     * 状态 0 禁用 1 启用
     */
    private List<Integer> statusList;

    /**
     * 创建人
     */
    private List<String> createdByList;


    /**
     * 创建时间-开始
     */
    private String createdTimeFrom;

    /**
     * 创建时间-结束
     */
    private String createdTimeTo;
}

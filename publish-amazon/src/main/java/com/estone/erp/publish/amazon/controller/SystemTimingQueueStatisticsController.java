package com.estone.erp.publish.amazon.controller;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.estone.erp.common.model.api.ApiRequestParam;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.model.api.CQuery;
import com.estone.erp.common.model.api.CQueryResult;
import com.estone.erp.common.util.Asserts;
import com.estone.erp.publish.amazon.bo.ExcelSystemTimingQueueStatistics;
import com.estone.erp.publish.amazon.model.SystemTimingQueueStatistics;
import com.estone.erp.publish.amazon.model.SystemTimingQueueStatisticsCriteria;
import com.estone.erp.publish.amazon.service.SystemTimingQueueStatisticsService;
import com.estone.erp.publish.common.ErrorCode;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.elasticsearch.util.EsAccountUtils;
import com.estone.erp.publish.system.newUsermgt.NewUsermgtUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> system_timing_queue_statistics
 * 2024-01-17 14:07:48
 */
@RestController
@RequestMapping("systemTimingQueueStatistics")
public class SystemTimingQueueStatisticsController {
    @Resource
    private SystemTimingQueueStatisticsService systemTimingQueueStatisticsService;

    @PostMapping
    public ApiResult<?> postSystemTimingQueueStatistics(@RequestBody(required = true) ApiRequestParam<String> requestParam, HttpServletResponse response) throws IOException {
        String method = requestParam.getMethod();
        if (StringUtils.isNotBlank(method)) {
            switch (method) {
                case "searchSystemTimingQueueStatistics": {
                    CQuery<SystemTimingQueueStatisticsCriteria> cquery = requestParam.getArgsValue(new TypeReference<>() {
                    });
                    Asserts.isTrue(cquery != null, ErrorCode.PARAM_EMPTY_ERROR);
                    SystemTimingQueueStatisticsCriteria search = cquery.getSearch();
                    // 超级管理员，销售主管可看全部账号
                    ApiResult<Boolean> superAdminOrEquivalent = NewUsermgtUtils.isSuperAdminOrEquivalent(SaleChannel.CHANNEL_AMAZON);
                    String error = this.handleAuth(search, superAdminOrEquivalent);
                    if (StringUtils.isNotBlank(error)) {
                        return ApiResult.newError(error);
                    }
                    // 有权限账号和输入账号都为空 且不是主管超管 直接返回
                    List<String> accounts = search.getAccountNumbers();
                    if (CollectionUtils.isEmpty(accounts) && !superAdminOrEquivalent.getResult()) {
                        return ApiResult.newSuccess();
                    }
                    // 查询列表
                    CQueryResult<SystemTimingQueueStatistics> results = systemTimingQueueStatisticsService.search(cquery);
                    return results;
                }
                case "download": {
                    CQuery<SystemTimingQueueStatisticsCriteria> cquery = requestParam.getArgsValue(new TypeReference<>() {
                    });
                    Asserts.isTrue(cquery != null, ErrorCode.PARAM_EMPTY_ERROR);
                    SystemTimingQueueStatisticsCriteria search = cquery.getSearch();
                    // 超级管理员，销售主管可看全部账号
                    ApiResult<Boolean> superAdminOrEquivalent = NewUsermgtUtils.isSuperAdminOrEquivalent(SaleChannel.CHANNEL_AMAZON);
                    String error = this.handleAuth(search, superAdminOrEquivalent);
                    if (StringUtils.isNotBlank(error)) {
                        return ApiResult.newError(error);
                    }
                    // 有权限账号和输入账号都为空 且不是主管超管 直接返回
                    List<String> accounts = search.getAccountNumbers();
                    if (CollectionUtils.isEmpty(accounts) && !superAdminOrEquivalent.getResult()) {
                        return ApiResult.newError("无账号权限");
                    }
                    OutputStream out = null;
                    int page = 0;
                    int limit = 500;
                    try {
                        ExcelWriter excelWriter = null;
                        WriteSheet writeSheet = EasyExcel.writerSheet("sheet").build();
                        while (true) {
                            cquery.setLimit(limit);
                            cquery.setOffset((page++) * limit);
                            CQueryResult<SystemTimingQueueStatistics> dataList = systemTimingQueueStatisticsService.search(cquery);
                            List<SystemTimingQueueStatistics> context = dataList.getRows();
                            if (CollectionUtils.isEmpty(context)) {
                                break;
                            }
                            List<ExcelSystemTimingQueueStatistics> excelSystemTimingQueueStatisticsList = new ArrayList<>();
                            for (SystemTimingQueueStatistics systemTimingQueueStatistics : context) {
                                ExcelSystemTimingQueueStatistics excelSystemTimingQueueStatistics = new ExcelSystemTimingQueueStatistics(systemTimingQueueStatistics);
                                excelSystemTimingQueueStatisticsList.add(excelSystemTimingQueueStatistics);
                            }
                            if (excelWriter == null) {
                                out = response.getOutputStream();
                                excelWriter = EasyExcel.write(out, ExcelSystemTimingQueueStatistics.class)
                                        .includeColumnFiledNames(ExcelSystemTimingQueueStatistics.getDownLoadFiled()).build();
                            }
                            excelWriter.write(excelSystemTimingQueueStatisticsList, writeSheet);
                        }

                        response.setContentType("application/vnd.ms-excel");
                        response.setCharacterEncoding("utf-8");
                        String fileName = URLEncoder.encode("systemTimingQueueStatistics" + LocalDateTime.now(), StandardCharsets.UTF_8);
                        response.setHeader("Content-disposition", "attachment;filename=" + fileName + ".xlsx");
                        if (null != excelWriter) {
                            excelWriter.finish();
                        }
                    } catch (Exception e) {
                        response.setContentType("application/json");
                        response.setCharacterEncoding("utf-8");
                        response.getWriter().println(JSON.toJSONString(ApiResult.newError(e.getMessage())));
                    } finally {
                        IOUtils.closeQuietly(out);
                    }
                    return null;
                }
            }
        }
        return ApiResult.newSuccess();
    }

    private String handleAuth(SystemTimingQueueStatisticsCriteria search, ApiResult<Boolean> superAdminOrEquivalent) {
        // 取勾选的，如果没有勾选的，判断是否为管理员，非管理员，查询相关的账号权限
        List<String> accountNumbers = search.getAccountNumbers();
        if (CollectionUtils.isEmpty(accountNumbers)) {
            if (!superAdminOrEquivalent.isSuccess()) {
                return superAdminOrEquivalent.getErrorMsg();
            }
            if (!superAdminOrEquivalent.getResult()) {
                ApiResult<List<String>> authorAccountList = EsAccountUtils.getAmazonAuthorAccountList(SaleChannel.CHANNEL_AMAZON, false);
                if (!authorAccountList.isSuccess()) {
                    return authorAccountList.getErrorMsg();
                }
                accountNumbers = authorAccountList.getResult();
            }
        }
        search.setAccountNumbers(accountNumbers);
        return null;
    }
}

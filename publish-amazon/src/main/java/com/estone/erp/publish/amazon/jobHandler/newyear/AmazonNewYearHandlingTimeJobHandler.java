package com.estone.erp.publish.amazon.jobHandler.newyear;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.estone.erp.common.jobHandler.AbstractJobHandler;
import com.estone.erp.common.model.TidbPageMeta;
import com.estone.erp.common.mq.RabbitMqSender;
import com.estone.erp.common.util.TidbPageMetaUtil;
import com.estone.erp.publish.amazon.componet.scheduler.enums.AmazonSchedulerTaskEnums;
import com.estone.erp.publish.amazon.enums.HandlingTimeRuleTypeEnums;
import com.estone.erp.publish.amazon.model.dto.AmazonHandlingTimeRuleDO;
import com.estone.erp.publish.amazon.mq.AmazonQueues;
import com.estone.erp.publish.amazon.mq.model.AmazonSchedulerTaskJobMessage;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.common.util.CacheUtils;
import com.estone.erp.publish.mq.PublishMqConfig;
import com.estone.erp.publish.platform.enums.SingleItemEnum;
import com.estone.erp.publish.system.param.model.SystemParam;
import com.estone.erp.publish.system.param.service.SystemParamService;
import com.estone.erp.publish.tidb.product.dao.SingleItemDao;
import com.estone.erp.publish.tidb.product.entity.SingleItem;
import com.google.common.collect.Lists;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * ES-10802 【Amazon】春节调发货期定时任务
 * 发送店铺到执行器执行：{@link com.estone.erp.publish.amazon.componet.scheduler.handler.AmazonNewYearHandlingTimeHandler}
 *
 * <AUTHOR>
 * @date 2024-12-31 9:50
 */
@Component
public class AmazonNewYearHandlingTimeJobHandler extends AbstractJobHandler {
    @Resource
    private SystemParamService systemParamService;
    @Resource
    private RabbitMqSender rabbitMqSender;
    @Resource
    private SingleItemDao singleItemDao;

    public AmazonNewYearHandlingTimeJobHandler() {
        super(AmazonNewYearHandlingTimeJobHandler.class.getName());
    }

    @Data
    private static class InnerParam {
        private List<String> accountNumbers;
    }


    @Override
    @XxlJob("AmazonNewYearHandlingTimeJobHandler")
    public ReturnT<String> run(String param) throws Exception {
        InnerParam innerParam = passParam(param, InnerParam.class);
        if (innerParam == null) {
            XxlJobLogger.log("参数错误, 请检查参数");
            return ReturnT.FAIL;
        }

        // 获取备货期配置
        List<AmazonHandlingTimeRuleDO> handlingTimeConfig = getHandlingTimeConfig();
        AmazonHandlingTimeRuleDO amazonHandlingTimeRuleDO = matchRuleConfig(handlingTimeConfig);
        if (amazonHandlingTimeRuleDO == null) {
            XxlJobLogger.log("当前时间：{},未匹配到处理规则", LocalDateTime.now());
            return ReturnT.FAIL;
        }

        if (HandlingTimeRuleTypeEnums.UPDATE.isTrue(amazonHandlingTimeRuleDO.getType())) {
            // 更新备货期
            updateHandlingTimeHandler();
            return ReturnT.SUCCESS;
        }

        if (HandlingTimeRuleTypeEnums.RESTORE.isTrue(amazonHandlingTimeRuleDO.getType())) {
            // 恢复备货期
            restoreHandlingTime(innerParam);
        }
        return ReturnT.SUCCESS;
    }

    private void restoreHandlingTime(InnerParam innerParam) {
        // 获取待执行店铺
        SystemParam systemParam = systemParamService.queryParamValue(SaleChannel.CHANNEL_AMAZON, "task_param", "handling_time_accounts");
        if (systemParam == null || StringUtils.isBlank(systemParam.getParamValue())) {
            XxlJobLogger.log("未配置待执行店铺");
            return;
        }
        String[] accountArray = systemParam.getParamValue().split(",");
        for (String account : accountArray) {
            if (CollectionUtils.isNotEmpty(innerParam.getAccountNumbers()) && !innerParam.getAccountNumbers().contains(account)) {
                continue;
            }
            XxlJobLogger.log("处理店铺: " + account);
            AmazonSchedulerTaskJobMessage message = new AmazonSchedulerTaskJobMessage();
            message.setData(StringUtils.trim(account));
            message.setScheduleTaskType(AmazonSchedulerTaskEnums.TaskType.UPDATE_NEW_YEAR_HANDLING_TIME.name());
            rabbitMqSender.allPublishVHostRabbitTemplateSend(PublishMqConfig.AMAZON_API_DIRECT_EXCHANGE, AmazonQueues.AMAZON_TASK_JOB_SCHEDULING_QUEUE_KEY, message);
        }
    }

    private void updateHandlingTimeHandler() {
        // 获取产品系统休假sku
        int itemStatus = SingleItemEnum.NORMAL.getCode();
        LambdaQueryWrapper<SingleItem> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(SingleItem::getId);
        queryWrapper.eq(SingleItem::getItemStatus, itemStatus);
        List<Map<Object, Object>> tidbPageMetaMap = singleItemDao.getTidbPageMetaMap(queryWrapper);
        List<TidbPageMeta<Long>> pageMetaList = TidbPageMetaUtil.getPageMetaList(tidbPageMetaMap);
        int total = 0;
        for (TidbPageMeta<Long> pageMeta : pageMetaList) {
            LambdaQueryWrapper<SingleItem> skuQueryWrapper = new LambdaQueryWrapper<>();
            skuQueryWrapper.select(SingleItem::getSonSku);
            skuQueryWrapper.eq(SingleItem::getItemStatus, itemStatus);
            skuQueryWrapper.between(SingleItem::getId, pageMeta.getStartKey(), pageMeta.getEndKey());
            List<SingleItem> items = singleItemDao.list(skuQueryWrapper);
            if (CollectionUtils.isEmpty(items)) {
                continue;
            }

            List<List<SingleItem>> partition = Lists.partition(items, 100);
            for (List<SingleItem> itemList : partition) {
                try {
                    List<String> holidaySkus = itemList.stream().map(SingleItem::getSonSku).collect(Collectors.toList());
                    AmazonSchedulerTaskJobMessage message = new AmazonSchedulerTaskJobMessage();
                    message.setData(JSON.toJSONString(holidaySkus));
                    message.setScheduleTaskType(AmazonSchedulerTaskEnums.TaskType.UPDATE_NEW_YEAR_HANDLING_TIME.name());
                    rabbitMqSender.allPublishVHostRabbitTemplateSend(PublishMqConfig.AMAZON_API_DIRECT_EXCHANGE, AmazonQueues.AMAZON_TASK_JOB_SCHEDULING_QUEUE_KEY, message);
                    total += holidaySkus.size();
                } catch (Exception e) {
                    XxlJobLogger.log("发送休假sku更新备货期失败：{}", e.getMessage());
                }
            }

        }
        XxlJobLogger.log("处理完成,累计执行休假sku数量：{}", total);

    }

    private List<AmazonHandlingTimeRuleDO> getHandlingTimeConfig() {
        String handingTimeRuleJson = CacheUtils.getSystemParamValue(SaleChannel.CHANNEL_AMAZON, "task_param", "handling_time_rule", 10);
        if (StringUtils.isBlank(handingTimeRuleJson)) {
            return null;
        }
        return JSON.parseArray(handingTimeRuleJson, AmazonHandlingTimeRuleDO.class);
    }


    private AmazonHandlingTimeRuleDO matchRuleConfig(List<AmazonHandlingTimeRuleDO> handlingTimeConfigs) {
        if (CollectionUtils.isEmpty(handlingTimeConfigs)) {
            return null;
        }

        LocalDateTime now = LocalDateTime.now();
        return handlingTimeConfigs.stream()
                .filter(config -> now.isAfter(config.getStarDateTime()) && now.isBefore(config.getEndDateTime())).findFirst()
                .orElseGet(() -> null);
    }
}

package com.estone.erp.publish.amazon.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.estone.erp.common.model.ResultModel;
import com.estone.erp.common.model.api.ApiRequestParam;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.model.api.CQuery;
import com.estone.erp.common.model.api.CQueryResult;
import com.estone.erp.common.util.Asserts;
import com.estone.erp.common.util.CommonCheckColumn;
import com.estone.erp.common.util.WebUtils;
import com.estone.erp.publish.amazon.enums.AmazonFormModuleType;
import com.estone.erp.publish.amazon.enums.AmazonFormOperationType;
import com.estone.erp.publish.amazon.model.AmazonFormTypeProductValue;
import com.estone.erp.publish.amazon.model.AmazonFormTypeProductValueCriteria;
import com.estone.erp.publish.amazon.model.AmazonFormTypeProductValueExample;
import com.estone.erp.publish.amazon.service.AmazonFormLogService;
import com.estone.erp.publish.amazon.service.AmazonFormTypeProductValueService;
import com.estone.erp.publish.common.ErrorCode;
import com.estone.erp.publish.common.util.ExcelUtils;
import com.estone.erp.publish.common.util.POIUtils;
import com.estone.erp.publish.common.util.PagingUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.sql.Timestamp;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 分类路径ProductType默认属性配置
 * <AUTHOR> amazon_form_type_product_value
 * 2020-09-08 18:40:08
 */
@Slf4j
@RestController
@RequestMapping("amazonFormTypeProductValue")
public class AmazonFormTypeProductValueController {
    @Resource
    private AmazonFormTypeProductValueService amazonFormTypeProductValueService;

    @Resource
    private AmazonFormLogService amazonFormLogService;

    @PostMapping
    public ApiResult<?> postAmazonFormTypeProductValue(@RequestBody(required = true) ApiRequestParam<String> requestParam) {
        String method = requestParam.getMethod();
        if (StringUtils.isNotBlank(method)) {
            switch (method) {
                case "searchAmazonFormTypeProductValue": // 查询列表
                    CQuery<AmazonFormTypeProductValueCriteria> cquery = requestParam.getArgsValue(new TypeReference<CQuery<AmazonFormTypeProductValueCriteria>>() {});
                    Asserts.isTrue(cquery != null, ErrorCode.PARAM_EMPTY_ERROR);
                    CQueryResult<AmazonFormTypeProductValue> results = amazonFormTypeProductValueService.search(cquery);
                    return results;
            }
        }
        return ApiResult.newSuccess();
    }

    /**
     * 删除
     * @param param
     * @return
     */
    @PostMapping(value = "/remove")
    public ApiResult<?> remove(@RequestBody(required = true)String param) {
        if(StringUtils.isBlank(param)){
            return ApiResult.newError("参数为空");
        }
        AmazonFormTypeProductValueCriteria bean;
        try {
            bean = JSON.parseObject(param, new TypeReference<AmazonFormTypeProductValueCriteria>() {
            });
        }catch (Exception e){
            log.error(String.format("参数解析错误：%s", param),e);
            return ApiResult.newError(String.format("参数解析错误：%s -> %s", param, e.getMessage()));
        }

        if(CollectionUtils.isEmpty(bean.getIds())){
            return ApiResult.newError("id为空");
        }

        int count = amazonFormTypeProductValueService.deleteByPrimaryKey(bean.getIds());
        amazonFormLogService.addLog(null, bean, (log) ->{
            log.setOperationType(AmazonFormOperationType.DELETE.name());
            log.setModuleType(AmazonFormModuleType.TYPE_PRODUCT_TYPE_VALUE_CONF.name());
        }, null);
        return ApiResult.newSuccess(count);
    }
    /**
     * 添加
     * @param param
     * @return
     */
    @PostMapping(value = "/add")
    public ApiResult<?> add(@RequestBody(required = true) String param) {
        if(StringUtils.isBlank(param)){
            return ApiResult.newError("参数为空");
        }

        AmazonFormTypeProductValue bean;
        try {
            bean = JSON.parseObject(param, new TypeReference<AmazonFormTypeProductValue>() {
            });
        }catch (Exception e){
            log.error(String.format("参数解析错误：%s", param),e);
            return ApiResult.newError(String.format("参数解析错误：%s -> %s", param, e.getMessage()));
        }
        String msg = CommonCheckColumn.checkFieldReturnMsg(bean);
        if(StringUtils.isNotBlank(msg)){
            return ApiResult.newError(msg);
        }

        AmazonFormTypeProductValueExample example = new AmazonFormTypeProductValueExample();
        example.createCriteria().andSiteEqualTo(bean.getSite()).andTypePathEnEqualTo(bean.getTypePathEn());
        int count = amazonFormTypeProductValueService.countByExample(example);
        if(count >0){
            return ApiResult.newError("站点，英文分类路径 已存在，不能重复添加！");
        }
        amazonFormTypeProductValueService.insert(bean);

        amazonFormLogService.addLog(null, bean, (log) ->{
            log.setRelationId(bean.getId());
            log.setOperationType(AmazonFormOperationType.ADD.name());
            log.setModuleType(AmazonFormModuleType.TYPE_PRODUCT_TYPE_VALUE_CONF.name());
        }, null);
        return ApiResult.newSuccess();
    }

    /**
     * 修改
     * @param param
     * @return
     */
    @PostMapping(value = "/update")
    public ApiResult<?> update(@RequestBody(required = true) String param) {
        if(StringUtils.isBlank(param)){
            return ApiResult.newError("参数为空");
        }
        AmazonFormTypeProductValue bean;
        try {
            bean = JSON.parseObject(param, new TypeReference<AmazonFormTypeProductValue>() {
            });
        }catch (Exception e){
            log.error(String.format("参数解析错误：%s", param),e);
            return ApiResult.newError(String.format("参数解析错误：%s -> %s", param, e.getMessage()));
        }
        if(bean.getId() == null){
            return ApiResult.newError("id为空，不能修改！");
        }
        String msg = CommonCheckColumn.checkFieldReturnMsg(bean);
        if(StringUtils.isNotBlank(msg)){
            return ApiResult.newError(msg);
        }

        AmazonFormTypeProductValueExample example = new AmazonFormTypeProductValueExample();
        example.createCriteria()
                .andSiteEqualTo(bean.getSite())
                .andTypePathEnEqualTo(bean.getTypePathEn());
        example.or().andIdEqualTo(bean.getId());
        List<AmazonFormTypeProductValue> dbList = amazonFormTypeProductValueService.selectByExample(example);
        long count = dbList.stream().filter(o -> !o.getId().equals(bean.getId())).count();
        if(count >0){
            return ApiResult.newError("修改后的站点，英文分类路径 已存在，不能修改！");
        }
        Optional<AmazonFormTypeProductValue> optional = dbList.stream().filter(o -> o.getId().equals(bean.getId())).findFirst();
        if(optional == null || !optional.isPresent()){
            return ApiResult.newError("要修改的数据不存在，请刷新数据！");
        }

        bean.setUpdateBy(WebUtils.getUserName());
        bean.setUpdateTime(new Timestamp(System.currentTimeMillis()));
        amazonFormTypeProductValueService.updateByPrimaryKeySelective(bean);

        amazonFormLogService.addLog(optional.get(), bean, (log) ->{
            log.setRelationId(bean.getId());
            log.setOperationType(AmazonFormOperationType.UPDATE.name());
            log.setModuleType(AmazonFormModuleType.TYPE_PRODUCT_TYPE_VALUE_CONF.name());
        }, null);
        return ApiResult.newSuccess();
    }

    private final String[] headers = {"分类站点","英文分类路径","属性1","属性1属性值"};

    /**
     * 导入
     * @param request
     * @return
     */
    @PostMapping(value = "/importData")
    public ApiResult<?> importData(HttpServletRequest request) {
        MultipartFile file;
        MultipartHttpServletRequest multiRequest = (MultipartHttpServletRequest) request;
        Map fileMap = multiRequest.getFileMap();
        if (fileMap.values().size() > 0) {
            file = (MultipartFile) fileMap.values().iterator().next();
        } else {
            return ApiResult.newError("请先上传文件");
        }
        String userName = WebUtils.getUserName();
        Timestamp now = new Timestamp(System.currentTimeMillis());

        // "站点","英文分类路径","属性1",“属性1属性值”,“属性2”,“属性2属性值”.....
        ResultModel<AmazonFormTypeProductValue> resultModel;
        Map<String, String> msgMap = new HashMap<>();
        try {
            resultModel = POIUtils.readExcelSheet1(headers, file, (row) -> {
                if(row == null){
                    return null;
                }
                AmazonFormTypeProductValue bean = new AmazonFormTypeProductValue();
                try {
                    bean.setSite(ExcelUtils.getCellValue(row.getCell(0)).trim().toUpperCase());
                    bean.setTypePathEn(ExcelUtils.getCellValue(row.getCell(1)).trim());

                    // 返回最后一列列数 既总列数不用减一
                    short lastCell = row.getLastCellNum();
                    Map<String, String> defaultValueMap = new HashMap<>();

                    for (int i = 2 ; lastCell > 2 && i + 1 < lastCell ; i = i + 2) {
                        String key = ExcelUtils.getCellValue(row.getCell(i)).trim();
                        String value = ExcelUtils.getCellValue(row.getCell(i + 1)).trim();
                        if(StringUtils.isNotBlank(key) && StringUtils.isNotBlank(value)) {
                            defaultValueMap.put(key, value);
                        }
                    }
                    bean.setDefaultValue(JSON.toJSONString(defaultValueMap));

                    String msg = CommonCheckColumn.checkFieldReturnMsg(bean);
                    //不合格数据
                    if(StringUtils.isNotBlank(msg)){
                        msgMap.put(String.format("第%s行", row.getRowNum()+1), msg);
                        return null;
                    }
                    if(!"US".equalsIgnoreCase(bean.getSite())){
                        msgMap.put(String.format("第%s行", row.getRowNum()+1), "不是US站点");
                        return null;
                    }

                    bean.setEnable(true);
                    bean.setCreateBy(userName);
                    bean.setCreateTime(now);
                    bean.setUpdateBy(userName);
                    bean.setUpdateTime(now);
                    return bean;
                }catch (Exception e){
                    log.error(String.format("解析row %s,错误：", row.getRowNum()), e);
                }
                return null;
                }, false);

        } catch (IOException e) {
            log.error("导入出错：", e);
            return ApiResult.newError("导入出错："+ e.getMessage());
        }

        if(!resultModel.isSuccess()){
            return ApiResult.newError("导入失败："+ resultModel.getMsg());
        }

        List<AmazonFormTypeProductValue> list = resultModel.getList();
        if(CollectionUtils.isEmpty(list)){
            return ApiResult.newError("导入数据为空！");
        }

        list = list.stream().filter(o -> o != null).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(list)){
            ApiResult<Object> apiResult = ApiResult.newError("导入数据过滤后为空！");
            apiResult.setResult(msgMap);
            return apiResult;
        }

        if(list.size() > 100000){
            return ApiResult.newError("导入数据不要超过100000！");
        }

        //先新增 再修改
        List<List<AmazonFormTypeProductValue>> pagingList = PagingUtils.pagingList(list, 10000);
        for (List<AmazonFormTypeProductValue> upList : pagingList) {
            amazonFormTypeProductValueService.batchInsert(upList);
            amazonFormTypeProductValueService.batchUpdateBySiteAndPath(upList);
        }

        //log
        amazonFormLogService.uploadFileLog(file, msgMap, (log) -> {
            log.setModuleType(AmazonFormModuleType.TYPE_PRODUCT_TYPE_VALUE_CONF.name());
        }, null);

        return ApiResult.newSuccess();
    }
}
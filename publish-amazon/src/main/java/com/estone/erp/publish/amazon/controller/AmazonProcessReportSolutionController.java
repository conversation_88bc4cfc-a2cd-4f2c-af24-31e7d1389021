package com.estone.erp.publish.amazon.controller;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.aliyuncs.utils.IOUtils;
import com.estone.erp.common.model.HttpParams;
import com.estone.erp.common.model.api.ApiRequestParam;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.model.api.CQuery;
import com.estone.erp.common.model.api.CQueryResult;
import com.estone.erp.common.util.Asserts;
import com.estone.erp.common.util.CommonCheckColumn;
import com.estone.erp.publish.amazon.model.AmazonProcessReportSolution;
import com.estone.erp.publish.amazon.model.AmazonProcessReportSolutionCriteria;
import com.estone.erp.publish.amazon.model.AmazonProcessReportSolutionExample;
import com.estone.erp.publish.amazon.model.request.ReportSolutionResponseDO;
import com.estone.erp.publish.amazon.service.AmazonProcessReportSolutionService;
import com.estone.erp.publish.amazon.util.AmazonReportSolutionUtil;
import com.estone.erp.publish.common.ErrorCode;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.common.util.CacheUtils;
import com.estone.erp.publish.common.util.HttpUtils;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.http.HttpMethod;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR> amazon_process_report_solution
 * 2019-11-26 11:47:20
 */
@Slf4j
@RestController
@RequestMapping("amazonProcessReportSolution")
public class AmazonProcessReportSolutionController {
    @Resource
    private AmazonProcessReportSolutionService amazonProcessReportSolutionService;

    @PostMapping
    public ApiResult<?> postAmazonProcessReportSolution(@RequestBody ApiRequestParam<String> requestParam) {
        String method = requestParam.getMethod();
        if (StringUtils.isNotBlank(method)) {
            switch (method) {
                case "searchAmazonProcessReportSolution": // 查询列表
                    CQuery<AmazonProcessReportSolutionCriteria> cquery = requestParam.getArgsValue(new TypeReference<CQuery<AmazonProcessReportSolutionCriteria>>() {});
                    Asserts.isTrue(cquery != null, ErrorCode.PARAM_EMPTY_ERROR);
                    CQueryResult<AmazonProcessReportSolution> results = amazonProcessReportSolutionService.search(cquery);
                    return results;
                case "addAmazonProcessReportSolution": // 添加
                    AmazonProcessReportSolution amazonProcessReportSolution = requestParam.getArgsValue(new TypeReference<AmazonProcessReportSolution>() {});
                    String msg = CommonCheckColumn.checkFieldReturnMsg(amazonProcessReportSolution);
                    if(StringUtils.isNotBlank(msg)){
                        return ApiResult.newError(msg);
                    }
                    //验证报告code是否重复
                    if(StringUtils.isNotBlank(amazonProcessReportSolution.getReportCode())){
                        amazonProcessReportSolution.setReportCode(amazonProcessReportSolution.getReportCode().replaceAll(" ",""));
                        AmazonProcessReportSolutionExample example = new AmazonProcessReportSolutionExample();
                        example.createCriteria().andReportCodeEqualTo(amazonProcessReportSolution.getReportCode());
                        List<AmazonProcessReportSolution> existData = amazonProcessReportSolutionService.selectByExample(example);
                        if(CollectionUtils.isNotEmpty(existData)){
                            return ApiResult.newError(String.format("报告code：%s，已存在，不能重复添加", amazonProcessReportSolution.getReportCode()));
                        }
                    }

                    amazonProcessReportSolutionService.insert(amazonProcessReportSolution);
                    return ApiResult.newSuccess(amazonProcessReportSolution);
                }
        }
        return ApiResult.newSuccess();
    }

    @GetMapping(value = "/{id}")
    public ApiResult<?> getAmazonProcessReportSolution(@PathVariable(value = "id") Integer id) {
        AmazonProcessReportSolution amazonProcessReportSolution = amazonProcessReportSolutionService.selectByPrimaryKey(id);
        return ApiResult.newSuccess(amazonProcessReportSolution);
    }

    @PutMapping(value = "/update")
    public ApiResult<?> putAmazonProcessReportSolution(@RequestBody ApiRequestParam<String> requestParam) {
        AmazonProcessReportSolution amazonProcessReportSolution = requestParam.getArgsValue(new TypeReference<AmazonProcessReportSolution>() {});
        String msg = CommonCheckColumn.checkFieldReturnMsg(amazonProcessReportSolution);
        if(StringUtils.isNotBlank(msg)){
            return ApiResult.newError(msg);
        }
        amazonProcessReportSolutionService.updateByPrimaryKeySelective(amazonProcessReportSolution);
        return ApiResult.newSuccess(amazonProcessReportSolution);
    }

    @DeleteMapping(value = "/delete")
    public ApiResult<?> deleteAmazonProcessReportSolution(@RequestBody ApiRequestParam<String> requestParam) {
        List<Integer> ids = requestParam.getArgsValue(new TypeReference<List<Integer>>() {});
        if(CollectionUtils.isEmpty(ids)){
            return ApiResult.newError("id为空");
        }
        int count = amazonProcessReportSolutionService.deleteByPrimaryKey(ids);
        return ApiResult.newSuccess();
    }

    @PostMapping(value = "/solution")
    public ApiResult<?> solution(@RequestBody ApiRequestParam<String> requestParam) {
        String args = requestParam.getArgs();
        if(StringUtils.isBlank(args)){
            return  ApiResult.newSuccess();
        }

        JSONObject param = new JSONObject();
        param.put("report_describe", Lists.newArrayList(args));

        HttpParams<String> httpParams = new HttpParams<>();
        String systemParamValue = CacheUtils.getSystemParamValue(SaleChannel.CHANNEL_AMAZON, "AMAZON", "AMAZON_REPORT", 60);
        //httpParams.setUrl(CacheUtils.SystemParamGet("AMAZON.AMAZON_REPORT").getParamValue());
        httpParams.setUrl(systemParamValue);
        httpParams.setHttpMethod(HttpMethod.POST);
        httpParams.setBody(param.toJSONString());
        ReportSolutionResponseDO solutionResponse = HttpUtils.exchange(httpParams, ReportSolutionResponseDO.class);
        if (solutionResponse != null) {
            return ApiResult.newSuccess(solutionResponse.getReportSolution());
        }
        return ApiResult.newSuccess();
    }

    @PostMapping(value = "/reportSolution")
    public ApiResult<?> reportSolution(@RequestBody ApiRequestParam<String> requestParam) {
        String args = requestParam.getArgs();
        if(StringUtils.isBlank(args)){
            return  ApiResult.newSuccess();
        }
        ApiResult<ReportSolutionResponseDO> apiResult = AmazonReportSolutionUtil.matchReportSolution(List.of(args));
        if (!apiResult.isSuccess()) {
            return ApiResult.newError(apiResult.getErrorMsg());
        }
        return ApiResult.newSuccess(apiResult.getResult().getResponse());
    }

    /**
     * 获取所有问题类型
     */
    @GetMapping("allSolutionType")
    public ApiResult<List<String>> getAllSolutionType() {
        List<String> allSolutionType = amazonProcessReportSolutionService.getAllSolutionType();
        if (CollectionUtils.isEmpty(allSolutionType)) {
            return ApiResult.newSuccess(Collections.emptyList());
        }
        return ApiResult.newSuccess(allSolutionType);
    }

    /**
     * 获取所有错误类型
     */
    @GetMapping("getAllErrorType")
    public ApiResult<List<String>> getAllErrorType() {
        List<String> allSolutionType = amazonProcessReportSolutionService.getAllErroeType();
        if (CollectionUtils.isEmpty(allSolutionType)) {
            return ApiResult.newSuccess(Collections.emptyList());
        }
        return ApiResult.newSuccess(allSolutionType);
    }

    /**
     * 导出
     * @param response
     * @throws IOException
     */
    @PostMapping("/download")
    public void download(HttpServletResponse response) throws IOException {
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");
        String fileName = "amazon-process-report" + System.currentTimeMillis() + ".xls";
        response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fileName, StandardCharsets.UTF_8));

        OutputStream out = null;
        int page = 0;
        int limit = 500;
        try {
            ExcelWriter excelWriter = null;
            WriteSheet writeSheet = EasyExcel.writerSheet("sheet").build();
            AmazonProcessReportSolutionExample example = new AmazonProcessReportSolutionExample();
            while (true) {
                example.setLimit(limit);
                example.setOffset((page ++) * limit);
                List<AmazonProcessReportSolution> amazonProcessReportSolutions = amazonProcessReportSolutionService.selectByExample(example);
                if (CollectionUtils.isEmpty(amazonProcessReportSolutions)) {
                    break;
                }

                if (excelWriter == null) {
                    out = response.getOutputStream();
                    excelWriter = EasyExcel.write(out, AmazonProcessReportSolution.class).build();
                }
                excelWriter.write(amazonProcessReportSolutions, writeSheet);
            }
            if (null != excelWriter) {
                excelWriter.finish();
            }
        } catch (Exception e) {
            response.setContentType("application/json");
            response.setCharacterEncoding("utf-8");
            response.getWriter().println(JSON.toJSONString(ApiResult.newError(e.getMessage())));
        } finally {
            IOUtils.closeQuietly(out);
        }
    }
}
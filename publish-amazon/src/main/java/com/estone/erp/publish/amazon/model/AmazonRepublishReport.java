package com.estone.erp.publish.amazon.model;

import lombok.Data;

import java.io.Serializable;
import java.sql.Timestamp;

@Data
public class AmazonRepublishReport implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     *  database column amazon_republish_report.id
     */
    private Integer id;

    /**
     * 模板编号 database column amazon_republish_report.template_id
     */
    private Integer templateId;

    /**
     * 问题类型 database column amazon_republish_report.report_solution_type
     */
    private String reportSolutionType;

    /**
     * 错误解决方案id database column amazon_republish_report.report_solution_id
     */
    private Integer reportSolutionId;

    /**
     * 重试时间 database column amazon_republish_report.retry_time
     */
    private Timestamp retryTime;
}
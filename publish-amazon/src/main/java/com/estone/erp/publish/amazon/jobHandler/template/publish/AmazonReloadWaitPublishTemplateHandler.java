package com.estone.erp.publish.amazon.jobHandler.template.publish;

import com.alibaba.fastjson.JSON;
import com.estone.erp.common.jobHandler.AbstractJobHandler;
import com.estone.erp.common.mq.RabbitMqSender;
import com.estone.erp.common.util.LocalDateTimeUtil;
import com.estone.erp.publish.amazon.bo.AmazonTemplateBO;
import com.estone.erp.publish.amazon.enums.AmaoznPublishStatusEnum;
import com.estone.erp.publish.amazon.enums.PublishTypeEnum;
import com.estone.erp.publish.amazon.mapper.AmazonTemplateMapper;
import com.estone.erp.publish.amazon.model.AmazonTemplate;
import com.estone.erp.publish.amazon.model.AmazonTemplateExample;
import com.estone.erp.publish.amazon.mq.AmazonQueues;
import com.estone.erp.publish.mq.PublishMqConfig;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 失败待刊登模板重刊登
 * <AUTHOR>
 * @date 2023-06-13 17:47
 */
@Slf4j
@Component
public class AmazonReloadWaitPublishTemplateHandler extends AbstractJobHandler {
    @Resource
    private AmazonTemplateMapper amazonTemplateMapper;
    @Autowired
    private RabbitMqSender rabbitMqSender;

    public AmazonReloadWaitPublishTemplateHandler() {
        super(AmazonReloadWaitPublishTemplateHandler.class.getName());
    }

    @Data
    public static class InnerParam {

        private List<Integer> templateIds;
        /**
         * 大于几小时前
         */
        private Integer greaterThanHours;
        /**
         * 小于几小时
         */
        private Integer lessThanHours;
    }

    @Override
    @XxlJob("AmazonReloadWaitPublishTemplateHandler")
    public ReturnT<String> run(String param) throws Exception {
        InnerParam innerParam = passParam(param, InnerParam.class);
        Date starDate = null;
        Date endDate = null;
        if (innerParam.getGreaterThanHours() != null && innerParam.getLessThanHours() != null) {
            LocalDateTime now = LocalDateTime.now();
            LocalDateTime starLocalDateTime = now.minusHours(innerParam.getGreaterThanHours());
            LocalDateTime endLocalDateTime = now.minusHours(innerParam.getLessThanHours());
            XxlJobLogger.log("{}-{}",starLocalDateTime, endLocalDateTime);
            starDate = LocalDateTimeUtil.passLocalDateTimeToDate(starLocalDateTime);
            endDate = LocalDateTimeUtil.passLocalDateTimeToDate(endLocalDateTime);
        }

        int startId = 0;
        int total = 0;
        while (true) {
            AmazonTemplateExample example = new AmazonTemplateExample();
            AmazonTemplateExample.Criteria criteria = example.createCriteria();
            if (CollectionUtils.isNotEmpty(innerParam.getTemplateIds())) {
                criteria.andIdIn(innerParam.getTemplateIds());
            }
            if (starDate != null) {
                criteria.andCreationDateBetween(starDate, endDate);
            }
            criteria.andIdGreaterThan(startId);
            criteria.andTitleIsNull();
            criteria.andPublishRoleEqualTo(1);
            criteria.andIsLockEqualTo(Boolean.FALSE);
            criteria.andPublishTypeIn(List.of(PublishTypeEnum.AUTO_PUBLISH_TEMPLATE.getCode(), PublishTypeEnum.AUTO_PUBLISH_TEMPLATE_TIME.getCode()));
            criteria.andPublishStatusEqualTo(AmaoznPublishStatusEnum.WAIT_PUBLISH.getStatusCode());
            example.setLimit(1000);
            example.setColumns("id, seller_id");
            List<AmazonTemplateBO> amazonTemplates = amazonTemplateMapper.selectFiledColumnsByExample(example);
            if (CollectionUtils.isEmpty(amazonTemplates)) {
                break;
            }
            total += amazonTemplates.size();
            AmazonTemplate amazonTemplate = amazonTemplates.get(amazonTemplates.size() - 1);
            if (amazonTemplate != null) {
                startId = amazonTemplate.getId();
            }
            Map<String, List<AmazonTemplateBO>> accountTemplateMap = amazonTemplates.stream().collect(Collectors.groupingBy(AmazonTemplate::getSellerId));
            accountTemplateMap.forEach((k, v) -> {
                List<Integer> ids = v.stream().map(AmazonTemplate::getId).collect(Collectors.toList());
                rabbitMqSender.publishSyncVHostRabbitTemplateSend(PublishMqConfig.AMAZON_API_DIRECT_EXCHANGE, AmazonQueues.AMAZON_REPUBLISH_WAIT_TEMPLATE_KEY, JSON.toJSON(ids));
            });

        }
        XxlJobLogger.log("size:{}", total);
        return ReturnT.SUCCESS;
    }
}

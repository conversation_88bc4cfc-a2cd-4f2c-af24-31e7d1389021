package com.estone.erp.publish.system.recommendProduct.service.impl;

import com.estone.erp.common.annotation.NeedToLog;
import com.estone.erp.common.model.api.CQuery;
import com.estone.erp.common.model.api.CQueryResult;
import com.estone.erp.common.util.StrUtil;
import com.estone.erp.common.util.WebUtils;
import com.estone.erp.publish.platform.model.OperateLog;
import com.estone.erp.publish.platform.service.OperateLogService;
import com.estone.erp.publish.system.recommendProduct.enums.RecommendTypeEnum;
import com.estone.erp.publish.system.recommendProduct.mapper.RecommendProductRuleMapper;
import com.estone.erp.publish.system.recommendProduct.model.RecommendProductRule;
import com.estone.erp.publish.system.recommendProduct.model.RecommendProductRuleCriteria;
import com.estone.erp.publish.system.recommendProduct.model.RecommendProductRuleExample;
import com.estone.erp.publish.system.recommendProduct.service.RecommendProductRuleService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.lang.reflect.Field;
import java.sql.Timestamp;
import java.util.List;
import java.util.stream.Collectors;

/**
 * recommend_product_rule
 * 2022-12-27 16:36:03
 */
@Service("recommendProductRuleService")
@Slf4j
public class RecommendProductRuleServiceImpl implements RecommendProductRuleService {
    @Resource
    private RecommendProductRuleMapper recommendProductRuleMapper;
    @Resource
    private OperateLogService operateLogService;

    @Override
    public int countByExample(RecommendProductRuleExample example) {
        Assert.notNull(example, "example is null!");
        return recommendProductRuleMapper.countByExample(example);
    }

    @Override
    public CQueryResult<RecommendProductRule> search(CQuery<RecommendProductRuleCriteria> cquery) {
        Assert.notNull(cquery, "cquery is null!");
        RecommendProductRuleCriteria query = cquery.getSearch();
        RecommendProductRuleExample example = query.getExample();
        example.setOrderByClause("creation_date desc");
        long total = 0;
        int totalPages = 0;
        // 是否分页
        if (cquery.isPageReqired()) {
            total = recommendProductRuleMapper.countByExample(example);
            totalPages = (int) Math.ceil((double) total / cquery.getLimit());
            example.setLimit(cquery.getLimit());
            example.setOffset(cquery.getOffset());
        }
        List<RecommendProductRule> recommendProductRules = recommendProductRuleMapper.selectByExample(example);
        // 组装结果
        CQueryResult<RecommendProductRule> result = new CQueryResult<>();
        result.setTotal(total);
        result.setTotalPages(totalPages);
        result.setRows(recommendProductRules);
        return result;
    }

    @Override
    public RecommendProductRule selectByPrimaryKey(Integer id) {
        Assert.notNull(id, "id is null!");
        return recommendProductRuleMapper.selectByPrimaryKey(id);
    }

    @Override
    public List<RecommendProductRule> selectByExample(RecommendProductRuleExample example) {
        Assert.notNull(example, "example is null!");
        return recommendProductRuleMapper.selectByExample(example);
    }

    @Override
    public int insert(RecommendProductRule record) {
        Assert.notNull(record, "record is null!");
        // 默认加时间和人
        record.setCreationDate(new Timestamp(System.currentTimeMillis()));
        record.setCreatedBy(StringUtils.isNotBlank(record.getCreatedBy()) ? record.getCreatedBy() : WebUtils.getUserName());
        record.setLastUpdateDate(new Timestamp(System.currentTimeMillis()));
        record.setLastUpdatedBy(StringUtils.isNotBlank(record.getLastUpdatedBy()) ? record.getLastUpdatedBy() : WebUtils.getUserName());
        biudRecommendProductRuleContent(record);
        return recommendProductRuleMapper.insert(record);
    }

    //处理数据，用于权限控制
    private static void biudRecommendProductRuleContent(RecommendProductRule recommendProductRule) {
        if(null == recommendProductRule) {
            return;
        }
        String targetPlatforms = StrUtil.strAddDelimiter(recommendProductRule.getTargetPlatforms(),",");
        if (recommendProductRule.getRecommendType().equalsIgnoreCase(RecommendTypeEnum.PLATFORM_MUTUAL_RECOMMENDATION.getStatusMsgCn()) &&  StringUtils.isNotBlank(recommendProductRule.getPlatformMarketRelation())){
            List<RecommendProductRule.PlaformMarketData> plaformMarketDataList = recommendProductRule.getPlaformSiteDataList();
            List<String> targetPlatformList = plaformMarketDataList.stream().map(o -> o.getTargetPlatform()).collect(Collectors.toList());
            targetPlatforms = StrUtil.strAddDelimiter(StringUtils.join(targetPlatformList, ","), ",");
        }else if (recommendProductRule.getRecommendType().equalsIgnoreCase(RecommendTypeEnum.PLATFORM_RECOMMENDATION.getStatusMsgCn()) && StringUtils.isNotBlank(recommendProductRule.getApplyPlatform())){
            targetPlatforms = StrUtil.strAddDelimiter(recommendProductRule.getApplyPlatform(),",");
        }
        recommendProductRule.setTargetPlatforms(targetPlatforms);
    }


    @Override
    public int updateByPrimaryKeySelective(RecommendProductRule record) {
        Assert.notNull(record, "record is null!");
        // 默认加修改时间和修改人
        record.setLastUpdateDate(new Timestamp(System.currentTimeMillis()));
        record.setLastUpdatedBy(StringUtils.isNotBlank(record.getLastUpdatedBy()) ? record.getLastUpdatedBy() : WebUtils.getUserName());
        biudRecommendProductRuleContent(record);
        return recommendProductRuleMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public int updateByExampleSelective(RecommendProductRule record, RecommendProductRuleExample example) {
        Assert.notNull(record, "record is null!");
        // 默认加修改时间和修改人
        record.setLastUpdateDate(new Timestamp(System.currentTimeMillis()));
        record.setLastUpdatedBy(StringUtils.isNotBlank(record.getLastUpdatedBy()) ? record.getLastUpdatedBy() : WebUtils.getUserName());
        biudRecommendProductRuleContent(record);
        return recommendProductRuleMapper.updateByExampleSelective(record, example);
    }

    @Override
    public int deleteByPrimaryKey(List<Integer> ids) {
        Assert.notNull(ids, "ids is null!");
        return recommendProductRuleMapper.deleteByPrimaryKey(ids);
    }

    @Override
    public RecommendProductRule selectByUnique(String ruleName){
        return recommendProductRuleMapper.selectByUnique(ruleName);
    }

    @Override
    public void generateConfigLog(RecommendProductRule updateRecommendProductRule, String userName) {
        if(null == updateRecommendProductRule || null == updateRecommendProductRule.getId()) {
            return;
        }
        RecommendProductRule dbRecommendProductRule = this.selectByPrimaryKey(updateRecommendProductRule.getId());
        if(null == dbRecommendProductRule) {
            return;
        }

        try {
            Class<RecommendProductRule> clazz = RecommendProductRule.class;
            Field[] fields = clazz.getDeclaredFields();
            for (Field field : fields) {
                if(!field.isAnnotationPresent(NeedToLog.class)) {
                    continue;
                }

                field.setAccessible(true);
                //只对非空的进行设置 改前改后不一致才记录日志
                if (field.get(updateRecommendProductRule) == null || field.get(updateRecommendProductRule).equals(field.get(dbRecommendProductRule))) {
                    continue;
                }
                OperateLog operateLog = new OperateLog();
                operateLog.setType("recommend_product_rule");
                operateLog.setBusinessId(updateRecommendProductRule.getId().toString());
                operateLog.setFieldName(field.getName());
                String beforeValue = field.get(dbRecommendProductRule) == null ? "" : field.get(dbRecommendProductRule).toString();
                operateLog.setBefore(beforeValue);
                operateLog.setAfter(field.get(updateRecommendProductRule).toString());
                operateLogService.insert(operateLog);
            }
        }catch (Exception e) {
            log.error("构建日志报错" + e.getMessage(), e);
        }
    }
}
package com.estone.erp.publish.system.newpms;

import com.estone.erp.common.constant.CacheConstant;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.util.SpringUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Slf4j
@Component
public class NewPmsUtils {


    private static NewPmsClient newPmsClient;

    @PostConstruct
    private void init() {
        try {
            newPmsClient = SpringUtils.getBean(NewPmsClient.class);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }

    public static ApiResult<List<String>> getAllNnSkuList() {
        return newPmsClient.getAllNnSkuList();
    }

    public static ApiResult<List<String>> getAllNnSkuLists(int retryTimes) {
        ApiResult<List<String>> apiResult = ApiResult.newError("");
        int retryTime = 0;
        while(retryTime < retryTimes && !apiResult.isSuccess()) {
            try{
                if(retryTime != 0) {
                    Thread.sleep(1 * 1000);
                }
                retryTime++;
                apiResult = getAllNnSkuList();
            }catch (Exception e){
                log.error("获取采购建议数据所属仓库是南宁仓且备货为是的sku数据异常失败，{}", e.getMessage());
                apiResult = ApiResult.newError("获取采购建议数据所属仓库是南宁仓且备货为是的sku数据异常" + e.getMessage());
            }
        }
        return apiResult;
    }

    // 补充一个缓存的方案，供列表查询
    public static ApiResult<List<String>> getAllNnSkuLists(boolean selectCacahe) {
        if (selectCacahe) {
            Set<String> allNnSkuStockUpList = CacheConstant.CACHE_ALL_NN_SKU_STOCK_UP_LIST_CACHE.getIfPresent(CacheConstant.CACHE_ALL_NN_SKU_STOCK_UP_LIST_KEY);
            if (CollectionUtils.isEmpty(allNnSkuStockUpList)) {
                ApiResult<List<String>> result = getAllNnSkuLists(3);
                if (result.isSuccess()) {
                    allNnSkuStockUpList = result.getResult().stream().collect(Collectors.toSet());
                    CacheConstant.CACHE_ALL_NN_SKU_STOCK_UP_LIST_CACHE.put(CacheConstant.CACHE_ALL_NN_SKU_STOCK_UP_LIST_KEY, allNnSkuStockUpList);
                    return result;
                }
            }else {
                return ApiResult.newSuccess(new ArrayList<>(allNnSkuStockUpList));
            }
        }
        return getAllNnSkuLists(3);
    }

}

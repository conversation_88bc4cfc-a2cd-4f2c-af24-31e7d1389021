package com.estone.erp.publish.platform.enums;

/**
 * 模板五点描述过滤内容
 * <AUTHOR>
 * @date 2022/3/1 11:45
 */
public enum BulletPointFilterEnum {
    FIELD("FIELD","100% brand new and high quality"),
    UPPERCASE_FIELD("UPPERCASE_FIELD","100% Brand new and high quality"),
    FIELD_WITH_FULL_STOP("FIELD_WITH_FULL_STOP","100% brand new and high quality."),
    UPPERCASE_FIELD_WITH_FULL_STOP("UPPERCASE_FIELD_WITH_FULL_STOP","100% Brand new and high quality."),
    ORIGINAL_FIELD("ORIGINAL_FIELD", "original brand new and high quality."),
    FIELD_NO_100("FIELD_NO_100","brand new and high quality"),
    UPPERCASE_FIELD_NO_100("UPPERCASE_FIELD_NO_100","Brand new and high quality"),
    FIELD_WITH_FULL_STOP_NO_100("FIELD_WITH_FULL_STOP_NO_100","brand new and high quality."),
    UPPERCASE_FIELD_WITH_FULL_STOP_NO_100("UPPERCASE_FIELD_WITH_FULL_STOP_NO_100","Brand new and high quality."),
    ORIGINAL_FIELD_NO_100("ORIGINAL_FIELD_NO_100", "original brand new and high quality.");

    private String code;
    private String name;

    private BulletPointFilterEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }
}

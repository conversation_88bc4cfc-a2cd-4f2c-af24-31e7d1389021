package com.estone.erp.publish.platform.model;

import java.io.Serializable;
import java.sql.Timestamp;
import lombok.Data;

@Data
public class DrainageSku implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键 database column drainage_sku.id
     */
    private Long id;

    /**
     * 平台 database column drainage_sku.platform
     */
    private String platform;

    /**
     * 账号 database column drainage_sku.account_number
     */
    private String accountNumber;

    /**
     * 子SKU database column drainage_sku.sku
     */
    private String sku;

    /**
     * 是否引流SKU database column drainage_sku.is_drainage
     */
    private Boolean isDrainage;

    /**
     * 创建人 database column drainage_sku.create_by
     */
    private String createBy;

    /**
     * 创建时间 database column drainage_sku.create_date
     */
    private Timestamp createDate;

    /**
     * 修改人 database column drainage_sku.update_by
     */
    private String updateBy;

    /**
     * 修改时间 database column drainage_sku.update_date
     */
    private Timestamp updateDate;
}
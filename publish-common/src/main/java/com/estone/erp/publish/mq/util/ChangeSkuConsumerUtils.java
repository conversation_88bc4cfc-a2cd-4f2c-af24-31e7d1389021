package com.estone.erp.publish.mq.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.estone.erp.common.util.PublishRedisClusterUtils;
import com.estone.erp.common.util.SpringUtils;
import com.estone.erp.publish.mq.bean.ChangeSku;
import com.estone.erp.publish.platform.model.ChangeSkuLog;
import com.estone.erp.publish.platform.service.ChangeSkuLogService;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.amqp.core.Message;

import java.io.IOException;
import java.nio.charset.StandardCharsets;

/**
 * 变更的sku消费工具类
 * <AUTHOR>
 * @date 2022/6/2 19:07
 */
@Slf4j
public class ChangeSkuConsumerUtils {

    /**
     * 消息确认并删除redis
     * @param channel
     * @param message
     * @throws IOException
     */
    public static void confirmAndDelete(Channel channel, Message message) throws IOException {
        channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);

        // 获取消息id
        String msgId = (String) message.getMessageProperties().getHeaders().get("msg-id");
        if (StringUtils.isBlank(msgId)) {
            return;
        }

        // redis查询是否有值 有值删除
        String value = PublishRedisClusterUtils.get(msgId);
        if (StringUtils.isNotBlank(value)) {
            PublishRedisClusterUtils.del(msgId);
        }
    }

    /**
     * 消息消费失败重试
     * @param channel
     * @param message
     * @param maxRetryCount
     * @param saleChannel
     * @throws IOException
     */
    public static void retry(Channel channel, Message message, int maxRetryCount, String saleChannel) throws IOException {
        // 获取消息id
        String msgId = (String) message.getMessageProperties().getHeaders().get("msg-id");
        if (StringUtils.isBlank(msgId)) {
            return;
        }

        // redis查询重试次数
        String value = PublishRedisClusterUtils.get(msgId);

        int retryCount;
        if (StringUtils.isBlank(value)) {
            retryCount = 0;
        } else {
            retryCount = Integer.valueOf(value);
        }

        if (retryCount >= maxRetryCount) {
            channel.basicNack(message.getMessageProperties().getDeliveryTag(), false, false);
            PublishRedisClusterUtils.del(msgId);

            // 达到最大重试次数 将消息记录到日志中
            String body = new String(message.getBody(), StandardCharsets.UTF_8);
            ChangeSku changeSku = JSON.parseObject(body, new TypeReference<ChangeSku>() {
            });
            if(null == changeSku) {
                return;
            }
            // 已存在日志id 不需要插入 如需要记录次数 可改为根据id修改
            if(null != changeSku.getLogId()) {
                return;
            }
            ChangeSkuLog changeSkuLog = new ChangeSkuLog();
            changeSkuLog.setSaleChannel(saleChannel);
            changeSkuLog.setStatus(0);
            changeSkuLog.setSkus(StringUtils.join(changeSku.getSkuList(), ","));
            changeSkuLog.setAccountNumbers(StringUtils.join(changeSku.getAccountNumberList(), ","));
            ChangeSkuLogService changeSkuLogService = SpringUtils.getBean(ChangeSkuLogService.class);
            changeSkuLogService.insert(changeSkuLog);
        } else {
            channel.basicNack(message.getMessageProperties().getDeliveryTag(), false, true);
            PublishRedisClusterUtils.set(msgId, retryCount + 1);
            log.info(String.format("%s变更的sku第%s次重试消费", saleChannel, retryCount + 1));
        }
    }
}

package com.estone.erp.publish.system.excel.service.impl;

import com.estone.erp.common.model.api.CQuery;
import com.estone.erp.common.model.api.CQueryResult;
import com.estone.erp.common.mq.RabbitMqSender;
import com.estone.erp.publish.component.PermissionsHelper;
import com.estone.erp.publish.platform.enums.SmallPlatformDownloadEnums;
import com.estone.erp.publish.system.excel.enums.ExcelDownloadPlatformEnums;
import com.estone.erp.publish.system.excel.mapper.ExcelDownloadLogMapper;
import com.estone.erp.publish.system.excel.model.ExcelDownloadLog;
import com.estone.erp.publish.system.excel.model.ExcelDownloadLogCriteria;
import com.estone.erp.publish.system.excel.model.ExcelDownloadLogExample;
import com.estone.erp.publish.system.excel.service.ExcelDownloadLogService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * 2024-07-23 16:05:10
 */
@Service("excelDownloadLogService")
@Slf4j
public class ExcelDownloadLogServiceImpl implements ExcelDownloadLogService {
    @Resource
    private ExcelDownloadLogMapper excelDownloadLogMapper;
    @Resource
    private RabbitMqSender rabbitMqSender;

    @Override
    public int countByExample(ExcelDownloadLogExample example) {
        Assert.notNull(example, "example is null!");
        return excelDownloadLogMapper.countByExample(example);
    }

    @Override
    public CQueryResult<ExcelDownloadLog> search(CQuery<ExcelDownloadLogCriteria> cquery) {
        Assert.notNull(cquery, "cquery is null!");
        ExcelDownloadLogCriteria query = cquery.getSearch();
        // 组装结果
        CQueryResult<ExcelDownloadLog> result = new CQueryResult<>();

        String platform = query.getPlatform();
        // 人员权限
        Pair<Boolean, List<String>> employeePair = PermissionsHelper.getDefaultOrAuthorEmployeePair(platform, query.getCreateBy(), query.getCreateByList(),
                query.getAccountNumber(), query.getAccountNumberList());
        if (BooleanUtils.isTrue(employeePair.getLeft())) {
            query.setCreateByList(employeePair.getRight());
        }

        ExcelDownloadLogExample example = query.getExample();
        example.setOrderByClause("create_time desc");

        long total = 0;
        int totalPages = 0;
        // 是否分页
        if (cquery.isPageReqired()) {
            total = excelDownloadLogMapper.countByExample(example);
            totalPages = (int) Math.ceil((double) total / cquery.getLimit());
            example.setLimit(cquery.getLimit());
            example.setOffset(cquery.getOffset());
        }
        List<ExcelDownloadLog> excelDownloadLogs = excelDownloadLogMapper.selectByExample(example);
        handleQueueUp(excelDownloadLogs, query.getPlatform());
        // 组装结果
        result.setTotal(total);
        result.setTotalPages(totalPages);
        result.setRows(excelDownloadLogs);
        return result;
    }

    private void handleQueueUp(List<ExcelDownloadLog> excelDownloadLogs, String platform) {
        if (CollectionUtils.isEmpty(excelDownloadLogs)) {
            return;
        }
        // 给排队中的产品排序
        ExcelDownloadLogExample orderByExample = new ExcelDownloadLogExample();
        orderByExample.createCriteria()
                .andStatusEqualTo(SmallPlatformDownloadEnums.Status.WAIT.getCode());
        orderByExample.setTableIndex(ExcelDownloadPlatformEnums.getPrefix(platform));
        orderByExample.setOrderByClause("id");
        orderByExample.setColumns("id, type");
        List<ExcelDownloadLog> orderByLogs = this.selectByExample(orderByExample);
        if (CollectionUtils.isNotEmpty(orderByLogs)) {
            Map<Integer, Integer> orderByMap = new HashMap<>();
            Map<String, List<ExcelDownloadLog>> typeMap = orderByLogs.stream().collect(Collectors.groupingBy(ExcelDownloadLog::getType));
            for (String type : typeMap.keySet()) {
                List<ExcelDownloadLog> allWaitList = typeMap.get(type);
                for (int i = 1; i <= allWaitList.size(); i++) {
                    ExcelDownloadLog excelLog = allWaitList.get(i - 1);
                    orderByMap.put(excelLog.getId(), i);
                }
            }
            // 设置排队数量
            for (ExcelDownloadLog excelDownloadLog : excelDownloadLogs) {
                excelDownloadLog.setQueueUp(orderByMap.get(excelDownloadLog.getId()));
            }
        }
    }

    @Override
    public ExcelDownloadLog selectByPrimaryKey(String platform, Integer id) {
        Assert.notNull(platform, "platform is null!");
        Assert.notNull(id, "id is null!");
        return excelDownloadLogMapper.selectByPrimaryKey(id, ExcelDownloadPlatformEnums.getPrefix(platform));
    }

    @Override
    public List<ExcelDownloadLog> selectByExample(ExcelDownloadLogExample example) {
        Assert.notNull(example, "example is null!");
        return excelDownloadLogMapper.selectByExample(example);
    }

    @Override
    public int insert(String platform, ExcelDownloadLog record) {
        Assert.notNull(record, "record is null!");
        // 默认加时间和人
        return excelDownloadLogMapper.insert(record, ExcelDownloadPlatformEnums.getPrefix(platform));
    }

    @Override
    public int updateByPrimaryKeySelective(String platform, ExcelDownloadLog record) {
        Assert.notNull(record, "record is null!");
        // 默认加修改时间和修改人
        return excelDownloadLogMapper.updateByPrimaryKeySelective(record, ExcelDownloadPlatformEnums.getPrefix(platform));
    }

    @Override
    public int updateByExampleSelective(ExcelDownloadLog record, ExcelDownloadLogExample example) {
        Assert.notNull(record, "record is null!");
        // 默认加修改时间和修改人
        return excelDownloadLogMapper.updateByExampleSelective(record, example);
    }

    @Override
    public int deleteByPrimaryKey(String platform, List<Integer> ids) {
        Assert.notNull(platform, "platform is null!");
        Assert.notNull(ids, "ids is null!");
        return excelDownloadLogMapper.deleteByPrimaryKey(ids, ExcelDownloadPlatformEnums.getPrefix(platform));
    }

    @Override
    public void addAndPushLog(ExcelDownloadLog downloadLog, String platform, String exchange, String routingKey) {
        int insert = excelDownloadLogMapper.insert(downloadLog, ExcelDownloadPlatformEnums.getPrefix(platform));
        if (insert > 0) {
            // 推送消息
            rabbitMqSender.allPublishVHostRabbitTemplateSend(exchange, routingKey, downloadLog.getId());
        }
    }
}
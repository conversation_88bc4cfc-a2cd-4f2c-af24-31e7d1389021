package com.estone.erp.publish.platform.mapper;

import com.estone.erp.publish.platform.model.SpuAccountPublishStatistics;
import com.estone.erp.publish.platform.model.SpuAccountPublishStatisticsExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface SpuAccountPublishStatisticsMapper {
    int countByExample(SpuAccountPublishStatisticsExample example);

    int deleteByPrimaryKey(List<Integer> ids);

    int deleteByExample(SpuAccountPublishStatisticsExample example);

    int insert(SpuAccountPublishStatistics record);

    SpuAccountPublishStatistics selectByPrimaryKey(Integer id);

    List<SpuAccountPublishStatistics> selectByExample(SpuAccountPublishStatisticsExample example);

    int updateByExampleSelective(@Param("record") SpuAccountPublishStatistics record, @Param("example") SpuAccountPublishStatisticsExample example);

    int updateByPrimaryKeySelective(SpuAccountPublishStatistics record);
}
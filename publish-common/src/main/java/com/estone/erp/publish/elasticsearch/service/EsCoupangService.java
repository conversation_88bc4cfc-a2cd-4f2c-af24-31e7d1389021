package com.estone.erp.publish.elasticsearch.service;

import com.estone.erp.common.model.PageResult;
import com.estone.erp.common.model.api.CQuery;
import com.estone.erp.publish.elasticsearch.model.ESCoupangItem;
import com.estone.erp.publish.elasticsearch.model.beanrequest.ESCoupangRequest;
import com.estone.erp.publish.feginService.modle.SkuPubilshListingFirstJoinTimeVo;

import java.util.Date;
import java.util.List;
import java.util.function.Consumer;

public interface EsCoupangService {
    PageResult<List<ESCoupangItem>> searchListing(CQuery<ESCoupangRequest> query);

    List<ESCoupangItem> searchItemListByIds(List<String> sellerProductIdList);

    /**
     * 根据选项Id查询商品
     * @param vendorItemIds vendorItemIds
     */
    List<ESCoupangItem> searchItemListByVendorItemIds(List<Long> vendorItemIds);

    ESCoupangItem getItemById(Long sellerProductId);

    void save(ESCoupangItem item);

    PageResult<List<ESCoupangItem>> searchItemPage(ESCoupangRequest query, Integer page, Integer limit);

    void deleteItem(ESCoupangItem item);

    /**
     * 按条件统计listing数量
     * @param query query
     * @return countNumber
     */
    Long countAccountListingByQuery(ESCoupangRequest query);

    List<ESCoupangItem> searchSkuItemListBySellerProductIds(List<Long> sellerProductIdList);

    List<ESCoupangItem> listItemByRequest(ESCoupangRequest request);

    /**
     * 获取店铺最近的同步时间
     * @param accountNumber
     * @return
     */
    Date getMaxSyncTime(String accountNumber);

    /**
     * 滚动查询执行任务
     *
     * @param request      查询条件
     * @param executorTask 任务
     * @return 查询结果数量
     */
    int scrollQueryExecutorTask(ESCoupangRequest request, Consumer<List<ESCoupangItem>> executorTask);

    /**
     * 获取sku商品第一次上架时间
     * @param articleNumberList
     * @return
     */
    List<SkuPubilshListingFirstJoinTimeVo> getCoupangFirstJoinTime(List<String> articleNumberList);
}

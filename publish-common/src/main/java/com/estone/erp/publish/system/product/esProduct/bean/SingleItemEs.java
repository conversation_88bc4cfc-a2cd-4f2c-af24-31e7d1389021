package com.estone.erp.publish.system.product.esProduct.bean;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.elasticsearch.annotations.Document;
import org.springframework.data.elasticsearch.annotations.Field;
import org.springframework.data.elasticsearch.annotations.FieldType;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Document(indexName = "singleitemes",type = "singleitemes")
@Data
public class SingleItemEs implements Serializable {

    private static final long serialVersionUID = 6528204871645610113L;

    @Id
    private String id;

    /**
     * 主sku
     */
    @Field(type = FieldType.Keyword)
    private String mainSku;

    /**
     * name
     */
    @Field(type = FieldType.Keyword)
    private String name;

    /**
     * 子sku
     */
    @Field(type = FieldType.Keyword)
    private String sonSku;

    /**
     * 单品状态7000草稿，7001待上架，7002正常,7003清仓，7004暂停，7005休假，7006甩卖，7007停产，7008存档
     */
    @Field(type = FieldType.Integer)
    private Integer itemStatus;

    /**
     |titleCn                              List<SingleItemOfficial> singleItemOfficials ->title language cn
     |desCn                                List<SingleItemOfficial> singleItemOfficials ->description language cn
     |titleEn                              List<SingleItemOfficial> singleItemOfficials->title language en
     |desEn                                List<SingleItemOfficial> singleItemOfficials->description language en

     |cnCustoms                           List<SingleItemOfficial> singleItemOfficials ->cnCustoms
     |enCustoms                           List<SingleItemOfficial> singleItemOfficials ->enCustoms

     |mustKeyword                         List<SingleItemOfficial> singleItemOfficials ->mustKeyword
     */
    @Field(type = FieldType.Nested)
    private List<SingleItemOfficial> singleItemOfficials;

    /**
     * 产品净重
     */
    @Field(type = FieldType.Float)
    private Double productWeight;

    /**
     * 预估包裹重量
     */
    private Double packageWeight;

    /**
     * 是否是季节性商品
     */
    private String isSeasonNew;


    /**
     * 长
     */
    @Field(type = FieldType.Float)
    private BigDecimal length;

    /**
     * 宽
     */
    @Field(type = FieldType.Float)
    private BigDecimal wide;

    /**
     * 高
     */
    @Field(type = FieldType.Float)
    private BigDecimal height;

    /**
     * 产品尺寸
     */
    @Field(type = FieldType.Keyword)
    private String sizeRemark;

    /**
     * |firstImage                           SingleItemPicInfo singleItemPicInfo->firstImage
     */
    @Field(type = FieldType.Nested)
    private SingleItemPicInfo singleItemPicInfo;

    /**
     * |packingWeigh                         packingMaterialsCode
     * 包材code
     */
    @Field(type = FieldType.Integer)
    private Integer packingMaterialsCode;

    /**
     * |matchPrice                           matchMaterialsCode
     * 搭配包材code
     */
    @Field(type = FieldType.Keyword)
    private String matchMaterialsCode;

    /**
     * 成本价
     */
    private BigDecimal cost;

    /**
     *
     */
    private Double shippingCost;

    /**
     * 销售成本价
     */
    @Field(type = FieldType.Float)
    private BigDecimal saleCost;

    /**
     * 上贴备注
     */
    @Field(type = FieldType.Keyword)
    private String postRemark;

    /**
     * 产品标签
     */
    @Field(type = FieldType.Keyword)
    private String tag;

    /**
     * |enTag                               tagCode产品标签code
     * 产品标签code
     */
    @Field(type = FieldType.Keyword)
    private String tagCode;

    /**
     * 单品来源 ：1.销售开发；2.pms；3.产品开发
     */
    @Field(type = FieldType.Integer)
    private Integer singleSource;

    /**
     * 产品类型0,单属性 1，多数性
     */
    @Field(type = FieldType.Integer)
    private Integer type;

    /**
     * 探雅json
     */
    @Field(type = FieldType.Keyword)
    private String tyJson;

    /**
     * 探雅json与子sku对应关系
     */
    @Field(type = FieldType.Keyword)
    private String sonSkus;

    /**
     * 禁售平台相关信息
     */
    @Field(type = FieldType.Keyword)
    private String salesProhibition;

    /**
     * 进入管理单品时间
     */
    //@Field(type = FieldType.Long)
    @JsonFormat(pattern ="yyyy-MM-dd HH:mm:ss")
    private Date inSingleTime;

    /**
     * 单品拓展信息
     */
    @Field(index = false)
    private SingleItemExternalInfo singleItemExternalInfo;

    @Field(index = false)
    private Category category;

    @Field(index = false)
    private List<SpecialGoods> specialGoodsList;

    @Field(index = false)
    private List<String> salesProbition;

    @Field(index = false)
    private String categoryPath;

    /**
     * 产品开发人员
     */
    @Field(type = FieldType.Text)
    private String devEmpName;

    /**
     * 促销状态 0 没有  1，是  2，否
     */
    private Integer promotion;

    /**
     * 是否新品
     */
    private Boolean newState;

    private Integer categoryId;

    /**
     * 产品录入时间
     */
    private Long createAt;

    /**
     * 单品状态修改的最后时间
     */
    @JsonFormat(pattern ="yyyy-MM-dd HH:mm:ss")
    private Date lastphasechangetime;

    private SingleItemAllSupplier singleItemAllSupplier;

    /**
     * 插头规格
     */
    @Field(type = FieldType.Keyword)
    private String plugSpecification;

    /**
     * 标准重量
     */
    private Double standardWeight;

    /**
     * 海关编码
     */
    @Field(type = FieldType.Text)
    private String customsCode;
}

package com.estone.erp.publish.elasticsearch.service.impl;

import com.estone.erp.publish.elasticsearch.model.beanresponse.SmtItemRankingDO;
import com.estone.erp.publish.elasticsearch.service.SmtProductRankingService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.data.elasticsearch.core.mapping.IndexCoordinates;

import java.time.LocalDate;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024-03-04 16:45
 */
@Slf4j
public class SmtProductRankingServiceImpl implements SmtProductRankingService {

    private final IndexCoordinates INDEXCOORDINATES = IndexCoordinates.of("smt_product_ranking");

//    @Resource
//    private RestHighLevelClient restHighLevelClient1;


//    @Override
//    public EsCompositeAggsResponse<SmtItemRankingDO> compositeProductIdRankingDetailSearch(String accountNumber, String starCrawlTimeDate, List<String> productIds, String searchAfter) {
//        EsCompositeAggsResponse<SmtItemRankingDO> compositeAggsResponse = new EsCompositeAggsResponse<>();
//
//        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
//        BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
//        boolQuery.filter(QueryBuilders.termQuery("accountName", accountNumber));
//        if (StringUtils.isNotBlank(starCrawlTimeDate)) {
//            boolQuery.filter(QueryBuilders.rangeQuery("updatedTime").gte(starCrawlTimeDate));
//        }
//        if (CollectionUtils.isNotEmpty(productIds)) {
//            boolQuery.filter(QueryBuilders.termsQuery("profuctID", productIds));
//        }
//
//        // 分组
//        CompositeAggregationBuilder compositeBuilder = new CompositeAggregationBuilder(
//                "item_group_pv_detail",
//                Collections.singletonList(new TermsValuesSourceBuilder("itemId").field("list_data.profuctID").order("desc"))
//        ).size(50);
//        if (StringUtils.isNotBlank(searchAfter)) {
//            compositeBuilder.aggregateAfter(ImmutableMap.of("itemId", searchAfter));
//        }
//
//        // 明细
//        TopHitsAggregationBuilder topHits = AggregationBuilders
//                .topHits("item_group_pv_detail")
//                .fetchSource(new String[]{
//                        "list_data.profuctID",
//                        "list_data.pageviews",
//                        "list_data.exposure",
//                        "list_data.timeframeUSATimeZone"}, null)
//                .sort("list_data.timeframeUSATimeZone", SortOrder.DESC)
//                .size(50);
//
//        compositeBuilder.subAggregation(topHits);
//
//        // 嵌套
//        NestedAggregationBuilder nestedAggregationBuilder = AggregationBuilders
//                .nested("item_pv_total", "list_data")
//                .subAggregation(compositeBuilder);
//        // 查询
//        searchSourceBuilder
//                .query(boolQuery)
//                .aggregation(nestedAggregationBuilder)
//                .size(0);
//
//        SearchRequest searchRequest = new SearchRequest(INDEXCOORDINATES.getIndexName());
//        searchRequest.source(searchSourceBuilder);
//        try {
//            SearchResponse search = restHighLevelClient1.search(searchRequest, RequestOptions.DEFAULT);
//            Aggregations aggregations = search.getAggregations();
//            if (aggregations == null) {
//                return compositeAggsResponse;
//            }
//            ParsedNested itemPvTotal = aggregations.get("item_pv_total");
//            if (itemPvTotal == null) {
//                return compositeAggsResponse;
//            }
//            CompositeAggregation itemGroupPvDetail = itemPvTotal.getAggregations().get("item_group_pv_detail");
//
//            List<SmtItemRankingDO> smtItemRankingDOS = itemGroupPvDetail.getBuckets().stream().map(bucket -> {
//                Aggregations bucketAggregations = bucket.getAggregations();
//                ParsedTopHits topHitsAggregations = bucketAggregations.get("item_group_pv_detail");
//                SearchHits hits = topHitsAggregations.getHits();
//                if (hits == null) {
//                    return null;
//                }
//                List<SmtItemRankingDO> rankingDOS = new ArrayList<>();
//                for (SearchHit hit : hits) {
//                    Map<String, Object> sourceAsMap = hit.getSourceAsMap();
//                    SmtItemRankingDO smtItemRankingDO = convertToSmtItemRankingDO(accountNumber, sourceAsMap);
//                    rankingDOS.add(smtItemRankingDO);
//                }
//                return rankingDOS;
//            }).filter(Objects::nonNull).flatMap(Collection::stream).collect(Collectors.toList());
//
//            if (itemGroupPvDetail.afterKey() != null) {
//                String afterKey = (String) itemGroupPvDetail.afterKey().get("itemId");
//                compositeAggsResponse.setSearchAfter(afterKey);
//            }
//            compositeAggsResponse.setDataList(smtItemRankingDOS);
//        } catch (IOException | RuntimeException e) {
//            log.error("search fail, query:{}, e:{}", searchSourceBuilder, e.getMessage(), e);
//        }
//        return compositeAggsResponse;
//    }


    private SmtItemRankingDO convertToSmtItemRankingDO(String accountNumber, Map<String, Object> sourceAsMap) {
        SmtItemRankingDO smtItemRankingDO = new SmtItemRankingDO();
        smtItemRankingDO.setAccountName(accountNumber);
        if (sourceAsMap.get("profuctID") != null) {
            smtItemRankingDO.setProfuctID((String) sourceAsMap.get("profuctID"));
        }
        if (sourceAsMap.get("pageviews") != null) {
            String pageviews = (String) sourceAsMap.get("pageviews");
            boolean creatable = NumberUtils.isCreatable(pageviews);
            if (creatable) {
                smtItemRankingDO.setPageviews(Integer.valueOf(pageviews));
            }
        }
        if (sourceAsMap.get("exposure") != null) {
            String exposure = (String) sourceAsMap.get("exposure");
            boolean creatable = NumberUtils.isCreatable(exposure);
            if (creatable) {
                smtItemRankingDO.setExposure(Integer.valueOf(exposure));
            }
        }
        if (sourceAsMap.get("timeframeUSATimeZone") != null) {
            String timeframeUSATimeZone = (String) sourceAsMap.get("timeframeUSATimeZone");
            try {
                smtItemRankingDO.setTimeframeUSATimeZone(LocalDate.parse(timeframeUSATimeZone));
            } catch (Exception e) {
                log.error("timeframeUSATimeZone parse fail, timeframeUSATimeZone:{}", timeframeUSATimeZone, e);
            }
        }
        return smtItemRankingDO;
    }
}

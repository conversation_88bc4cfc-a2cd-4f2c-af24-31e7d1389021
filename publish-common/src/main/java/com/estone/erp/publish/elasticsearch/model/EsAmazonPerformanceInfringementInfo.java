package com.estone.erp.publish.elasticsearch.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.elasticsearch.annotations.DateFormat;
import org.springframework.data.elasticsearch.annotations.Document;
import org.springframework.data.elasticsearch.annotations.Field;
import org.springframework.data.elasticsearch.annotations.FieldType;
import java.util.Date;

@Data
@Document(indexName = "amazon_performance_infringement_info", type = "esAmazonPerformanceInfringementInfo")
public class EsAmazonPerformanceInfringementInfo {


    @Id
    @Field(type = FieldType.Keyword)
    private String id;

    /**
     * 销售站点账号
     */
    @Field(type = FieldType.Keyword)
    private String saleAccount;

    /**
     * 站点
     */
    @Field(type = FieldType.Keyword)
    private String site;

    /**
     * asin码
     */
    @Field(type = FieldType.Keyword)
    private String asin;

    /**
     * sellerSku
     */
    @Field(type = FieldType.Keyword)
    private String sellerSku;


    /**
     * SKU
     */
    @Field(type = FieldType.Keyword)
    private String sku;

    /**
     * 侵权原因
     */
    @Field(type = FieldType.Keyword)
    private String cause;

    /**
     * 侵权时间
     */
    @Field(type = FieldType.Date, format = DateFormat.custom, pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date infringementTime;

    /**
     * 30天销量
     */
    @Field(type = FieldType.Long)
    private Integer order_last_30d_count;

    /**
     * 在线列表状态
     */
    @Field(type = FieldType.Keyword)
    private Boolean isOnline;

    /**
     * 禁售平台(逗号拼接) database column amazon_product_listing.forbidChannel
     */
    @Field(type = FieldType.Keyword)
    private String forbidChannel;

    /**
     * 确认状态
     */
    @Field(type = FieldType.Keyword)
    private Boolean confirmStatus;

    /**
     * 产品开发人员
     */
    @Field(type = FieldType.Keyword)
    private String productMan;


    /**
     * 侵权审核人员
     */
    @Field(type = FieldType.Keyword)
    private String infringementCheckMan;

    /**
     * 销售负责人
     */
    @Field(type = FieldType.Keyword)
    private String salesMan;

    /**
     * 销售组长
     */
    @Field(type = FieldType.Keyword)
    private String salesTeamLeader;

    /**
     * 销售主管
     */
    @Field(type = FieldType.Keyword)
    private String salesSupervisorName;

    /**
     * 爬取时间
     */
    @Field(type = FieldType.Date, format = DateFormat.custom, pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date getTime;

    /**
     * 确认人
     */
    @Field(type = FieldType.Keyword)
    private String confirmMan;

    /**
     * 确认时间
     */
    @Field(type = FieldType.Date, format = DateFormat.custom, pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date confirmTime;

    /**
     * 备注
     */
    @Field(type = FieldType.Keyword)
    private String remark;

}
package com.estone.erp.publish.elasticsearch2.model;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.fastjson.annotation.JSONField;
import com.estone.erp.publish.component.converter.ListStringFormatConverter;
import com.estone.erp.publish.system.infringement.response.InfringementWordInfo;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.elasticsearch.annotations.DateFormat;
import org.springframework.data.elasticsearch.annotations.Document;
import org.springframework.data.elasticsearch.annotations.Field;
import org.springframework.data.elasticsearch.annotations.FieldType;

import java.util.*;

@Data
@Document(indexName = "aliexpress_product_listing", type = "esAliexpressProductListing")
public class EsAliexpressProductListing{

    /**
     *  database column aliexpress_product.id
     */
    @Id
    @Field(type = FieldType.Keyword)
    private String id;

    /**
     * 速卖通帐号 database column aliexpress_product.aliexpress_account_number
     */
    @Field(type = FieldType.Keyword)
    private String aliexpressAccountNumber;

    /**
     * 产品ID database column aliexpress_product.product_id
     */
    @Field(type = FieldType.Long)
    private Long productId;

    /**
     * sku对应的单品货号 database column aliexpress_product.article_number
     */
    @Field(type = FieldType.Keyword)
    private String spu;

    /**
     * 刊登角色
     */
    @Field(type = FieldType.Integer)
    private Integer publishRole;

    /**
     * sku对应的单品货号 database column aliexpress_product.article_number
     */
    @Field(type = FieldType.Keyword)
    private String articleNumber;

    //新增字段
    /**
     * 无用，当初是copy amazon的字段
     */
    @Field(type = FieldType.Keyword)
    private String infringementWord;

    /**
     * 禁售平台(逗号拼接) database column amazon_product_listing.forbidChannel
     */
    @Field(type = FieldType.Keyword)
    private String forbidChannel;

    /**
     * 禁售类型
     */
    @ExcelProperty(value = "禁售类型", converter = ListStringFormatConverter.class)
    @Field(type = FieldType.Keyword)
    private List<String> infringementTypeNames;

    /**
     * 禁售原因
     */
    @ExcelProperty(value = "禁售原因", converter = ListStringFormatConverter.class)
    @Field(type = FieldType.Keyword)
    private List<String> infringementObjs;

    /**
     * 禁售站点
     */
    @ExcelProperty(value = "禁售站点", converter = ListStringFormatConverter.class)
    @Field(type = FieldType.Keyword)
    private List<String> prohibitionSites;

    /**
     * 单品状态 database column amazon_product_listing.skuStatus
     */
    @Field(type = FieldType.Keyword)
    private String skuStatus;

    /**
     * 产品标签code database column amazon_product_listing.tagCodes
     */
    @Field(type = FieldType.Keyword)
    private String tagCodes;

    /**
     * 产品标签 database column amazon_product_listing.tagNames
     */
    @Field(type = FieldType.Keyword)
    private String tagNames;

    /**
     * 特殊标签 database column amazon_product_listing.specialGoodsCode
     */
    @Field(type = FieldType.Keyword)
    private String specialGoodsCode;

    /**
     * 特殊标签 database column amazon_product_listing.specialGoodsName
     */
    @Field(type = FieldType.Keyword)
    private String specialGoodsName;

    /**
     * 产品类目id
     */
    @Field(type = FieldType.Keyword)
    private String proCategoryId;

    @Field(type = FieldType.Keyword)
    private String proCategoryCnName;

    /**
     * 24小时销量
     */
    @Field(type = FieldType.Long)
    private Integer order_24H_count ;

    /**
     * 7天销量
     */
    @Field(type = FieldType.Long)
    private Integer order_last_7d_count;

    /**
     * 14天销量
     */
    @Field(type = FieldType.Long)
    private Integer order_last_14d_count;

    /**
     * 30天销量
     */
    @Field(type = FieldType.Long)
    private Integer order_last_30d_count;

    /**
     * 30天动销天数
     */
    @Field(type = FieldType.Long)
    private Integer order_days_within_30d;

    /**
     * 30天动销率
     */
    @Field(type = FieldType.Double)
    private Double order_days_within_30d_rate;

    /**
     * 60天销量
     */
    @Field(type = FieldType.Long)
    private Integer order_last_60d_count;

    /**
     * 60天动销天数
     */
    @Field(type = FieldType.Long)
    private Integer order_days_within_60d;

    /**
     * 60天动销率
     */
    @Field(type = FieldType.Double)
    private Double order_days_within_60d_rate;

    /**
     * aliexpress_product_listing  order_last_180d_count_new（现在使用的） order_last_180d_count（可以废弃了）
     */
    /**
     * 180天销量
     */
    @Field(type = FieldType.Long)
    private Integer order_last_180d_count_new;

    /**
     * 总销量
     */
    @Field(type = FieldType.Long)
    private Integer order_num_total;

    /**
     * sku商品编码 database column aliexpress_product.sku_code
     */
    @Field(type = FieldType.Keyword)
    private String skuCode;

    /**
     * SKU ID database column aliexpress_product.sku_id
     */
    @Field(type = FieldType.Keyword)
    private String skuId;

    /**
     * sku实际可售库存 database column aliexpress_product.ipm_sku_stock
     */
    @Field(type = FieldType.Integer)
    private Integer ipmSkuStock;

    /**
     * 模版展示图 database column aliexpress_product.display_image_url
     */
    @Field(type = FieldType.Keyword)
    private String displayImageUrl;

    /**
     * 商品拥有者的login_id database column aliexpress_product.owner_member_id
     */
    @Field(type = FieldType.Keyword)
    private String ownerMemberId;

    /**
     * 商品拥有者的ID database column aliexpress_product.owner_member_seq
     */
    @Field(type = FieldType.Integer)
    private Integer ownerMemberSeq;

    /**
     * 备货期。取值范围:1-60;单位:天。 database column aliexpress_product.delivery_time
     */
    @Field(type = FieldType.Integer)
    private Integer deliveryTime;

    /**
     * 服务模板设置。（需和服务模板查询接口api.queryPromiseTemplateById进行关联使用） database column aliexpress_product.promise_template_id
     */
    @Field(type = FieldType.Long)
    private Long promiseTemplateId;

    /**
     * 是否支持车型库
     */
    @Field(type = FieldType.Boolean)
    private Boolean isCayType;

    /**
     * 商品所属类目ID。必须是叶子类目，通过类目接口获取。 database column aliexpress_product.category_id
     */
    @Field(type = FieldType.Integer)
    private Integer categoryId;

    /**
     * 类目名 database column aliexpress_product.category_name
     */
    @Field(type = FieldType.Keyword)
    private String categoryName;

    /**
     * 	商品标题 长度在1-128之间英文。 database column aliexpress_product.subject
     */
    @Field(type = FieldType.Keyword)
    private String subject;

    /**
     * 商品一口价。取值范围:0-100000,保留两位小数;单位:美元。如:200.07，表示:200美元7分。需要在正确的价格区间内。上传多属性产品的时候，有好几个SKU和价格，productprice无需填写。 database column aliexpress_product.product_price
     */
    @Field(type = FieldType.Double)
    private Double productPrice;

    /**
     * 运费模版ID。通过运费接口listFreightTemplate获取。 database column aliexpress_product.freight_template_id
     */
    @Field(type = FieldType.Long)
    private Long freightTemplateId;


    /**
     * 产品的主图URL列表。如果这个产品有多张主图，那么这些URL之间使用英文分号(";")隔开。 一个产品最多只能有6张主图。图片格式JPEG，文件大小5M以内；图片像素建议大于800*800；横向和纵向比例建议1:1到1:1.3之间；图片中产品主体占比建议大于70%；背景白色或纯色，风格统一；如果有LOGO，建议放置在左上角，不宜过大。 不建议自行添加促销标签或文字。切勿盗用他人图片，以免受网规处罚。更多说明请至http://seller.aliexpress.com/so/tupianguifan.php进行了解。 database column aliexpress_product.image_urls
     */
    @Field(type = FieldType.Keyword)
    private String imageUrls;

    /**
     * 商品单位 (存储单位编号) 100000000:袋 (bag/bags) 100000001:桶 (barrel/barrels) 100000002:蒲式耳 (bushel/bushels) 100078580:箱 (carton) 100078581:厘米 (centimeter) 100000003:立方米 (cubic meter) 100000004:打 (dozen) 100078584:英尺 (feet) 100000005:加仑 (gallon) 100000006:克 (gram) 100078587:英寸 (inch) 100000007:千克 (kilogram) 100078589:千升 (kiloliter) 100000008:千米 (kilometer) 100078559:升 (liter/liters) 100000009:英吨 (long ton) 100000010:米 (meter) 100000011:公吨 (metric ton) 100078560:毫克 (milligram) 100078596:毫升 (milliliter) 100078597:毫米 (millimeter) 100000012:盎司 (ounce) 100000014:包 (pack/packs) 100000013:双 (pair) 100000015:件/个 (piece/pieces) 100000016:磅 (pound) 100078603:夸脱 (quart) 100000017:套 (set/sets) 100000018:美吨 (short ton) 100078606:平方英尺 (square feet) 100078607:平方英寸 (square inch) 100000019:平方米 (square meter) 100078609:平方码 (square yard) 100000020:吨 (ton) 100078558:码 (yard/yards) database column aliexpress_product.product_unit
     */
    @Field(type = FieldType.Integer)
    private Integer productUnit;

    /**
     * 打包销售: true 非打包销售:false database column aliexpress_product.package_type
     */
    @Field(type = FieldType.Boolean)
    private Boolean packageType;

    /**
     * 每包件数。 打包销售情况，lotNum>1,非打包销售情况,lotNum=1 database column aliexpress_product.lot_num
     */
    @Field(type = FieldType.Integer)
    private Integer lotNum;

    /**
     * 商品包装长度。取值范围:1-700,单位:厘米。产品包装尺寸的最大值+2×（第二大值+第三大值）不能超过2700厘米。 database column aliexpress_product.package_length
     */
    @Field(type = FieldType.Integer)
    private Integer packageLength;

    /**
     * 商品包装宽度。取值范围:1-700,单位:厘米。 database column aliexpress_product.package_width
     */
    @Field(type = FieldType.Integer)
    private Integer packageWidth;

    /**
     * 商品包装高度。取值范围:1-700,单位:厘米。 database column aliexpress_product.package_height
     */
    @Field(type = FieldType.Integer)
    private Integer packageHeight;

    /**
     * 商品毛重,取值范围:0.001-500.000,保留三位小数,采用进位制,单位:公斤。 database column aliexpress_product.gross_weight
     */
    @Field(type = FieldType.Keyword)
    private String grossWeight;

    /**
     * 是否自定义计重.true为自定义计重,false反之. database column aliexpress_product.is_pack_sell
     */
    @Field(type = FieldType.Boolean)
    private Boolean isPackSell;

    /**
     * 是否自定义计重 database column aliexpress_product.is_wholesale
     */
    @Field(type = FieldType.Boolean)
    private Boolean isWholesale;

    /**
     * isPackSell为true时,此项必填。购买几件以内不增加运费。取值范围1-1000 database column aliexpress_product.base_unit
     */
    @Field(type = FieldType.Integer)
    private Integer baseUnit;

    /**
     * isPackSell为true时,此项必填。 每增加件数.取值范围1-1000。 database column aliexpress_product.add_unit
     */
    @Field(type = FieldType.Integer)
    private Integer addUnit;

    /**
     * isPackSell为true时,此项必填。 对应增加的重量.取值范围:0.001-500.000,保留三位小数,采用进位制,单位:公斤。 database column aliexpress_product.add_weight
     */
    @Field(type = FieldType.Keyword)
    private String addWeight;

    /**
     * 商品有效天数。取值范围:1-30,单位:天。 database column aliexpress_product.ws_valid_num
     */
    @Field(type = FieldType.Integer)
    private Integer wsValidNum;

    /**
     * 批发最小数量 。取值范围2-100000。批发最小数量和批发折扣需同时有值或无值。 database column aliexpress_product.bulk_order
     */
    @Field(type = FieldType.Integer)
    private Integer bulkOrder;

    /**
     * 批发折扣。扩大100倍，存整数。取值范围:1-99。注意：这是折扣，不是打折率。 如,打68折,则存32。批发最小数量和批发折扣需同时有值或无值。 database column aliexpress_product.bulk_discount
     */
    @Field(type = FieldType.Integer)
    private Integer bulkDiscount;

    /**
     * 尺码表模版ID。必须选择当前类目下的尺码模版。 database column aliexpress_product.size_chart_id
     */
    @Field(type = FieldType.Long)
    private Long sizeChartId;

    /**
     * 新尺码表模板
     */
    @Field(type = FieldType.Keyword)
    private String sizechartIdList;

    /**
     * 库存扣减策略，总共有2种：下单减库存(place_order_withhold)和支付减库存(payment_success_deduct)。 database column aliexpress_product.reduce_strategy
     */
    @Field(type = FieldType.Keyword)
    private String reduceStrategy;

    /**
     * 产品所关联的产品分组ID database column aliexpress_product.group_id
     */
    @Field(type = FieldType.Long)
    private Long groupId;

    /**
     * 产品所在的产品分组列表，多个用;分开 database column aliexpress_product.group_ids
     */
    @Field(type = FieldType.Keyword)
    private String groupIds;


    /**
     * 模糊匹配 aliexpress_product.group_ids 字段
     */
    private List<String> groupIdsList;

    /**
     * 货币单位。如果不提供该值信息，则默认为"USD"；非俄罗斯卖家这个属性值可以不提供。对于俄罗斯海外卖家，该单位值必须提供，如: "RUB"。 database column aliexpress_product.currency_code
     */
    @Field(type = FieldType.Keyword)
    private String currencyCode;

    /**
     * 卡券商品开始有效期 database column aliexpress_product.coupon_start_date
     */
    @Field(type = FieldType.Date, format = DateFormat.custom, pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern ="yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date couponStartDate;

    /**
     * 卡券商品结束有效期 database column aliexpress_product.coupon_end_date
     */
    @Field(type = FieldType.Date, format = DateFormat.custom, pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern ="yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date couponEndDate;


    /**
     * 产品的来源 database column aliexpress_product.src
     */
    @Field(type = FieldType.Keyword)
    private String src;

    /**
     * 产品的下架日期 database column aliexpress_product.ws_offline_date
     */
    @Field(type = FieldType.Date, format = DateFormat.custom, pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern ="yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date wsOfflineDate;

    /**
     * 产品的下架原因 database column aliexpress_product.ws_display
     */
    @Field(type = FieldType.Keyword)
    private String wsDisplay;

    /**
     * 产品的状态 database column aliexpress_product.product_status_type
     */
    @Field(type = FieldType.Keyword)
    private String productStatusType;


    /**
     * 是否是动态图产品 database column aliexpress_product.is_image_dynamic
     */
    @Field(type = FieldType.Boolean)
    private Boolean isImageDynamic;

    /**
     * 数据库创建时间 database column aliexpress_product.gmt_create
     */
    @Field(type = FieldType.Date, format = DateFormat.custom, pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern ="yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /**
     * 数据库修改时间 database column aliexpress_product.gmt_create
     */
    @Field(type = FieldType.Date, format = DateFormat.custom, pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern ="yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date lastEditTime;

    /**
     * 创建时间 database column aliexpress_product.gmt_create
     */
    @Field(type = FieldType.Date, format = DateFormat.custom, pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern ="yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date gmtCreate;

    /**
     * 更新时间 database column aliexpress_product.gmt_modified
     */
    @Field(type = FieldType.Date, format = DateFormat.custom, pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern ="yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date gmtModified;

    /**
     * 最小价格 database column aliexpress_product.product_min_price
     */
    @Field(type = FieldType.Double)
    private Double productMinPrice;

    /**
     * 最大价格 database column aliexpress_product.product_max_price
     */
    @Field(type = FieldType.Double)
    private Double productMaxPrice;

    /**
     * 最后同步时间 database column aliexpress_product.last_sync_time
     */
    @Field(type = FieldType.Date, format = DateFormat.custom, pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern ="yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date lastSyncTime;

    /**
     * sku图片 database column aliexpress_product.sku_display_img
     */
    @Field(type = FieldType.Keyword)
    private String skuDisplayImg;

    /**
     * sku价格 database column aliexpress_product.sku_price
     */
    @Field(type = FieldType.Double)
    private Double skuPrice;

    /**
     * sku日常促销价 database column aliexpress_product.sku_discount_price
     */
    @Field(type = FieldType.Double)
    private Double skuDiscountPrice;

    /**
     * barcode database column aliexpress_product.barcode
     */
    @Field(type = FieldType.Keyword)
    private String barcode;

    /**
     * 是否多属性
     */
    @Field(type = FieldType.Boolean)
    private Boolean isVariant;

    /**
     * Sku属性对象list，允许1-3个sku属性对象，按sku属性顺序排放 database column aliexpress_product.aeop_s_k_u_property_list
     */
    @Field(type = FieldType.Text,index=false)
    private String aeopSKUPropertyList;

    /**
     * sku分国家的日常促销价 database column aliexpress_product.aeop_s_k_u_national_discount_price_list
     */
    @Field(type = FieldType.Text,index=false)
    private String aeopSKUNationalDiscountPriceList;

    /**
     * Detail详情。以下内容会被过滤，但不影响产品提交:(1)含有script\textarea\style\iframe\frame\input\pre\button均被过滤.(2)a标签href属性只允许是aliexpress.com域名连接,否则被过滤.(3)img标签src只允许alibaba.com或者aliimg.com域名链接.(4)任意具有style属性的html标签，其style受检查，只允许一般简单的样式.不允许的内容将被过滤.(5)如果发现html内容标签缺失，会自动补全标签. database column aliexpress_product.detail
     */
    @Field(type = FieldType.Text,index=false)
    private String detail;

    /**
     * 浏览量曝光量更新时间
     */
    @Field(type = FieldType.Date, format = DateFormat.custom, pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern ="yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date viewUpdateDate;


    /**
     * 浏览量曝光量最后统计时间
     */
    @Field(type = FieldType.Date, format = DateFormat.custom, pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern ="yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date lastPVUpdateDate;

    /**
     * 7天浏览量
     */
    @Field(type = FieldType.Integer)
    private Integer view_7d_count;

    /**
     * 7天曝光量
     */
    @Field(type = FieldType.Integer)
    private Integer exposure_7d_count;

    /**
     * 14天浏览量
     */
    @Field(type = FieldType.Integer)
    private Integer view_14d_count;

    /**
     * 14天曝光量
     */
    @Field(type = FieldType.Integer)
    private Integer exposure_14d_count;

    /**
     * 30天浏览量
     */
    @Field(type = FieldType.Integer)
    private Integer view_30d_count;

    /**
     * 30天曝光量
     */
    @Field(type = FieldType.Integer)
    private Integer exposure_30d_count;

    /**
     * 是否区域调价
     */
    @Field(type = FieldType.Boolean)
    private Boolean isRegionPrice;

    /**
     * 发货地
     */
    @Field(type = FieldType.Keyword)
    private String deliveryAddress;

    /**
     * 是否海外仓
     */
    @Field(type = FieldType.Boolean)
    private Boolean isOverseas;

    /**
     * 是否中国省份类目
     */
    @Field(type = FieldType.Boolean)
    private Boolean isCnProvinceCategory;

    /**
     * 中国省份名称
     */
    @Field(type = FieldType.Keyword)
    private String cnProvince;

    /**
     * 品牌Id
     */
    @Field(type = FieldType.Long)
    private Long brandId;

    /**
     * 品牌名
     * json {zh:小米，en:MI}
     */
    @Field(type = FieldType.Nested)
    private Map<String,String> brand;

    /**
     * 是否多属性无图片
     */
    @Field(type = FieldType.Boolean)
    private Boolean isMultiNoImg;

    /**
     * 产品数据类型
     * listing 数据来源
     */
    @Field(type = FieldType.Long)
    private Long dataSourceType;

    /**
     * 组合状态
     * tips：数据来源为组合套装的才有值，对应产品套装的流转状态
     */
    @Field(type = FieldType.Keyword)
    private Integer composeStatus;

    /**
     * 促销状态 0 没有  1，是  2，否  特别注意 0 和 2 都是(否)，1是（是）
     */
    @Field(type = FieldType.Keyword)
    private Integer promotion;

    /**
     * 新品状态
     */
    @Field(type = FieldType.Boolean)
    private Boolean newState;

    /**
     * 商品表现
     * 1 商品竞争力不佳
     */
    @Field(type = FieldType.Integer)
    private Integer itemShow;

    /**
     * 滞销标签: 滞销,短呆滞,长呆滞
     */
    @Field(type = FieldType.Keyword)
    private String unsalableTag;

    /**
     * 侵权词
     */
    @Field(type = FieldType.Keyword)
    private List<String> infringementWordList;

    /**
     * 侵权词 详细信息
     */
    @Field(type = FieldType.Nested)
    private List<InfringementWordInfo> infringementWordInfos;

    /**
     * 侵权校验时间
     */
    @Field(type = FieldType.Date, format = DateFormat.custom, pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern ="yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date infringementCheckTime;

    /**
     * 是否有视频
     */
    @Field(type = FieldType.Boolean)
    private Boolean isHasVideo;

    /**
     * 是否有资质
     */
    @Field(type = FieldType.Boolean)
    private Boolean isHasQualification;

    /**
     * 产品物流模板标签
     */
    @Field(type = FieldType.Keyword)
    private String categoryLabel;

    /**
     * 平台生成的唯一随机数
     */
    @Field(type = FieldType.Keyword)
    private String platSkuId;

    /**
     * 销售成本价
     */
    @Field(type = FieldType.Double)
    private Double saleCost;

    /**
     * 产品在线状态：平台同步回系统时，系统内产品默认在线状态，将系统中没有被平台同步的产品设置为不在线
     */
    @Field(type=FieldType.Keyword)
    private String onlineStatus;

    /**
     * 制造商id
     */
    @Field(type = FieldType.Long)
    private Long manufactureId;

    /**
     * 制造商name
     */
    @Field(type=FieldType.Keyword)
    private String manufactureName;

    /**
     * 欧盟负责人
     */
    @Field(type = FieldType.Long)
    private Long msrEuId;

    /**
     * 欧盟类别
     */
    @Field(type=FieldType.Keyword)
    private String msrEuIdType;

    /**
     * JIT 24小时销量
     */
    @Field(type=FieldType.Integer)
    private Integer jit_order_num_24h;

    /**
     * JIT 7天销量
     */
    @Field(type=FieldType.Integer)
    private Integer jit_order_num_7d;

    /**
     * JIT 14天销量
     */
    @Field(type=FieldType.Integer)
    private Integer jit_order_num_14d;

    /**
     * JIT 30天销量
     */
    @Field(type=FieldType.Integer)
    private Integer jit_order_num_30d;

    /**
     * JIT 60天销量
     */
    @Field(type=FieldType.Integer)
    private Integer jit_order_num_60d;

    /**
     * JIT 180天销量
     */
    @Field(type=FieldType.Integer)
    private Integer jit_order_num_180d;

    /**
     * JIT 总销量
     */
    @Field(type=FieldType.Integer)
    private Integer jit_order_num_total;

    /**
     * 是否同步admin范本
     */
    @Field(type = FieldType.Boolean)
    private Boolean adminToProductFlag;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern ="yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Field(type = FieldType.Date, format = DateFormat.custom, pattern = "yyyy-MM-dd HH:mm:ss")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date adminToProductTime;

    /**
     * [{\"num\":1,\"off\":2},{\"num\":3,\"off\":5},{\"num\":10,\"off\":10},{\"num\":100,\"off\":15}]
     * 阶梯批发价
     */
    @Field(type = FieldType.Text,index=false)
    private String ladderPrice;

    /**
     * 是否有阶梯批发价
     */
    @Field(type = FieldType.Boolean)
    private Boolean isHasLadderPrice;


    /**
     * 是否有GPSR图片
     */
    @Field(type = FieldType.Boolean)
    private Boolean isHasGPSRImg;

    /**
     * 是否有Package图片
     */
    @Field(type = FieldType.Boolean)
    private Boolean isHasPackageImg;

    /**
     * 是否有Stock图片
     */
    @Field(type = FieldType.Boolean)
    private Boolean isHasStockImg;

    /**
     * 1 不含税报价 (美国半托管必须设置1) 2 含税报价
     */
    @Field(type=FieldType.Keyword)
    private String taxType;

    /**
     * 海关编码
     */
    @Field(type=FieldType.Keyword)
    private String hscode;

    /**
     * 半托管退出标签
     * JIT国家退出审核中、待区域调价
     */
    @Field(type=FieldType.Keyword)
    private String halfCountryExitLabel;
}
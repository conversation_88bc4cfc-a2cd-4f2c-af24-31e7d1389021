package com.estone.erp.publish.elasticsearch2.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.data.elasticsearch.annotations.DateFormat;
import org.springframework.data.elasticsearch.annotations.Field;
import org.springframework.data.elasticsearch.annotations.FieldType;

import java.io.Serializable;
import java.util.Date;

/**
 * @Auther yucm
 * @Date 2022/9/16
 */
@Data
public class EsShopeeGlobalTemplateShop implements Serializable {

    /**
     * 模板id
     */
    @Field(type = FieldType.Long)
    private Long templateId;

    /**
     * 店铺账号
     */
    @Field(type = FieldType.Keyword)
    private String accountNumber;

    /**
     * 平台店铺id
     */
    @Field(type = FieldType.Keyword)
    private String shopId;

    /**
     * 站点
     */
    @Field(type = FieldType.Keyword)
    private String site;

    /**
     * 货号
     */
    @Field(type = FieldType.Keyword)
    private String sku;

    /**
     * 全球产品id
     */
    @Field(type = FieldType.Long)
    private Long globalItemId;

    /**
     * 店铺产品id
     */
    @Field(type = FieldType.Keyword)
    private Long itemId;

    /**
     * 发布任务的id
     */
    @Field(type = FieldType.Keyword)
    private String publishTaskId;

    /**
     * 发布任务到平台的状态
     */
    @Field(type = FieldType.Keyword)
    private String publishStatus;

    /**
     * 发布状态，1.待发布 2.发布中 3.发布成功 4.发布失败
     */
    @Field(type = FieldType.Integer)
    private Integer localStatus;

    /**
     * 创建时间
     */
    @Field(type = FieldType.Date, format = DateFormat.custom, pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern ="yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createDate;

    /**
     * 创建人
     */
    @Field(type = FieldType.Keyword)
    private String createdBy;

    /**
     * 修改时间
     */
    @Field(type = FieldType.Date, format = DateFormat.custom, pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern ="yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date lastUpdateDate;

    /**
     * 修改人
     */
    @Field(type = FieldType.Keyword)
    private String lastUpdatedBy;
}

package com.estone.erp.publish.platform.model;

import java.io.Serializable;
import java.sql.Timestamp;
import lombok.Data;

@Data
public class TSkuBind implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     *  database column t_sku_bind.bind_id
     */
    private Integer bindId;

    /**
     *  database column t_sku_bind.sku
     */
    private String sku;

    /**
     *  database column t_sku_bind.bind_sku
     */
    private String bindSku;

    /**
     *  database column t_sku_bind.platform
     */
    private String platform;

    /**
     *  database column t_sku_bind.seller_id
     */
    private String sellerId;

    /**
     *  database column t_sku_bind.create_date
     */
    private Timestamp createDate;

    /**
     *  database column t_sku_bind.extend
     */
    private String extend;

    /**
     * 系统sku
     */
    private String systemSku;

    /**
     * 主sku
     */
    private String mainSku;

    /**
     * SKU数据来源(1.产品系统;2.数据分析系统;3.冠通;4.探雅)
     */
    private Integer skuDataSource;
}
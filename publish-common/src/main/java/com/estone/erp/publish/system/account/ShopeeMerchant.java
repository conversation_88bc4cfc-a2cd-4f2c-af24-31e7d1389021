package com.estone.erp.publish.system.account;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2021/7/19 18:19
 * @description
 */
@Getter
@Setter
public class ShopeeMerchant {

    private Integer id;
    /** 商家id */
    private String merchantId;

    /** 商家名称 */
    private String merchantName;

    /** 主账号id(母账号id) */
    private String mainAccountId;

    /** 状态*/
    private String authorizationStatus;

    /** token */
    private String accessToken;

    /** 刷新token */
    private String refreshToken;

    /** 商家授权给母账号的过期时间 */
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSX", timezone = "GMT+8")
    private Date expireTime;

    /** token过期时间 */
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSX", timezone = "GMT+8")
    private Date tokenExpireTime;

   /* private Long createDate;

    private Long modifiedDate;*/
}

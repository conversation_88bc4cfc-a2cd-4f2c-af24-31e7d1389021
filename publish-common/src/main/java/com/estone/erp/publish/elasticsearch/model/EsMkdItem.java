package com.estone.erp.publish.elasticsearch.model;


import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.elasticsearch.annotations.DateFormat;
import org.springframework.data.elasticsearch.annotations.Document;
import org.springframework.data.elasticsearch.annotations.Field;
import org.springframework.data.elasticsearch.annotations.FieldType;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
@Document(indexName = "mkd_item", type = "esMkdListing")
public class EsMkdItem implements Serializable {

    @Id
    @Field(type = FieldType.Keyword)
    private String id;

    @Field(type = FieldType.Keyword)
    private String image;

    /**
     * 店铺
     */
    @Field(type = FieldType.Keyword)
    private String accountNumber;

    /**
     * 站点
     */
    @Field(type = FieldType.Keyword)
    private String site;

    /**
     * cnt网站ID，主ID
     */
    @Field(type = FieldType.Keyword)
    private String cbtItemId;

    @Field(type = FieldType.Keyword)
    private String itemId;

    @Field(type = FieldType.Keyword)
    private String sellerSku;

    @Field(type = FieldType.Keyword)
    private String sku;

    @Field(type = FieldType.Keyword)
    private String spu;

    /**
     * 标题
     */
    @Field(type = FieldType.Keyword)
    private String title;

    /**
     * 标签
     */
    @Field(type = FieldType.Keyword)
    private String tag;

    /**
     * 特殊标签
     */
    @Field(type = FieldType.Keyword)
    private List<Integer> specialTag;

    /**
     * 价格
     */
    @Field(type = FieldType.Keyword)
    private Double usPrice;

    /**
     * 站点价格
     */
    @Field(type = FieldType.Keyword)
    private Double sitePrice;

    /**
     * 币种
     */
    @Field(type = FieldType.Keyword)
    private String currency;

    /**
     * 毛利率
     */
    @Field(type = FieldType.Keyword)
    private Double profitMargin;

    /**
     * 库存
     */
    @Field(type = FieldType.Keyword)
    private Integer inventory;

    /**
     * 状态
     */
    @Field(type = FieldType.Keyword)
    private String status;

    /**
     * 子状态
     */
    @Field(type = FieldType.Keyword)
    private List<String> subStatus;

    /**
     * 单品状态
     */
    @Field(type = FieldType.Keyword)
    private String itemStatus;


    /**
     * 访问量
     */
    @Field(type = FieldType.Keyword)
    private Integer visits;

    /**
     * 订单量
     */
    @Field(type = FieldType.Keyword)
    private Integer order;

    /**
     * 分类
     */
    @Field(type = FieldType.Keyword)
    private String category;

    /**
     * 禁售平台
     */
    @Field(type = FieldType.Keyword)
    private List<String> forbidChannel;

//    /**
//     * 属性
//     */
//    @Field(type = FieldType.Object)
//    private JSONObject attributes;

    /**
     * 属性
     */
    @Field(type = FieldType.Keyword, index = false)
    private String attributesJson;

    /**
     * 属性值(用来导出判断)
     */
    private JSONObject attributesValue;

    /**
     * listing类型id
     */
    @Field(type = FieldType.Keyword)
    private String listingTypeId;

    /**
     * 创建时间
     */
    @Field(type = FieldType.Date, format = DateFormat.custom, pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createDate;

    /**
     * 同步时间
     */
    @Field(type = FieldType.Date, format = DateFormat.custom, pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date syncDate;

    /**
     * 最后跟新时间
     */
    @Field(type = FieldType.Date, format = DateFormat.custom, pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date lastUpdate;

    /**
     * 是否变体
     * 0：主体，1：变体
     */
    @Field(type = FieldType.Keyword)
    private Integer isVariation;

    /**
     * 是否多属性
     * 0：否，1：是
     */
    @Field(type = FieldType.Keyword)
    private Integer hasVariation;

    /**
     * 变体ID
     */
    @Field(type = FieldType.Keyword)
    private String variationId;

    /**
     * CBT变体ID
     */
    @Field(type = FieldType.Keyword)
    private String cbtVariationId;

    /**
     * 模型类型(用于修改价格)
     */
    @Field(type = FieldType.Keyword)
    private String logisticType;

    /**
     * 销售
     */
    @Field(type = FieldType.Keyword)
    private String salesman;

    /**
     * 禁售原因
     */
    @Field(type = FieldType.Keyword)
    private List<String> infringementObjs;

    /**
     * 禁售类型
     */
    @Field(type = FieldType.Keyword)
    private List<String> infringementTypeNames;

    /**
     * 平台禁售站点
     */
    @Field(type = FieldType.Keyword)
    private List<String> prohibitionSites;


    /**
     * 促销状态 0 没有  1，是  2，否  特别注意 0 和 2 都是(否)，1是（是）
     */
    @Field(type = FieldType.Keyword)
    private Integer promotion;

    /**
     * 新品状态
     */
    @Field(type = FieldType.Boolean)
    private Boolean newState;

    /**
     * 活动信息
     */
    @Field(type = FieldType.Nested)
    private List<MkdPromotion> promotions;

    /**
     * 是否促销
     */
    @Field(type = FieldType.Boolean)
    private Boolean isPromotion;

    /**
     * 数据来源
     */
    @Field(type = FieldType.Integer)
    private Integer skuDataSource;

    /**
     * 组合状态
     */
    @Field(type = FieldType.Integer)
    private Integer composeStatus;


    /**
     * 同步产品信息日期
     */
    @Field(type = FieldType.Date, format = DateFormat.custom, pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateSyncProductInfoDate;

    /**
     * 活动信息
     */
    @Data
    public static class MkdPromotion {
        /**
         * 活动ID
         */
        @Field(type = FieldType.Keyword)
        private String promotionId;

        /**
         * 活动类型
         */
        @Field(type = FieldType.Keyword)
        private String promotionType;

        /**
         * 活动状态
         */
        @Field(type = FieldType.Integer)
        private Integer status;

        /**
         * 折扣
         */
        @Field(type = FieldType.Double)
        private Double discount;

        /**
         * 折扣价
         */
        @Field(type = FieldType.Double)
        private Double discountPrice;

        /**
         * 折扣价
         */
        @Field(type = FieldType.Float)
        private Double discountSitePrice;

        /**
         * 折扣价
         */
        @Field(type = FieldType.Long)
        private Integer stock;
    }
}

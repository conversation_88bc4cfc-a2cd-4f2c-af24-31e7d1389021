package com.estone.erp.publish.system.account;

import com.estone.erp.common.model.api.ApiRequestParam;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.model.api.CQueryResult;
import com.estone.erp.publish.system.account.modle.VmUser;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import java.util.List;

/**
 * 功能描述: 销售系统接口
 **/
@FeignClient(name = "account-center-service")
//@FeignClient(name = "account-center-service", url = "http://10.100.1.1:31100")
public interface AccountCenterClient {

    /**
     * 根据店铺名获取紫鸟店铺名
     * http://172.16.10.40/web/#/18/9777
     * @param accountNumbers 店铺集合
     */
    @RequestMapping(value = "/account/getVmusernameByAcc", method = RequestMethod.POST, headers = "content-type=application/json")
    ApiResult<List<VmUser>> getVmusernameByAcc(@RequestBody List<String> accountNumbers);


    /**
     * 根据紫鸟店铺名获取店铺
     *
     * @param requestParam req
     */
    @RequestMapping(value = "/accounts", method = RequestMethod.POST, headers = "content-type=application/json")
    ApiResult<CQueryResult<VmUser>> getAccounts(@RequestBody ApiRequestParam<String> requestParam);
}

package com.estone.erp.publish.elasticsearch2.service;

import com.estone.erp.publish.elasticsearch2.model.EsShopeeItemOffline;
import com.estone.erp.publish.elasticsearch2.model.beanrequest.EsShopeeItemOfflineRequest;
import org.springframework.data.domain.Page;

import java.util.List;
import java.util.Map;

/**
 * Shopee es service
 */
public interface EsShopeeItemOfflineService {

    long count();

    void save(EsShopeeItemOffline EsShopeeItemOffline);

    void saveAll(List<EsShopeeItemOffline> EsShopeeItemOfflines);

    void deleteById(String id);

    EsShopeeItemOffline findAllById(String id);

    List<EsShopeeItemOffline> getEsShopeeItemOfflines(EsShopeeItemOfflineRequest EsShopeeItemOfflineRequest);

    Page<EsShopeeItemOffline> page(EsShopeeItemOfflineRequest request, int pageSize, Integer offset);

    Map<String, Integer> getAccountItemSumMap(EsShopeeItemOfflineRequest request);
}

package com.estone.erp.publish.platform.mapper;

import com.estone.erp.publish.platform.model.ChangeSkuLog;
import com.estone.erp.publish.platform.model.ChangeSkuLogExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface ChangeSkuLogMapper {
    int countByExample(ChangeSkuLogExample example);

    int deleteByPrimaryKey(List<Integer> ids);

    int insert(ChangeSkuLog record);

    ChangeSkuLog selectByPrimaryKey(Integer id);

    List<ChangeSkuLog> selectByExample(ChangeSkuLogExample example);

    int updateByExampleSelective(@Param("record") ChangeSkuLog record, @Param("example") ChangeSkuLogExample example);

    int updateByPrimaryKeySelective(ChangeSkuLog record);
}
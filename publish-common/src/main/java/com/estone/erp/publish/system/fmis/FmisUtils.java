package com.estone.erp.publish.system.fmis;

import com.estone.erp.publish.system.fmis.model.AccountInfoDTO;
import com.google.common.collect.Maps;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Copyright: Copyright(c) 2024
 * @date 2024年03月12日/15:40
 * @Description: <p>工具类</p>
 * @Version: 1.0.0
 * @modified:
 */

public class FmisUtils {

    public static final Logger LOGGER = LoggerFactory.getLogger(FmisUtils.class);

    /**
     * 同一个店铺多个经营大类通过拼接符拼接在一起
     *
     * @param list      店铺列表
     * @param separator 分隔符
     * @return
     */
    public static Map<String, String> getCategoryMap(List<AccountInfoDTO> list, String separator) {
        if (CollectionUtils.isEmpty(list) || StringUtils.isEmpty(separator)) {
            return Maps.newHashMap();
        }
        List<String> types = Arrays.asList("成功开通", "审核通过");
        Map<String, String> categoryMap = new HashMap<>(16);
        for (AccountInfoDTO accountInfoDTO : list) {
            //仅显示店铺经营大类成功开通和审核通过两种状态
            if (!types.contains(accountInfoDTO.getCurrentStatus())) {
                continue;
            }
            String account = accountInfoDTO.getAccount();
            String categoryName = accountInfoDTO.getCategoryName();
            if (categoryMap.containsKey(account)) {
                categoryMap.put(account, categoryMap.get(account) + separator + categoryName);
                continue;
            }
            categoryMap.put(account, categoryName);
        }
        return categoryMap;
    }
}

package com.estone.erp.publish.platform.controller;

import com.alibaba.fastjson.TypeReference;
import com.estone.erp.common.model.api.ApiRequestParam;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.model.api.CQuery;
import com.estone.erp.common.model.api.CQueryResult;
import com.estone.erp.common.util.Asserts;
import com.estone.erp.publish.common.ErrorCode;
import com.estone.erp.publish.platform.model.OperateLog;
import com.estone.erp.publish.platform.model.OperateLogCriteria;
import com.estone.erp.publish.platform.service.OperateLogService;
import javax.annotation.Resource;
import org.apache.commons.lang.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR> operate_log
 * 2022-07-07 17:22:41
 */
@RestController
@RequestMapping("operateLog")
public class OperateLogController {
    @Resource
    private OperateLogService operateLogService;

    @PostMapping
    public ApiResult<?> postOperateLog(@RequestBody(required = true) ApiRequestParam<String> requestParam) {
        String method = requestParam.getMethod();
        if (StringUtils.isNotBlank(method)) {
            switch (method) {
                case "searchOperateLog": // 查询列表
                    CQuery<OperateLogCriteria> cquery = requestParam.getArgsValue(new TypeReference<CQuery<OperateLogCriteria>>() {});
                    Asserts.isTrue(cquery != null, ErrorCode.PARAM_EMPTY_ERROR);
                    CQueryResult<OperateLog> results = operateLogService.search(cquery);
                    return results;
                case "addOperateLog": // 添加
                    OperateLog operateLog = requestParam.getArgsValue(new TypeReference<OperateLog>() {});
                    operateLogService.insert(operateLog);
                    return ApiResult.newSuccess(operateLog);
                }
        }
        return ApiResult.newSuccess();
    }

    @GetMapping(value = "/{id}")
    public ApiResult<?> getOperateLog(@PathVariable(value = "id", required = true) Long id) {
        OperateLog operateLog = operateLogService.selectByPrimaryKey(id);
        return ApiResult.newSuccess(operateLog);
    }

    @PutMapping(value = "/{id}")
    public ApiResult<?> putOperateLog(@PathVariable(value = "id", required = true) Integer id, @RequestBody ApiRequestParam<String> requestParam) {
        String method = requestParam.getMethod();
        if (StringUtils.isNotBlank(method)) {
            switch (method) {
                case "updateOperateLog": // 单个修改
                    OperateLog operateLog = requestParam.getArgsValue(new TypeReference<OperateLog>() {});
                    operateLogService.updateByPrimaryKeySelective(operateLog);
                    return ApiResult.newSuccess(operateLog);
                }
        }
        return ApiResult.newSuccess();
    }
}
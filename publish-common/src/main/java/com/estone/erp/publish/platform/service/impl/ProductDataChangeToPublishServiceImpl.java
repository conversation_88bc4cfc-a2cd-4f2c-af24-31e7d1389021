package com.estone.erp.publish.platform.service.impl;

import com.estone.erp.common.model.api.CQuery;
import com.estone.erp.common.model.api.CQueryResult;
import com.estone.erp.publish.common.util.DateUtils;
import com.estone.erp.publish.platform.mapper.ProductDataChangeToPublishMapper;
import com.estone.erp.publish.platform.model.ProductDataChangeToPublish;
import com.estone.erp.publish.platform.model.ProductDataChangeToPublishCriteria;
import com.estone.erp.publish.platform.model.ProductDataChangeToPublishExample;
import com.estone.erp.publish.platform.service.ProductDataChangeToPublishService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> product_data_change_to_publish
 * 2020-08-12 10:02:20
 */
@Service("productDataChangeToPublishService")
@Slf4j
public class ProductDataChangeToPublishServiceImpl implements ProductDataChangeToPublishService {
    @Resource
    private ProductDataChangeToPublishMapper productDataChangeToPublishMapper;

    @Override
    public int countByExample(ProductDataChangeToPublishExample example) {
        Assert.notNull(example, "example is null!");
        return productDataChangeToPublishMapper.countByExample(example);
    }

    @Override
    public CQueryResult<ProductDataChangeToPublish> search(CQuery<ProductDataChangeToPublishCriteria> cquery) {
        Assert.notNull(cquery, "cquery is null!");
        ProductDataChangeToPublishCriteria query = cquery.getSearch();
        ProductDataChangeToPublishExample example = query.getExample();
        long total = 0;
        int totalPages = 0;
        // 是否分页
        if (cquery.isPageReqired()) {
            total = productDataChangeToPublishMapper.countByExample(example);
            totalPages = (int) Math.ceil((double) total / cquery.getLimit());
            example.setLimit(cquery.getLimit());
            example.setOffset(cquery.getOffset());
        }
        List<ProductDataChangeToPublish> productDataChangeToPublishs = productDataChangeToPublishMapper.selectByExample(example);
        // 组装结果
        CQueryResult<ProductDataChangeToPublish> result = new CQueryResult<>();
        result.setTotal(total);
        result.setTotalPages(totalPages);
        result.setRows(productDataChangeToPublishs);
        return result;
    }

    @Override
    public ProductDataChangeToPublish selectByPrimaryKey(Long id) {
        Assert.notNull(id, "id is null!");
        return productDataChangeToPublishMapper.selectByPrimaryKey(id);
    }

    @Override
    public List<ProductDataChangeToPublish> selectByExample(ProductDataChangeToPublishExample example) {
        Assert.notNull(example, "example is null!");
        return productDataChangeToPublishMapper.selectByExample(example);
    }

    @Override
    public List<String> selectMainSkuByExample(ProductDataChangeToPublishExample example) {
        return productDataChangeToPublishMapper.selectMainSkuByExample(example);
    }

    @Override
    public int insert(ProductDataChangeToPublish record) {
        Assert.notNull(record, "record is null!");
        // 默认加时间和人
        return productDataChangeToPublishMapper.insert(record);
    }

    @Override
    public int updateByPrimaryKeySelective(ProductDataChangeToPublish record) {
        Assert.notNull(record, "record is null!");
        // 默认加修改时间和修改人
        return productDataChangeToPublishMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public int updateByExampleSelective(ProductDataChangeToPublish record, ProductDataChangeToPublishExample example) {
        Assert.notNull(record, "record is null!");
        // 默认加修改时间和修改人
        return productDataChangeToPublishMapper.updateByExampleSelective(record, example);
    }

    @Override
    public int deleteByPrimaryKey(List<Long> ids) {
        Assert.notNull(ids, "ids is null!");
        return productDataChangeToPublishMapper.deleteByPrimaryKey(ids);
    }

    @Override
    public int deleteByExample(ProductDataChangeToPublishExample example) {
        return productDataChangeToPublishMapper.deleteByExample(example);
    }

    @Override
    public List<ProductDataChangeToPublish> selectChangeDataType(String beginTime, String endTime, String type){

        if(StringUtils.isBlank(beginTime) && StringUtils.isBlank(endTime)){
            Date newDateBeforeDayBegin = DateUtils.getDateBegin(-1);
            Date newDateBeforeDayEnd = DateUtils.getDateEnd(-1);
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            beginTime = sdf.format(newDateBeforeDayBegin);
            endTime = sdf.format(newDateBeforeDayEnd);
        }
        return productDataChangeToPublishMapper.selectChangeData(beginTime, endTime, type);
    }


    public List<String> selectChangeSku(String beginTime, String endTime){
        if(StringUtils.isBlank(beginTime) && StringUtils.isBlank(endTime)){
            Date newDateBeforeDayBegin = DateUtils.getDateBegin(-1);
            Date newDateBeforeDayEnd = DateUtils.getDateEnd(-1);
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            beginTime = sdf.format(newDateBeforeDayBegin);
            endTime = sdf.format(newDateBeforeDayEnd);
        }
        return productDataChangeToPublishMapper.selectChangeSku(beginTime, endTime);
    }
}
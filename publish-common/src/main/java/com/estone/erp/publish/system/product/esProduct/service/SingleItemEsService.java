package com.estone.erp.publish.system.product.esProduct.service;

import com.estone.erp.common.model.PackageInfo;
import com.estone.erp.publish.system.product.ProductInfo;
import com.estone.erp.publish.system.product.bean.ForbiddenAndSpecical;
import com.estone.erp.publish.system.product.bean.forbidden.SalesProhibitionsVo;
import com.estone.erp.publish.system.product.esProduct.bean.SingleItemEs;
import com.estone.erp.publish.system.product.esProduct.bean.SingleItemEsRequest;
import com.estone.erp.publish.system.product.esProduct.bean.dto.SingleItemEsForbidInfoDO;

import java.util.List;
import java.util.Map;

/**
 * @Description: ${description}
 * @Author: yjy
 * @Date: 2020/11/25 10:45
 * @Version: 1.0.0
 */
public interface SingleItemEsService {

    List<SingleItemEs> getSingleItemEsList(SingleItemEsRequest criteria);

    List<SingleItemEs> getSingleItemsByCondition(SingleItemEsRequest request);

    List<SingleItemEs> getSingleItemEsList(List<String> mainSkuList);

    Map<String, String> getMainSkuBySubSku(List<String> subList);

    /**
     * 查询单个子SKU对应产品状态等
     * @param sonSku
     * @return
     */
    SingleItemEs getSkuInfo(String sonSku);

    SingleItemEs getSkuInfoByMainSku(String mainSku);

    /**
     * 春节改价 使用，可用库存 和 可卖天数的条件筛选
     * @param request
     * @return
     */
    List<SingleItemEs> getSingleItemEsListForNewYear(SingleItemEsRequest request);


    /**
     * 根据 子sku集合 和符合状态 返回结果
     * @param sonSkuList
     * @param statusList
     * @return
     */
    List<String> getFindSkuList(List<String> sonSkuList, List<Integer> statusList);

    /**
     * 查询传入的sku的主sku下的所有子sku信息（信息中心包含站点，并且主子sku同级展示）
     * @param skuList
     * @return
     */
    Map<String, List<SalesProhibitionsVo>> getForbiddenSalesSiteBySku(List<String> skuList) throws Exception;

    /**
     * 查询sku 禁售平台(只查询传入的子sku，不查子主sku和其他同级子sku信息)
     * @param sonSkuList
     * @return
     */
    Map<String, List<SalesProhibitionsVo>> getForbiddenSalesSiteBySkuToSon(List<String> sonSkuList) throws Exception;

    /**
     * 查询spu fullpathcode
     * @param spu
     * @return
     */
    String getFullpathcode(String spu);


    /**
     * 查询spu 产品开发
     * @param spu
     * @return
     */
    String getDevEmpName(String spu);


    /**
     * spu 查询最重的单品信息
     * @param spu
     */
    ProductInfo getMaxWeightSingleItem(String spu);


    /**
     * spuList 查询最重的单品信息
     */
    ProductInfo getMaxWeightSingleItemForSpuList(List<String> spuList);



    /**
     *  skuList 查询最重的单品信息 (子sku)
     * @param skuList
     */
    ProductInfo getMaxWeightSingleItemBySonSkuList(List<String> skuList);


    Map<String, Integer> findSkuAndStatusBySku(List<String> skuList);


    /**
     * 获取当前sku的主sku状态和当前输入的sku状态
     * 主sku状态按所有子sku状态排序
     * 试卖>正常>清仓>暂停>休假>甩卖>停产>存档>废弃
     */
    Map<String, Integer> findCurrentSkuMainSonSkuStatus(String sku);


    /**
     * 获取货号的 fullpathcode
     * @param skuList
     * @return
     */
    List<String> getFullpathcodeListBySonSkuList(List<String> skuList);

    /**
     * 分页获取子sku
     */
    List<String> pageGetSonSkuList(Integer page, Integer size);


    /**
     * 获取单品状态变更时间范围内的sku
     */
    List<String> getSkuByItemStatusChangTimeRang(Long starTime, Long endTime);

    /**
     * 通过spu获取所有的标签code
     * @param spu
     * @return
     */
    List<String> getAllTagCodeListBySpu(String spu);

    /**
     * 获取spu侵权信息明细
     * @param spu spu
     * @return 侵权信息
     */
    List<SingleItemEsForbidInfoDO> getSpuInfringementSaleProhibitions(List<String> spu);

    List<SingleItemEs> getSkuCreateAt(List<String> articleNumbers);

    /**
     * 获取sku禁售信息和特殊标签
     * @param spu spu
     * @return 侵权信息
     */
    Map<String, ForbiddenAndSpecical> getForbiddenAndSpecicalBySonSku(List<String> skuList);

    /**
     * 获取sku插头规格
     * @param skuList 子sku
     * @return sonSku,plugSpecification
     */
    List<SingleItemEs> getSkuPlugSpecification(List<String> skuList);

    /**
     * 根据主sku查询子sku
     * @param mainSkuList
     * @return
     */
    List<String> getSonSkuListByMainSku(List<String> mainSkuList);


    /**
     * 获取某些单品状态变更时间范围内的sku
     */
    List<String> getSkuByItemStatusListAndChangTime(Long starTime, Long endTime, List<Integer> itemStatusList);

    List<SingleItemEs> getSkuInSingleTime(List<String> articleNumbers);

    List<PackageInfo> getPackageInfoBySku(List<String> skuList);


    List<SingleItemEs> getSingleItemBySkuList(List<String> skuList);

    List<SingleItemEs> getSkuSpecialGoodsCode(List<String> skus);
}


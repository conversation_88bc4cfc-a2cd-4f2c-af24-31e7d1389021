package com.estone.erp.publish.platform.mapper;

import com.estone.erp.publish.platform.model.OperateLog;
import com.estone.erp.publish.platform.model.OperateLogExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface OperateLogMapper {
    int countByExample(OperateLogExample example);

    int deleteByPrimaryKey(List<Long> ids);

    int insert(OperateLog record);

    OperateLog selectByPrimaryKey(Long id);

    List<OperateLog> selectByExample(OperateLogExample example);

    int updateByExampleSelective(@Param("record") OperateLog record, @Param("example") OperateLogExample example);

    int updateByPrimaryKeySelective(OperateLog record);

    void insertBatch(@Param("list") List<OperateLog> list);
}
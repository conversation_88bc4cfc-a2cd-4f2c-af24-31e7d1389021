package com.estone.erp.publish.platform.service.impl;

import com.estone.erp.common.model.api.CQuery;
import com.estone.erp.common.model.api.CQueryResult;
import com.estone.erp.common.util.WebUtils;
import com.estone.erp.publish.platform.mapper.CustomFieldTemplateMapper;
import com.estone.erp.publish.platform.model.CustomFieldTemplate;
import com.estone.erp.publish.platform.model.CustomFieldTemplateCriteria;
import com.estone.erp.publish.platform.model.CustomFieldTemplateExample;
import com.estone.erp.publish.platform.service.CustomFieldTemplateService;
import java.sql.Timestamp;
import java.util.List;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

/**
 * <AUTHOR> custom_field_template
 * 2022-04-03 16:54:11
 */
@Service("customFieldTemplateService")
@Slf4j
public class CustomFieldTemplateServiceImpl implements CustomFieldTemplateService {
    @Resource
    private CustomFieldTemplateMapper customFieldTemplateMapper;

    @Override
    public int countByExample(CustomFieldTemplateExample example) {
        Assert.notNull(example, "example is null!");
        return customFieldTemplateMapper.countByExample(example);
    }

    @Override
    public CQueryResult<CustomFieldTemplate> search(CQuery<CustomFieldTemplateCriteria> cquery) {
        Assert.notNull(cquery, "cquery is null!");
        CustomFieldTemplateCriteria query = cquery.getSearch();
        CustomFieldTemplateExample example = query.getExample();
        long total = 0;
        int totalPages = 0;
        // 是否分页
        if (cquery.isPageReqired()) {
            total = customFieldTemplateMapper.countByExample(example);
            totalPages = (int) Math.ceil((double) total / cquery.getLimit());
            example.setLimit(cquery.getLimit());
            example.setOffset(cquery.getOffset());
        }
        List<CustomFieldTemplate> customFieldTemplates = customFieldTemplateMapper.selectByExample(example);
        // 组装结果
        CQueryResult<CustomFieldTemplate> result = new CQueryResult<>();
        result.setTotal(total);
        result.setTotalPages(totalPages);
        result.setRows(customFieldTemplates);
        return result;
    }

    @Override
    public CustomFieldTemplate selectByPrimaryKey(Integer id) {
        Assert.notNull(id, "id is null!");
        return customFieldTemplateMapper.selectByPrimaryKey(id);
    }

    @Override
    public List<CustomFieldTemplate> selectByExample(CustomFieldTemplateExample example) {
        Assert.notNull(example, "example is null!");
        return customFieldTemplateMapper.selectByExample(example);
    }

    @Override
    public int insert(CustomFieldTemplate record) {
        Assert.notNull(record, "record is null!");
        // 默认加时间和人
        record.setCreateDate(new Timestamp(System.currentTimeMillis()));
        record.setLastUpdateDate(new Timestamp(System.currentTimeMillis()));
        return customFieldTemplateMapper.insert(record);
    }

    @Override
    public int updateByPrimaryKeySelective(CustomFieldTemplate record) {
        Assert.notNull(record, "record is null!");
        // 默认加修改时间和修改人
        record.setLastUpdateDate(new Timestamp(System.currentTimeMillis()));
        return customFieldTemplateMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public int updateByExampleSelective(CustomFieldTemplate record, CustomFieldTemplateExample example) {
        Assert.notNull(record, "record is null!");
        // 默认加修改时间和修改人
        record.setLastUpdateDate(new Timestamp(System.currentTimeMillis()));
        return customFieldTemplateMapper.updateByExampleSelective(record, example);
    }

    @Override
    public int deleteByPrimaryKey(List<Integer> ids) {
        Assert.notNull(ids, "ids is null!");
        return customFieldTemplateMapper.deleteByPrimaryKey(ids);
    }
}
package com.estone.erp.publish.elasticsearch.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.elasticsearch.annotations.DateFormat;
import org.springframework.data.elasticsearch.annotations.Document;
import org.springframework.data.elasticsearch.annotations.Field;
import org.springframework.data.elasticsearch.annotations.FieldType;

import java.util.Date;
import java.util.List;

@Data
@Document(indexName = "amazon_tort_complaint_info", type = "amazonTortComplaintInfo")
public class EsAmazonTortComplaintInfo {


    @Id
    @Field(type = FieldType.Text)
    private String id;

    /**
     * 销售站点账号
     */
    @Field(type = FieldType.Keyword)
    private String saleAccount;

    /**
     * 市场ID
     */
    @Field(type = FieldType.Keyword)
    private String marketplaceId;

    /**
     * 店铺ID
     */
    @Field(type = FieldType.Keyword)
    private String merchantId;

    /**
     * 侵犯知识产权
     */
    @Field(type = FieldType.Nested)
    private List<AmazonProductInfo> infringementIntellectualPropertyRights;

    /**
     * 知识产权投诉
     */
    @Field(type = FieldType.Nested)
    private List<AmazonProductInfo> intellectualPropertyComplaints;

    /**
     * 商品真伪投诉
     */
    @Field(type = FieldType.Nested)
    private List<AmazonProductInfo> goodsAuthenticityComplaints;


    /**
     * 创建时间
     */
    @Field(type = FieldType.Date, format = DateFormat.custom, pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /**
     * 更新时间,格式 2021-11-16 11:30:31
     */
    @Field(type = FieldType.Date, format = DateFormat.custom, pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;


}
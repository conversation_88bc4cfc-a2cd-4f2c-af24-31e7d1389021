package com.estone.erp.publish.elasticsearch.model.beanrequest;


import lombok.Data;

import java.util.List;

@Data
public class EsMkdItemRequest {
    private Integer pageSize = 200;
    private Integer pageIndex = 0;
    private String [] fields = {"id","sku","spu"};

    private String id;

    private String account;

    private List<String> cbtItemId;
    /**
     * 店铺
     */
    private List<String> accountNumber;

    /**
     * 站点
     */
    private String marketplaceSite;

    /**
     * 站点
     */
    private List<String> site;



    private List<String> itemId;


    private List<String> sellerSku;


    private List<String> spu;

    private List<String> sku;

    /**
     * 单品状态
     */
    private List<String> itemStatus;

    private List<String> status;
    /**
     * 价格
     */
    private List<Double> usPrice;

    /**
     * 毛利率
     */
    private List<Double> profitMargin;

    /**
     * 库存
     */
    private List<Integer> inventory;

    /**
     * 禁售平台
     */
    private List<String> forbidChannel;

    /**
     * 产品标签
     */
    private List<String> tag;

    /**
     * 特殊标签
     */
    private List<String> specialTag;

    /**
     * 是否变体
     */
    private Integer isVariation;

    /**
     * 变体ID
     */
    private String variationId;

    private List<String> excludeSite;

    private List<String> excludeStatus;

    /**
     * 大于同步时间
     */
    private String gtSyncDate;

    /**
     * 导出数据表头字段,用逗号分割
     */
    private String attrs;

    /**
     * 禁售原因
     */
    private List<String> infringementObjs;

    /**
     * 禁售类型
     */
    private List<String> infringementTypeNames;

    /**
     * 平台禁售站点
     */
    private List<String> prohibitionSites;

    /**
     * 是否新品
     */
    private Boolean newState;

    /**
     * 是否促销
     */
    private Boolean isPromotion;

    /**
     * 是否平台促销
     */
    private Boolean isPlatformPromotion;

    /**
     * 排除活动Id
     */
    private String excludePromotionId;

    /**
     * 活动Id
     */
    private String promotionId;

    /**
     * 活动Id
     */
    private Integer promotionStatus;

    /**
     * 活动Id
     */
    private List<String> promotionIds;

    /**
     * 数据来源
     */
    private Integer skuDataSource;

    /**
     * 组合状态
     */
    private Integer composeStatus;
}
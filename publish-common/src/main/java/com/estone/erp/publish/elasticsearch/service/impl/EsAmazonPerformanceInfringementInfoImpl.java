package com.estone.erp.publish.elasticsearch.service.impl;

import com.estone.erp.publish.elasticsearch.dao.EsAmazonPerformanceInfringementInfoRepository;
import com.estone.erp.publish.elasticsearch.model.EsAmazonPerformanceInfringementInfo;
import com.estone.erp.publish.elasticsearch.model.beanrequest.EsAmazonPerformanceInfringementInfoRequest;
import com.estone.erp.publish.elasticsearch.service.EsAmazonPerformanceInfringementInfoService;
import com.estone.erp.publish.elasticsearch.util.AmazonModelUtil;
import org.apache.commons.collections.CollectionUtils;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.elasticsearch.core.ElasticsearchRestTemplate;
import org.springframework.data.elasticsearch.core.query.NativeSearchQuery;
import org.springframework.data.elasticsearch.core.query.NativeSearchQueryBuilder;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
public class EsAmazonPerformanceInfringementInfoImpl implements EsAmazonPerformanceInfringementInfoService {
    @Autowired
    EsAmazonPerformanceInfringementInfoRepository esAmazonPerformanceInfringementInfoRepository;
    @Autowired
    private ElasticsearchRestTemplate elasticsearchRestTemplate1;
    @Override
    public long count() {
        return esAmazonPerformanceInfringementInfoRepository.count();
    }

    @Override
    public void save(EsAmazonPerformanceInfringementInfo esAmazonPerformanceInfringementInfo) {
        if (esAmazonPerformanceInfringementInfo != null) {
            elasticsearchRestTemplate1.save(esAmazonPerformanceInfringementInfo);
        }
    }

    @Override
    public void saveAll(List<EsAmazonPerformanceInfringementInfo> esAmazonPerformanceInfringementInfos) {
        if (CollectionUtils.isNotEmpty(esAmazonPerformanceInfringementInfos)) {
            elasticsearchRestTemplate1.save(esAmazonPerformanceInfringementInfos);
        }
    }

    @Override
    public void deleteById(String id) {
        esAmazonPerformanceInfringementInfoRepository.deleteById(id);
    }

    @Override
    public List<EsAmazonPerformanceInfringementInfo> getAllById(List<String> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return null;
        }
        List<EsAmazonPerformanceInfringementInfo> list = new ArrayList<>();
        // 查询条件
        NativeSearchQueryBuilder queryBuilder = new NativeSearchQueryBuilder();
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        //利用反射根据传入的对象，组装查询条件
        EsAmazonPerformanceInfringementInfoRequest request = new EsAmazonPerformanceInfringementInfoRequest();
        request.setId(ids);
        AmazonModelUtil.getEsModelByReflect(boolQueryBuilder, request);
        //创建查询条件构造器
        queryBuilder.withQuery(boolQueryBuilder);
        //构建分页
        // 每页条数
        int pageSize = 1000;
        //es的分页的页码从0开始
        int pageIndex = 0;

        while (true) {
            queryBuilder.withPageable(PageRequest.of(pageIndex, pageSize));
            NativeSearchQuery searchQuery = queryBuilder.build();
            searchQuery.setTrackTotalHits(true);
            Page<EsAmazonPerformanceInfringementInfo> search = esAmazonPerformanceInfringementInfoRepository.search(searchQuery);
            if (CollectionUtils.isEmpty(search.getContent())) {
                break;
            }
            list.addAll(search.getContent());
            pageIndex++;
        }
        return list;
    }


    @Override
    public Page<EsAmazonPerformanceInfringementInfo> page(EsAmazonPerformanceInfringementInfoRequest esAmazonPerformanceInfringementInfoRequest, int pageSize, int pageIndex) {
        if (esAmazonPerformanceInfringementInfoRequest == null) {
            return null;
        }
        // 查询条件
        NativeSearchQueryBuilder queryBuilder = new NativeSearchQueryBuilder();
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        //利用反射根据传入的对象，组装查询条件
        AmazonModelUtil.getEsModelByReflect(boolQueryBuilder, esAmazonPerformanceInfringementInfoRequest);
        //创建查询条件构造器
        queryBuilder.withQuery(boolQueryBuilder);
        //构建分页
        // 每页条数
        pageSize = pageSize == 0 ? 10 : pageSize;
        //es的分页的页码从0开始
        pageIndex = pageIndex < 1 ? 0 : pageIndex;

        queryBuilder.withPageable(PageRequest.of(pageIndex, pageSize));
        NativeSearchQuery searchQuery = queryBuilder.build();
        searchQuery.setTrackTotalHits(true);
        try {
            return esAmazonPerformanceInfringementInfoRepository.search(searchQuery);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }
}

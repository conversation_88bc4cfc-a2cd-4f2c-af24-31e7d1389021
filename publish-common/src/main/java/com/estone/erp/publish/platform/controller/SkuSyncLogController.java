package com.estone.erp.publish.platform.controller;

import com.alibaba.fastjson.TypeReference;
import com.estone.erp.common.model.api.ApiRequestParam;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.model.api.CQuery;
import com.estone.erp.common.model.api.CQueryResult;
import com.estone.erp.common.util.Asserts;
import com.estone.erp.publish.common.ErrorCode;
import com.estone.erp.publish.platform.model.SkuSyncLog;
import com.estone.erp.publish.platform.model.SkuSyncLogCriteria;
import com.estone.erp.publish.platform.service.SkuSyncLogService;
import javax.annotation.Resource;
import org.apache.commons.lang.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR> sku_sync_log
 * 2022-11-21 14:48:34
 */
@RestController
@RequestMapping("skuSyncLog")
public class SkuSyncLogController {
    @Resource
    private SkuSyncLogService skuSyncLogService;

    @PostMapping
    public ApiResult<?> postSkuSyncLog(@RequestBody(required = true) ApiRequestParam<String> requestParam) {
        String method = requestParam.getMethod();
        if (StringUtils.isNotBlank(method)) {
            switch (method) {
                case "searchSkuSyncLog": // 查询列表
                    CQuery<SkuSyncLogCriteria> cquery = requestParam.getArgsValue(new TypeReference<CQuery<SkuSyncLogCriteria>>() {});
                    Asserts.isTrue(cquery != null, ErrorCode.PARAM_EMPTY_ERROR);
                    CQueryResult<SkuSyncLog> results = skuSyncLogService.search(cquery);
                    return results;
                case "addSkuSyncLog": // 添加
                    SkuSyncLog skuSyncLog = requestParam.getArgsValue(new TypeReference<SkuSyncLog>() {});
                    skuSyncLogService.insert(skuSyncLog);
                    return ApiResult.newSuccess(skuSyncLog);
                }
        }
        return ApiResult.newSuccess();
    }

    @GetMapping(value = "/{id}")
    public ApiResult<?> getSkuSyncLog(@PathVariable(value = "id", required = true) Integer id) {
        SkuSyncLog skuSyncLog = skuSyncLogService.selectByPrimaryKey(id);
        return ApiResult.newSuccess(skuSyncLog);
    }

    @PutMapping(value = "/{id}")
    public ApiResult<?> putSkuSyncLog(@PathVariable(value = "id", required = true) Integer id, @RequestBody ApiRequestParam<String> requestParam) {
        String method = requestParam.getMethod();
        if (StringUtils.isNotBlank(method)) {
            switch (method) {
                case "updateSkuSyncLog": // 单个修改
                    SkuSyncLog skuSyncLog = requestParam.getArgsValue(new TypeReference<SkuSyncLog>() {});
                    skuSyncLogService.updateByPrimaryKeySelective(skuSyncLog);
                    return ApiResult.newSuccess(skuSyncLog);
                }
        }
        return ApiResult.newSuccess();
    }
}
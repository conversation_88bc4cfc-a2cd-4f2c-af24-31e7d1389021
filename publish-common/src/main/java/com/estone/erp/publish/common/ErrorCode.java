package com.estone.erp.publish.common;

import com.estone.erp.common.component.IErrorCode;

/***
 * publish自定义错误代码，1001000-1002000, 错误信息在resources文件中定义
 */
public enum ErrorCode implements IErrorCode {
    PUBLISH_ERROR(1001000, 500, "L-ERROR-CODE-1001000"),
    PARAM_EMPTY_ERROR(1001001, 500, "L-ERROR-CODE-1001001"),
    COPY_NUM_ERROR(1001002, 500, "L-ERROR-CODE-1001002"),
    TEMPLATE_EMPTY_ERROR(1001003, 500, "L-ERROR-CODE-1001003")
    ;
    
    private ErrorCode(int code, int httpStatus, String reason) {
        this.code = code;
        this.httpStatus = httpStatus;
        this.reason = reason;
    }

    /**
     * 自定义异常代码
     */
    private int code;

    /**
     * http错误状态码
     */
    private int httpStatus;

    /**
     * 异常提示信息
     */
    private String reason;

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public int getHttpStatus() {
        return httpStatus;
    }

    public void setHttpStatus(int httpStatus) {
        this.httpStatus = httpStatus;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }
}

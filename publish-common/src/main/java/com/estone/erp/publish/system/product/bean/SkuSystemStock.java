package com.estone.erp.publish.system.product.bean;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/4/23 10:48
 */
@Data
public class SkuSystemStock {

    /**
     * sku
     */
    private String sku;

    /**
     * 可用库存
     */
    private Integer usableStock;

    /**
     * 在途+待上架
     */
    private Integer waitingOnWayStock;

    /**
     * 待发
     */
    private Integer pendingStock;

    /**
     * 可用+在途+待上架-待发
     */
    private Integer usableWaitingPendingStock;

    /**
     * 可用-待发
     */
    private Integer usablePendingStock;

    /**
     * 南宁可用库存
     */
    private Integer nnUsableStock;

    /**
     * 南宁待发
     */
    private Integer nnPendingStock;
}

package com.estone.erp.publish.elasticsearch4.model;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.estone.erp.publish.component.converter.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.elasticsearch.annotations.DateFormat;
import org.springframework.data.elasticsearch.annotations.Document;
import org.springframework.data.elasticsearch.annotations.Field;
import org.springframework.data.elasticsearch.annotations.FieldType;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/9/18 17:27
 */
@Data
@Document(indexName = "fyndiq_item")
public class EsFyndiqItem {

    /**
     * 唯一id
     */
    @Id
    @Field(type = FieldType.Keyword)
    @ExcelIgnore
    private String id;

    /**
     * 店铺
     */
    @Field(type = FieldType.Keyword)
    @ExcelProperty(value = "店铺", order = 1)
    private String accountNumber;

    /**
     * 卖家sku
     */
    @Field(type = FieldType.Keyword)
    @ExcelProperty(value = "sellerSku", order = 2)
    private String sellerSku;

    /**
     * 价格json
     */
    @Field(type = FieldType.Keyword)
    @ExcelIgnore
    private String priceText;

    /**
     * 原价json
     */
    @Field(type = FieldType.Keyword)
    @ExcelIgnore
    private String originalPriceText;

    /**
     * 瑞典站点价格
     */
    @Field(type = FieldType.Double)
    @ExcelProperty(value = "价格", order = 11)
    private Double sePrice;

    /**
     * 挪威站点价格
     */
    @Field(type = FieldType.Double)
    @ExcelIgnore
    private Double noPrice;

    /**
     * 丹麦站点价格
     */
    @Field(type = FieldType.Double)
    @ExcelIgnore
    private Double dkPrice;

    /**
     * 芬兰站点价格
     */
    @Field(type = FieldType.Double)
    @ExcelIgnore
    private Double fiPrice;

    /**
     * 库存
     */
    @Field(type = FieldType.Integer)
    @ExcelProperty(value = "库存", order = 10)
    private Integer quantity;

    /**
     * 平台分类
     */
    @Field(type = FieldType.Keyword)
    @ExcelProperty(value = "平台分类", order = 4)
    private String category;

    /**
     * 标题
     */
    @Field(type = FieldType.Keyword)
    @ExcelProperty(value = "标题", order = 3)
    private String title;

    /**
     * 描述
     */
    @Field(type = FieldType.Keyword)
    @ExcelIgnore
    private String description;

    /**
     * 主图
     */
    @Field(type = FieldType.Keyword)
    @ExcelProperty(value = "主图", order = 0)
    private String mainImage;

    /**
     * 品牌
     */
    @Field(type = FieldType.Keyword)
    @ExcelIgnore
    private String brand;

    /**
     * 产品id
     */
    @Field(type = FieldType.Keyword)
    @ExcelIgnore
    private String productId;

    /**
     * 产品状态
     */
    @Field(type = FieldType.Keyword)
    @ExcelProperty(value = "状态", order = 12)
    private String productStatus;

    /**
     * 平台状态
     */
    @Field(type = FieldType.Keyword)
    @ExcelIgnore
    private String fyndiqStatus;

    /**
     * 销售站点
     */
    @Field(type = FieldType.Keyword)
    @ExcelIgnore
    private List<String> markets;

    /**
     * SPU
     */
    @Field(type = FieldType.Keyword)
    @ExcelProperty(value = "SPU", order = 5)
    private String spu;

    /**
     * SKU
     */
    @Field(type = FieldType.Keyword)
    @ExcelProperty(value = "SKU", order = 6)
    private String sku;

    /**
     * 单品状态（产品信息字段）
     */
    @Field(type = FieldType.Keyword)
    @ExcelProperty(value = "单品状态", order = 7, converter = SkuStatusCodeConverter.class)
    private String skuStatus;

    /**
     * 类目id
     */
    @Field(type = FieldType.Keyword)
    @ExcelIgnore
    private String proCategoryId;

    /**
     * 类目中文名
     */
    @Field(type = FieldType.Keyword)
    @ExcelProperty(value = "分类", order = 13)
    private String proCategoryCnName;

    /**
     * 禁售平台（逗号拼接）
     */
    @Field(type = FieldType.Keyword)
    @ExcelProperty(value = "禁售平台", order = 18)
    private String forbidChannel;

    /**
     * 禁售站点
     */
    @Field(type = FieldType.Keyword)
    @ExcelProperty(value = "禁售站点", order = 19, converter = ListStringFormatConverter.class)
    private List<String> prohibitionSites;

    /**
     * 禁售类型
     */
    @Field(type = FieldType.Keyword)
    @ExcelProperty(value = "禁售类型", order = 16)
    private String infringementTypeName;

    /**
     * 禁售原因
     */
    @Field(type = FieldType.Keyword)
    @ExcelProperty(value = "禁售原因", order = 17)
    private String infringementObj;

    /**
     * 产品标签code
     */
    @Field(type = FieldType.Keyword)
    @ExcelIgnore
    private String tagCodes;

    /**
     * 产品标签中文名
     */
    @Field(type = FieldType.Keyword)
    @ExcelProperty(value = "产品标签", order = 14)
    private String tagNames;

    /**
     * 特殊标签code
     */
    @Field(type = FieldType.Keyword)
    @ExcelIgnore
    private String specialGoodsCode;

    /**
     * 特殊标签中文名
     */
    @Field(type = FieldType.Keyword)
    @ExcelProperty(value = "特殊标签", order = 15)
    private String specialGoodsName;

    /**
     * 促销状态 0 没有  1，是  2，否  特别注意 0 和 2 都是(否)，1是（是）
     */
    @Field(type = FieldType.Keyword)
    @ExcelProperty(value = "促销状态", order = 23, converter = PromotionConverter.class)
    private Integer promotion;

    /**
     * 新品状态
     */
    @Field(type = FieldType.Boolean)
    @ExcelProperty(value = "新品状态", order = 24, converter = BooleanCodeConverter.class)
    private Boolean newState;

    /**
     * 数据来源
     */
    @Field(type = FieldType.Keyword)
    @ExcelProperty(value = "数据来源", order = 8, converter = SkuDataSourceConverter.class)
    private Integer skuDataSource;

    /**
     * 组合状态 8003 启用 8004 禁用
     */
    @Field(type = FieldType.Keyword)
    @ExcelProperty(value = "组合状态", order = 9, converter = ComposeStatusConverter.class)
    private Integer composeStatus;

    /**
     * 同步产品信息时间
     */
    @Field(type = FieldType.Date, format = DateFormat.custom, pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern ="yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ExcelIgnore
    private Date syncProdDate;

    /**
     * 同步时间
     */
    @Field(type = FieldType.Date, format = DateFormat.custom, pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern ="yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ExcelProperty(value = "同步时间", order = 25)
    private Date syncDate;

    /**
     * 创建时间
     */
    @Field(type = FieldType.Date, format = DateFormat.custom, pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern ="yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ExcelProperty(value = "创建时间", order = 26)
    private Date creationDate;

    /**
     * 创建人
     */
    @Field(type = FieldType.Keyword)
    @ExcelIgnore
    private String CreatedBy;

    /**
     * 修改时间
     */
    @Field(type = FieldType.Date, format = DateFormat.custom, pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern ="yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ExcelProperty(value = "修改时间", order = 27)
    private Date lastUpdateDate;

    /**
     * 修改人
     */
    @Field(type = FieldType.Keyword)
    @ExcelIgnore
    private String lastUpdatedBy;

    // -- 缺失的字段
    /**
     * 分类集合
     */
    @Field(type = FieldType.Keyword)
    @ExcelIgnore
    private List<String> categories;

    /**
     * 发货时间
     */
    @Field(type = FieldType.Keyword)
    @ExcelIgnore
    private String shippingTimeText;

}

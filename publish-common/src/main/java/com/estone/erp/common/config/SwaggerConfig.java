package com.estone.erp.common.config;

import com.estone.erp.common.util.constant.Constant;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;
import springfox.documentation.builders.ApiInfoBuilder;
import springfox.documentation.builders.ParameterBuilder;
import springfox.documentation.builders.PathSelectors;
import springfox.documentation.builders.RequestHandlerSelectors;
import springfox.documentation.schema.ModelRef;
import springfox.documentation.service.ApiInfo;
import springfox.documentation.service.Contact;
import springfox.documentation.service.Parameter;
import springfox.documentation.spi.DocumentationType;
import springfox.documentation.spring.web.plugins.Docket;
import springfox.documentation.swagger2.annotations.EnableSwagger2;

import java.util.ArrayList;
import java.util.List;

/***
 * swaggger配置文件， 依赖项目在application.yml中配置
 * 
 * <AUTHOR>
 *
 */
@Configuration
@EnableSwagger2
@EnableConfigurationProperties
@Profile({ "dev", "test", "local" })
@ConfigurationProperties("swagger")
public class SwaggerConfig implements WebMvcConfigurer {
    /**
     * 服务名
     */
	@Value("${swagger.service.name}")
    private String serviceName;

    /**
     * 联系人
     */
	@Value("${swagger.contact.name}")
    private String contactName;

    /**
     * 联系人url
     */
	@Value("${swagger.contact.url}")
    private String contactUrl;

    /**
     * 联系人email
     */
	@Value("${swagger.contact.email}")
    private String contactEmail;

    /**
     * 服务描述
     */
    @Value("${swagger.service.description}")
    private String description;

    /**
     * 属性Resolver
     */
  //  private RelaxedPropertyResolver propertyResolver;

    public SwaggerConfig() {
    }

    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        registry.addResourceHandler(new String[] { "swagger-ui.html" })
                .addResourceLocations(new String[] { "classpath:/META-INF/resources/" });
        registry.addResourceHandler(new String[] { "/webjars*" })
                .addResourceLocations(new String[] { "classpath:/META-INF/resources/webjars/" });
    }

    @Bean
    public Docket createRestApi() {
        return (new Docket(DocumentationType.SWAGGER_2)).apiInfo(this.apiInfo()).select()
                .apis(RequestHandlerSelectors.basePackage("com.estone.erp")).paths(PathSelectors.any()).build()
                .pathMapping("/").globalOperationParameters(setHeaderToken());
    }

    private List<Parameter> setHeaderToken() {
        ParameterBuilder paramBuilder = new ParameterBuilder();
        List<Parameter> params = new ArrayList<>(1);
        paramBuilder.name(Constant.ACCESS_TOKEN).description("token").modelRef(new ModelRef("string")).parameterType("header")
                .required(false).build();
        params.add(paramBuilder.build());
        return params;
    }

    private ApiInfo apiInfo() {
        Contact contact = new Contact(contactName, contactUrl, contactEmail);
        return new ApiInfoBuilder().title(this.serviceName + " Restful APIs").description(this.description)
                .contact(contact).version("1.0").build();
    }



   /* @Override
    public void setEnvironment(Environment environment) {
        this.propertyResolver = new RelaxedPropertyResolver(environment, (String) null);
        this.serviceName = this.propertyResolver.getProperty("swagger.service.name");
        this.description = this.propertyResolver.getProperty("swagger.service.description");
        this.contactName = this.propertyResolver.getProperty("swagger.contact.name");
        this.contactUrl = this.propertyResolver.getProperty("swagger.contact.url");
        this.contactEmail = this.propertyResolver.getProperty("swagger.contact.email");
    }*/
}

package com.estone.erp.common.util;

import java.io.InputStream;

import javax.xml.bind.JAXBContext;
import javax.xml.bind.Unmarshaller;

import org.springframework.core.io.Resource;

import lombok.extern.slf4j.Slf4j;

/***
 * xml工具类
 * 
 * <AUTHOR>
 *
 */
@Slf4j
public class XmlUtils {

    /**
     * 解析Xml文件，生成xml指定对象
     * 
     * @param resource 资源
     * @param clazz class<T>
     * @return T
     */
    @SuppressWarnings("unchecked")
    public static <T> T readFile(Resource resource, Class<T> clazz) {
        try {
            InputStream inputStream = resource.getInputStream();
            JAXBContext jc = JAXBContext.newInstance(clazz);
            Unmarshaller u = jc.createUnmarshaller();
            return (T) u.unmarshal(inputStream);
        }
        catch (Exception e) {
            log.error(e.getMessage(), e);
        }

        return null;
    }
}

package com.estone.erp.common.model;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 尺寸信息
 * <AUTHOR>
 */
@Data
public class PackageInfo {

    private String spu;

    private String sku;

    /**
     * 长 cm
     */
    private BigDecimal length;
    /**
     * 宽 cm
     */
    private BigDecimal wide;
    /**
     * 高 cm
     */
    private BigDecimal height;
    /**
     * 包装长 cm
     */
    private BigDecimal packLength;
    /**
     * 包装宽 cm
     */
    private BigDecimal packWidth;
    /**
     * 包装高 cm
     */
    private BigDecimal packHeight;

    public boolean checkPack() {
        return packHeight != null && packWidth != null && packLength != null;
    }

    public boolean checkProduct() {
        return length != null && wide != null && height != null;
    }

}

package com.estone.erp.common.constant;

/**
 * <AUTHOR>
 * @date 2019/12/18 10:30
 * @description
 */
public class RedisKeyConstant {

    //产品系统不再需要这个key值
    //public static final String PUBLISH_PRODUCT_STATISTICS = "publish-product-statistics";

    /**
     * 获取所有缓存sku：7天销量。（缓存目前是30分钟更新一次）
     * Map<String,String> map
     */
    public static final String FLINK_SKU_SEVEN_DAY_COUNT = "Sku_Seven_Day_Count";

//    /**
//     *  可使用工具类 SkuStockUtils.getAllocationQuan(sku)
//     * 获取sku 调拨库存
//     * 使用方法： key = PRODUCT_SKU_SAVE_ALLOCATION_QUAN + sku
//     */
//    public static final String PRODUCT_SKU_SAVE_ALLOCATION_QUAN = "PRODUCT_SKU_SAVE_ALLOCATION_QUAN";

    /**
     * 系统类目缓存
     */
    public static final String PRODUCT_SYSTEM_CATEGORY = "PRODUCT_SYSTEM_CATEGORY";

    /**
     * 深圳仓
     * 可用库存
     * 可使用工具类 SkuStockUtils.getAvableStock(sku)
     * 获取sku 库存
     * 使用方法： key = PRODUCT_SKU_SAVE_AVALIBLE_STOCK + sku
     */
    public static final String PRODUCT_SKU_SAVE_AVALIBLE_STOCK = "PRODUCT_SKU_SAVE_AVALIBLE_STOCK";

    /**
     * 深圳仓
     * 获取sku在途+待上架库存
     * 使用方法：key = PRODUCT_SKU_SAVE_WAITING_ON_WAY + sku
     */
    public static final String PRODUCT_SKU_SAVE_WAITING_ON_WAY = "PRODUCT_SKU_SAVE_WAITING_ON_WAY";

    /**
     * 深圳仓
     * 获取sku待发库存
     * 使用方法：key = PRODUCT_SKU_SAVE_PENDING + sku
     */
    public static final String PRODUCT_SKU_SAVE_PENDING = "PRODUCT_SKU_SAVE_PENDING";
    /**
     * 深圳仓 （采购维护）
     * 获取非待上架数量,刊登使用
     */
    public static final String PRODUCT_SKU_SAVE_NOT_WAIT_UP = "PRODUCT_SKU_SAVE_NOT_WAIT_UP";

    /**
     * 南宁仓
     * 获取非待上架数量
     */
    public static final String NN_PRODUCT_SKU_SAVE_NOT_WAIT_UP = "NN:PRODUCT_SKU_SAVE_NOT_WAIT_UP";

    /**
     * 南宁仓
     * 获取sku在途+待上架库存 （采购维护）
     */
    public static final String NN_PRODUCT_SKU_SAVE_WAITING_ON_WAY = "NN:PRODUCT_SKU_SAVE_WAITING_ON_WAY";

    /**
     * 南宁仓
     * 实时待发
     */
    public static final String NN_PRODUCT_SKU_SAVE_PENDING = "NN:PRODUCT_SKU_SAVE_PENDING";
    /**
     * 南宁仓
     * 可用库存
     */
    public static final String NN_PRODUCT_SKU_SAVE_AVALIBLE_STOCK = "NN:PRODUCT_SKU_SAVE_AVALIBLE_STOCK";

}

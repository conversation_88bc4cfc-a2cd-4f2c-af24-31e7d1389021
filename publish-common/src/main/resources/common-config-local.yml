spring:
  main:
    allow-bean-definition-overriding: true
#  rabbitmq:
#    addresses: *************:5672
#  redis:
#    cluster:
#      nodes: ***********:7001,***********:7002,***********:7003,***********:7004,***********:7005,***********:7006
#    timeout: 6000
#    lettuce:
#      pool:
#        maxActive: 100
#        maxIdle: 80
#        minIdle: 5
#        maxWait: 6000
#  data:
#    mongodb:
#      message:
#        servers:
#          - host: ************
#            port: 27017
#          - host: *************
#            port: 27017
#        database: erp_message
#        username: smt
#        password: smt
  http: 
    multipart: 
      enabled: true
      max-file-size: 10MB
      max-request-size: 100MB

erp:
  ftp:
    host: *************
    port: 21
    username: java
    password: java
  common:        
    ignore-token-urls: /info,/favicon.ico,vice/logout,/logisticsInfo/login,/logistics-engine-service/getLabel,/logistics-engine/getLabel,/logistics-engine-service/printLabel,/logistics-engine/printLabel,/message-routing-information,/swagger*/**,/v2/api-docs/**,/webjars*/**,/wishFinance/syncAccountBalance,/wishFinance/syncOrderNotPay,/wishFinance/syncOrderPay,/wishFinance/getLastSyncOrderPayFlag,/purchaseOrder/updatePurchaseLogisticsByPlatformOrderNo,/saleaccount/**,/hystrix.stream,/api/template/**,/api/productDev/**
    saleChannels: 10,11street,Amazon,AmazonFBA,Ebay,factorymarket,Intmall,Joom,Lazada,Mall.my,Shopee,SMT,Tophatter,VOVA,Wish,XJ,GoAfrica,HZJiaYun,Linio,Offline,paytm,SHOPPO,TaoBao,yandex
  # 文件服务器相关
  seaweed:
    url: http://************:8888
    max-total: 200
    publish:
      url: http://*************:8888
      max-total: 200

### xxl-job admin address list, such as "http://address" or "http://address01,http://address02"
#xxl:
#  job:
#    admin:
#      addresses: http://*************:8181/xxl-job-admin
#    accessToken: ''
#    executor:
#      address: ''
#      ip:
#      port: 9999
#      logretentiondays: 7
#      logpath: /var/log/xxl-job
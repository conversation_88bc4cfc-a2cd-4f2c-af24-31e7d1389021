package com.estone.erp.publish.nocnoc.mq.consumer;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.estone.erp.common.mq.PublishQueues;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.elasticsearch2.model.EsNocnocItem;
import com.estone.erp.publish.elasticsearch2.model.beanrequest.EsNocnocItemRequest;
import com.estone.erp.publish.mq.bean.ChangeSku;
import com.estone.erp.publish.mq.util.ChangeSkuConsumerUtils;
import com.estone.erp.publish.nocnoc.service.NocnocItemEsService;
import com.estone.erp.publish.nocnoc.util.NocnocItemUtils;
import com.estone.erp.publish.platform.model.ChangeSkuLog;
import com.estone.erp.publish.platform.service.ChangeSkuLogService;
import com.estone.erp.publish.system.product.ProductInfoVO;
import com.estone.erp.publish.system.product.ProductUtils;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 同步单品信息到listing
 * <AUTHOR>
 * @date 2022/10/12 17:20
 */
@Slf4j
@Component
public class SyncNocnocProductInfoMqListener {

    @Resource
    private ChangeSkuLogService changeSkuLogService;

    @Resource
    private NocnocItemEsService nocnocItemEsService;

    /**
     * 最大重试次数
     */
    private static final int MAX_RETRY_COUNT = 3;

    @RabbitListener(queues = PublishQueues.NOCNOC_SYNC_PRODUCT_INFO_QUEUE, containerFactory = "batchAntifollowConsumeFactory")
    public void syncNocnocProductInfo(Message message, Channel channel) throws IOException {
        try {
            // 获取消息体
            String body = new String(message.getBody(), StandardCharsets.UTF_8);
            if (StringUtils.isBlank(body)) {
                return;
            }

            ChangeSku changeSku;
            try {
                changeSku = JSON.parseObject(body, new TypeReference<ChangeSku>() {
                });
            } catch (Exception e) {
                log.error("解析mq消息体异常 -> {}", body);
                return;
            }

            Boolean isSuccess = executeUpdate(changeSku);
            if(isSuccess) {
                // 确认消息并删除redis重试次数
                ChangeSkuConsumerUtils.confirmAndDelete(channel, message);
            } else {
                // 重试
                ChangeSkuConsumerUtils.retry(channel, message, MAX_RETRY_COUNT, SaleChannel.CHANNEL_NOCNOC);
            }
        } catch (Exception e) {
            // 重试
            ChangeSkuConsumerUtils.retry(channel, message, MAX_RETRY_COUNT, SaleChannel.CHANNEL_NOCNOC);
            log.error("NOCNOC NOCNOC_SYNC_PRODUCT_INFO_QUEUE Exception error: {}", e.getMessage());
        }
    }

    private Boolean executeUpdate(ChangeSku changeSku) {
        List<String> skuList = changeSku.getSkuList();
        if (CollectionUtils.isEmpty(skuList)) {
            log.error("更新在线列表单品状态信息存在sku为空的数据" + JSON.toJSONString(changeSku));
            return true;
        }

        List<String> accountNumberList = changeSku.getAccountNumberList();
        syncProductInfo(skuList, accountNumberList);

        // 如果日志id不为空 修改日志状态
        Long logId = changeSku.getLogId();
        if (null != logId) {
            // 修改日志状态
            ChangeSkuLog changeSkuLog = new ChangeSkuLog();
            changeSkuLog.setId(logId.intValue());
            changeSkuLog.setStatus(1);
            changeSkuLogService.updateByPrimaryKeySelective(changeSkuLog);
        }

        return true;
    }

    private void syncProductInfo(List<String> skuList, List<String> accountNumberList) {
        if(CollectionUtils.isEmpty(skuList)) {
            return;
        }

        Map<String, ProductInfoVO> map = new HashMap<>(20000);

        EsNocnocItemRequest request = new EsNocnocItemRequest();
        request.setSkuList(skuList);
        request.setAccountNumberList(accountNumberList);
        String[] fields = {"id"};
        request.setQueryFields(fields);
        List<EsNocnocItem> esNocnocItems = nocnocItemEsService.getEsNocnocItems(request);
        if (CollectionUtils.isEmpty(esNocnocItems)) {
            return;
        }

        esNocnocItems.forEach(esNocnocItem -> {
            EsNocnocItem item = nocnocItemEsService.findAllById(esNocnocItem.getId());

            // 查询产品信息
            ProductInfoVO productInfoVO = map.get(item.getSku());
            if (ObjectUtils.isEmpty(productInfoVO)) {
                productInfoVO = ProductUtils.getSkuInfo(item.getSku());
                map.put(item.getSku(), productInfoVO);
            }
            if (ObjectUtils.isEmpty(productInfoVO) || null == productInfoVO.getSonSku()) {
                return;
            }

            NocnocItemUtils.setProductInfo(item, productInfoVO);
            nocnocItemEsService.save(item);
        });
    }
}

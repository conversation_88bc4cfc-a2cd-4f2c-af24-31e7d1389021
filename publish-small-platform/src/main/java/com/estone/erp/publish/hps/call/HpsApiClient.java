package com.estone.erp.publish.hps.call;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.estone.erp.common.warpper.EnvironmentSupplierWrapper;
import com.estone.erp.common.util.AbstractHttpClient;
import com.estone.erp.publish.hps.call.api.HpsApiConstant;
import com.estone.erp.publish.hps.call.model.ProductSearchRequest;
import com.estone.erp.publish.hps.call.model.ProductSearchResponse;
import com.estone.erp.publish.hps.call.model.UpsertProductRequest;
import com.estone.erp.publish.hps.call.model.VariantsUpdateRequest;
import com.estone.erp.publish.hps.call.model.dto.HpsProductDO;
import com.estone.erp.publish.hps.call.model.dto.HpsTransactionDO;
import com.estone.erp.publish.hps.common.HpsAccountCacheManager;
import com.estone.erp.publish.system.account.SaleAccountAndBusinessResponse;
import com.google.common.base.Stopwatch;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.compress.utils.IOUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.http.Header;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.message.BasicHeader;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.net.URI;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2023-04-21 17:37
 */
@Slf4j
@Component
public class HpsApiClient extends AbstractHttpClient {

    @Autowired
    private HpsAccountCacheManager hpsAccountCacheManager;

    private List<Header> createDefaultHeader( String token) {
        if (StringUtils.isBlank(token)) {
            throw new IllegalArgumentException("token cant be empty");
        }
        List<Header> headers = new ArrayList<>();
        headers.add(new BasicHeader("Authorization", "Bearer " + token));
        return headers;
    }

    /**
     * 搜索商品列表
     * @param request  request
     * @return 商品列表 {@see ProductSearchResponse}
     */
    public HpsResponseResult<ProductSearchResponse> searchProductList(String accountNumber, ProductSearchRequest request) {
        String param = JSON.toJSONString(request);
        String url = HpsApiConstant.BASE_HOST + HpsApiConstant.SEARCH_PRODUCT;
        return doPost(accountNumber,param, url, new TypeReference<HpsResponseResult<ProductSearchResponse>>(){});
    }

    /**
     * 获取商品详情
     * @param accountNumber 店铺
     * @param spu           spu
     */
    public HpsResponseResult<HpsProductDO> getProductInfo(String accountNumber, String spu) {
        String url = HpsApiConstant.BASE_HOST + HpsApiConstant.PRODUCT_INFO+spu;
        SaleAccountAndBusinessResponse account = hpsAccountCacheManager.getAccount(accountNumber);
        List<Header> defaultHeader = createDefaultHeader(account.getAccessToken());
        TypeReference<HpsResponseResult<HpsProductDO>> typeReference = new TypeReference<HpsResponseResult<HpsProductDO>>() {
        };
        HpsResponseResult<HpsProductDO> responseResult = get(new HashMap<>(), url, typeReference, defaultHeader);
        responseResult.setCode(200);
        return responseResult;
    }

    /**
     * 修改库存价格
     * @param accountNumber    店铺
     * @param updateRequest    数据
     */
    public HpsResponseResult<HpsTransactionDO> updateStockAndPrice(String accountNumber, VariantsUpdateRequest updateRequest) {
        String url = HpsApiConstant.BASE_HOST + HpsApiConstant.STOCK_AND_PRICE;
        String param = JSON.toJSONString(updateRequest);
        return doPost(accountNumber, param, url, new TypeReference<HpsResponseResult<HpsTransactionDO>>(){});
    }

    /**
     * 创建或修改商品
     * @param accountNumber 店铺
     * @param request       数据
     */
    public HpsResponseResult<HpsTransactionDO> updateProduct(String accountNumber, UpsertProductRequest request) {
        String url = HpsApiConstant.BASE_HOST + HpsApiConstant.PRODUCT_UPSERT;
        String param = JSON.toJSONString(request);
        return doPost(accountNumber, param, url, new TypeReference<HpsResponseResult<HpsTransactionDO>>(){});
    }

    /**
     * 下架商品
     * @param accountNumber 店铺
     * @param sellerSku     变体sku
     */
    public HpsResponseResult<HpsTransactionDO> disabledVariants(String accountNumber, String sellerSku) {
        return EnvironmentSupplierWrapper.execute(()->{
            String url = HpsApiConstant.BASE_HOST + HpsApiConstant.DISABLED_VARIANTS + sellerSku;
            SaleAccountAndBusinessResponse account = hpsAccountCacheManager.getAccount(accountNumber);
            List<Header> defaultHeader = createDefaultHeader(account.getAccessToken());
            TypeReference<HpsResponseResult<HpsTransactionDO>> typeReference = new TypeReference<HpsResponseResult<HpsTransactionDO>>() {
            };
            HpsResponseResult<HpsTransactionDO> responseResult = get(new HashMap<>(), url, typeReference, defaultHeader);
            responseResult.setCode(200);
            return responseResult;
        },()->{
            log.info("非正式环境不执行下架，{}",sellerSku);
            return HpsResponseResult.failResult("非正式环境不执行下架");
        });
    }

    /**
     * 获取处理结果
     */
    public HpsResponseResult<HpsTransactionDO> getTransaction(String accountNumber, String id) {
        String url = HpsApiConstant.BASE_HOST + HpsApiConstant.GET_TRANSACTIONS + id;
        SaleAccountAndBusinessResponse account = hpsAccountCacheManager.getAccount(accountNumber);
        List<Header> defaultHeader = createDefaultHeader(account.getAccessToken());
        TypeReference<HpsResponseResult<HpsTransactionDO>> typeReference = new TypeReference<HpsResponseResult<HpsTransactionDO>>() {
        };
        HpsResponseResult<HpsTransactionDO> responseResult = get(new HashMap<>(), url, typeReference, defaultHeader);
        responseResult.setCode(200);
        return responseResult;
    }

    private <T> HpsResponseResult<T> doPost(String accountNumber, String json, String url, TypeReference<HpsResponseResult<T>> reference) {
        SaleAccountAndBusinessResponse account = hpsAccountCacheManager.getAccount(accountNumber);
        List<Header> defaultHeader = createDefaultHeader(account.getAccessToken());
        Stopwatch watch = Stopwatch.createStarted();
        Exception ex = null;
        HttpPost httpPost = new HttpPost();
        try {

            StringEntity entity = new StringEntity(json, StandardCharsets.UTF_8);
            entity.setContentEncoding("UTF-8");
            entity.setContentType("application/json");
            if(CollectionUtils.isNotEmpty(defaultHeader)) {
                httpPost.setHeaders(defaultHeader.toArray(new Header[] {}));
            }
            httpPost.setEntity(entity);
            httpPost.setURI(new URI(url));
            return getResultString(getHttpClient(6000).execute(httpPost), reference);
        } catch (Exception e) {
            ex = new IllegalStateException("请求失败", e);
            return HpsResponseResult.failResult(e.getMessage());
        } finally {
            httpPost.releaseConnection();
            String template = "[http-client][POST][{}][requestData][{}][result][][error][{}][costTime][{} ms]";
            if (ex != null) {
                log.error(template, url, json,  ex.getMessage(), watch.elapsed(TimeUnit.MILLISECONDS), ex);
            } else {
                log.debug(template, url, json,  org.apache.commons.lang3.StringUtils.EMPTY, watch.elapsed(TimeUnit.MILLISECONDS));
            }
        }
    }

    private <T> HpsResponseResult<T> getResultString(CloseableHttpResponse httpResponse, TypeReference<HpsResponseResult<T>> reference) {
        int statusCode = httpResponse.getStatusLine().getStatusCode();
        try {

            try {
                String result = EntityUtils.toString(httpResponse.getEntity());
                if (statusCode != 200) {
                    HpsResponseResult<T> responseResult = JSON.parseObject(result, reference);
                    responseResult.setCode(Optional.ofNullable(responseResult.getCode()).orElseGet(() -> statusCode));
                    return responseResult;
                }
                HpsResponseResult<T> responseResult = JSON.parseObject(result, reference);
                responseResult.setCode(200);
                return responseResult;
            } catch (IOException e) {
                throw new IllegalStateException("转换失败");
            }
        }finally {
            IOUtils.closeQuietly(httpResponse);
        }
    }


}

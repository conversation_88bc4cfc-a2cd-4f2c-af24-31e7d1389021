package com.estone.erp.publish.nocnoc.call;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.estone.erp.publish.common.AbstractHttpCall;
import com.estone.erp.publish.nocnoc.constant.NocnocCallConstant;
import com.estone.erp.publish.system.account.SaleAccountAndBusinessResponse;
import jodd.util.ThreadUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.http.client.methods.*;
import org.springframework.http.HttpStatus;

import java.util.Map;

/**
 * nocnoc请求基础类
 * <AUTHOR>
 * @date 2022/11/2 15:45
 */
@Slf4j
public class AbstractNocnocCall extends AbstractHttpCall {

    protected SaleAccountAndBusinessResponse nocnocAccount;

    public AbstractNocnocCall(SaleAccountAndBusinessResponse nocnocAccount) {
        this.nocnocAccount = nocnocAccount;
    }

    @Override
    protected HttpGet createGetRequest(String url, Map<String, Object> data, Object... args) {
        // /开头代表部分路径 需补上基础路径
        if (StringUtils.startsWith(url, NocnocCallConstant.PATH_SPLIT)) {
            url = NocnocCallConstant.ROOT_URL + url;
        }

        HttpGet request = super.createGetRequest(url, data, args);
        setRequestHeader(request);

        return request;
    }

    @Override
    protected HttpPost createPostRequest(String url, JSONObject data, Object... args) {
        if (StringUtils.startsWith(url, NocnocCallConstant.PATH_SPLIT)) {
            url = NocnocCallConstant.ROOT_URL + url;
        }

        HttpPost request = super.createPostRequest(url, data, args);
        setRequestHeader(request);

        return request;
    }

    @Override
    protected HttpPut createPutRequest(String url, Map<String, Object> data, Object... args) {
        if (StringUtils.startsWith(url, NocnocCallConstant.PATH_SPLIT)) {
            url = NocnocCallConstant.ROOT_URL + url;
        }

        HttpPut request = super.createPutRequest(url, data, args);
        setRequestHeader(request);

        return request;
    }

    protected HttpDelete createDeleteRequest(String url) {
        if (StringUtils.startsWith(url, NocnocCallConstant.PATH_SPLIT)) {
            url = NocnocCallConstant.ROOT_URL + url;
        }

        HttpDelete request = new HttpDelete(url);
        setRequestHeader(request);

        return request;
    }

    /**
     * 设置请求头
     * @param request
     */
    private void setRequestHeader(HttpUriRequest request) {
        request.setHeader("X-Api-Key", nocnocAccount.getAccessToken());
        request.setHeader("Content-Type", "application/json");
    }

    @Override
    protected String execute(HttpUriRequest request) {
        String body = null;
        int tryNum = 3;
        do {
            try {
                // 执行请求
                body = super.execute(request);

                // 检查错误信息
                checkErrorMessage(body);
                break;
            } catch (Exception e) {
                String message = e.getMessage();
                if (StringUtils.isBlank(message)) {
                    log.error("异常信息为空！Body" + body);
                    throw new RuntimeException("异常信息为空！Body" + body);
                } else if (message.contains(String.valueOf(HttpStatus.GATEWAY_TIMEOUT.value()))) {
                    // 异常信息不为空 且为超时异常 睡眠5s重试最多3次
                    log.error("tryNum " + tryNum + body);
                    ThreadUtil.sleep(5000);
                } else {
                    throw new RuntimeException(message);
                }
            }
        } while (--tryNum > 0);

        return body;
    }

    private void checkErrorMessage(String body) {
        JSONObject jsonObject = JSON.parseObject(body);
        if (jsonObject == null) {
            return;
        }

        Integer code = jsonObject.getInteger("code");
        String message = jsonObject.getString("message");
        String key = jsonObject.getString("key");
        if (null != code && StringUtils.isNotBlank(message) && StringUtils.isNotBlank(key)) {
            log.error(String.format("[%s]Code:%s;Message:%s;Key:%s", nocnocAccount.getAccountNumber(), code, message, key));
            throw new RuntimeException(String.format("[%s]Code:%s;Message:%s;Key:%s", nocnocAccount.getAccountNumber(), code, message, key));
        }
    }
}

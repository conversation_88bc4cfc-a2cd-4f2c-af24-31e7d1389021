package com.estone.erp.publish.temu.call.model.response;

import lombok.Data;

import java.util.List;

/**
 * @program: publish
 * @description: 描述
 * @author: dinghong
 * @create: 2024/6/11
 **/
@Data
public class ModelResponse {
    /**
     * 总数
     */
    private Integer total;

    /**
     * 模特列表
     */
    private List<ModelItem> modelList;

    @Data
    public static class ModelItem {

        /**
         * 名称
         */
        private String modelName;

        /**
         * 是否可编辑，仅为 false 才不可编辑与删除
         */
        private Boolean canEdit;

        /**
         * 成衣模特信息
         */
        private ClothesModel clothesModel;

        /**
         * 鞋模信息
         */
        private ShoeModel shoeModel;

        /**
         * ID
         */
        private Long id;

        /**
         * 模特类型. 可选值含义说明，0: 成衣模特，1: 鞋模
         */
        private Integer modelType;

        /**
         * 头像
         */
        private String headPortrait;

        @Data
        public static class ClothesModel {

            /**
             * 臀围
             */
            private String hipline;

            /**
             * 胸围
             */
            private String bust;

            /**
             * 腰围
             */
            private String waist;

            /**
             * 身高
             */
            private String height;
        }

        @Data
        public static class ShoeModel {

            /**
             * 脚长
             */
            private String footLength;

            /**
             * 脚宽
             */
            private String footWidth;
        }
    }
}

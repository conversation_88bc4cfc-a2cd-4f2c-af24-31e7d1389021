package com.estone.erp.publish.temu.service;

import com.estone.erp.common.model.api.CQuery;
import com.estone.erp.common.model.api.CQueryResult;
import com.estone.erp.publish.temu.model.TemuSkuPublishStatistics;
import com.estone.erp.publish.temu.model.TemuSkuPublishStatisticsCriteria;
import com.estone.erp.publish.temu.model.TemuSkuPublishStatisticsExample;
import com.estone.erp.publish.temu.model.vo.OrderStatisticVO;
import com.estone.erp.publish.temu.model.vo.StatisticResponseVO;
import com.estone.erp.publish.temu.model.vo.TemuSkuPublishStatisticsVo;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> temu_sku_publish_statistics
 * 2024-03-11 15:22:36
 */
public interface TemuSkuPublishStatisticsService {
    int countByExample(TemuSkuPublishStatisticsExample example);

    CQueryResult<TemuSkuPublishStatisticsVo> getNewAddIncrementGroup(CQuery<TemuSkuPublishStatisticsCriteria> cquery, boolean groupByStatisticalTime, boolean groupByAccountNumber, boolean groupBysaleAccount);

    CQueryResult<TemuSkuPublishStatisticsVo> getTotalGroup(CQuery<TemuSkuPublishStatisticsCriteria> cquery, boolean groupByStatisticalTime, boolean groupByAccountNumber, boolean groupBysaleAccount);

    List<TemuSkuPublishStatistics> selectByExample(TemuSkuPublishStatisticsExample example);

    TemuSkuPublishStatistics selectByPrimaryKey(Integer id);

    int insert(TemuSkuPublishStatistics record);

    int updateByPrimaryKeySelective(TemuSkuPublishStatistics record);

    int updateByExampleSelective(TemuSkuPublishStatistics record, TemuSkuPublishStatisticsExample example);

    int deleteByPrimaryKey(List<Integer> ids);

    void deleteByDayAndAccount(Integer dataType, Byte type, Date day, List<String> accountNumbers);

    void batchInsert(List<TemuSkuPublishStatistics> list);

    List<TemuSkuPublishStatisticsVo> list(CQuery<TemuSkuPublishStatisticsCriteria> cquery);

    void batchUpdateSaleId(List<TemuSkuPublishStatistics> lists);

    List<StatisticResponseVO> orderStatistics(OrderStatisticVO vo);
}
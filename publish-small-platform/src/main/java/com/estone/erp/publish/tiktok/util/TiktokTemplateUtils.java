package com.estone.erp.publish.tiktok.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.estone.erp.common.constant.CurrencyConstant;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.util.SpringUtils;
import com.estone.erp.common.util.StrUtil;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.feginService.modle.BatchPriceCalculatorRequest;
import com.estone.erp.publish.feginService.modle.BatchPriceCalculatorResponse;
import com.estone.erp.publish.system.account.PriceCalculatedUtil;
import com.estone.erp.publish.system.param.model.SystemParam;
import com.estone.erp.publish.system.param.service.SystemParamService;
import com.estone.erp.publish.system.product.ProductInfo;
import com.estone.erp.publish.system.product.bean.SpuOfficial;
import com.estone.erp.publish.tiktok.enums.TiktokTrialRuleEnum;
import com.estone.erp.publish.tiktok.model.TiktokAccountConfig;
import com.estone.erp.publish.tiktok.model.dto.SkuCalcPriceDTO;
import com.estone.erp.publish.tiktok.model.dto.TitleDescriptionDTO;
import com.estone.erp.publish.tiktok.service.TiktokAccountConfigService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.BooleanUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/12/12 15:12
 */
@Slf4j
public class TiktokTemplateUtils {

    private static SystemParamService systemParamService = SpringUtils.getBean(SystemParamService.class);
    private static TiktokAccountConfigService tiktokAccountConfigService = SpringUtils.getBean(TiktokAccountConfigService.class);

    /**
     * 获取标题描述
     */
    public static TitleDescriptionDTO matchingTitleDescription(SpuOfficial spuOfficial) {
        TitleDescriptionDTO titleDescription = new TitleDescriptionDTO();
        if (null == spuOfficial) {
            return titleDescription;
        }

        // 标题 优先级：长标题>短标题>sku标题
        String title = null;
        if (StringUtils.isNotBlank(spuOfficial.getLongTitleJson())) {
            List<String> titleList = JSON.parseObject(spuOfficial.getLongTitleJson(), new TypeReference<>() {
            });
            titleList = titleList.stream().filter(StringUtils::isNotBlank).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(titleList)) {
                Collections.shuffle(titleList);
                title = titleList.get(0);
            }
        }
        if (StringUtils.isBlank(title) && StringUtils.isNotBlank(spuOfficial.getShortTitleJson())) {
            List<String> titleList = JSON.parseObject(spuOfficial.getShortTitleJson(), new TypeReference<>() {
            });
            titleList = titleList.stream().filter(StringUtils::isNotBlank).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(titleList)) {
                Collections.shuffle(titleList);
                title = titleList.get(0);
            }
        }
        if (StringUtils.isBlank(title) && StringUtils.isNotBlank(spuOfficial.getTitle())) {
            List<String> titleList = JSON.parseObject(spuOfficial.getTitle(), new TypeReference<>() {
            });
            titleList = titleList.stream().filter(StringUtils::isNotBlank).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(titleList)) {
                Collections.shuffle(titleList);
                title = titleList.get(0);
            }
        }
        titleDescription.setTitle(title);

        // 描述 优先级：SKU描述新>SKU描述
        String description = spuOfficial.getNewDescription();
        if (StringUtils.isBlank(description)) {
            try {
                String desc = StrUtil.objectToStr(spuOfficial.getDescription());
                List<String> list = JSON.parseObject(desc, new TypeReference<>() {
                });
                if (CollectionUtils.isNotEmpty(list)) {
                    for (String s : list) {
                        if (StringUtils.isNotBlank(s)) {
                            description = s;
                            break;
                        }
                    }
                }
            } catch (Exception e) {
                log.error("解析描述报错：" + e.getMessage());
            }
        }
        if (StringUtils.isNotBlank(description)) {
            if (StringUtils.isNotBlank(title)) {
                titleDescription.setDescription(title + "\n\n" + description);
            } else {
                titleDescription.setDescription(description);
            }
        }

        return titleDescription;
    }

    /**
     * 获取sku销售属性
     */
    public static Map<String, String> getSaleAttributes(ProductInfo productInfo) {
        if (null == productInfo) {
            return null;
        }
        String attributes = productInfo.getSaleAtts();
        if (StringUtils.isBlank(attributes)) {
            return null;
        }
        Map<String, String> attrMap = new HashMap<>();
        JSONArray attributeArray = JSON.parseArray(attributes);
        for (int i = 0; i < attributeArray.size(); i++) {
            // 属性中包含指定的属性
            JSONObject attributeJson = attributeArray.getJSONObject(i);
            String enName = attributeJson.getString("enName");
            String enValue = attributeJson.getString("enValue");
            if (StringUtils.isNotBlank(enName) && StringUtils.isNotBlank(enValue)) {
                attrMap.put(enName, enValue);
            }
        }
        return attrMap;
    }

    public static Map<String, Double> calcPrice(List<SkuCalcPriceDTO> skuCalcPriceDTOList, List<String> accountNumberList) {
        Map<String, Double> skuPriceMap = new HashMap<>();

        // 获取配置毛利率
        TiktokAccountConfig accountConfig = tiktokAccountConfigService.getConfigByPriority(accountNumberList);
        Double accountGrossProfitRate = accountConfig == null ? null : accountConfig.getGrossProfitRate();
        if (null == accountGrossProfitRate) {
            accountGrossProfitRate = 0.3;
        }

        String site = StringUtils.substringAfterLast(accountNumberList.get(0), "-");
        if (TiktokTrialRuleEnum.US.getCode().equals(site)) {
            List<BatchPriceCalculatorRequest> params = buildUSCalcRequests(skuCalcPriceDTOList, site, accountGrossProfitRate);

            // 请求算价
            ApiResult<List<BatchPriceCalculatorResponse>> apiResult = PriceCalculatedUtil.batchPriceCalculator(params, 1);
            if (!apiResult.isSuccess()) {
                throw new RuntimeException(String.format("算价请求失败: message:%s", apiResult.getErrorMsg()));
            }

            for (BatchPriceCalculatorResponse response : apiResult.getResult()) {
                if (BooleanUtils.isFalse(response.getIsSuccess()) || StringUtils.isNotBlank(response.getErrorMsg())) {
                    throw new RuntimeException("算价失败：" + response.getErrorMsg());
                }
                skuPriceMap.put(response.getId(),
                        BigDecimal.valueOf(response.getForeignPrice()).setScale(2, RoundingMode.UP).doubleValue());
            }
        } else {
            // 获取平台费率
            SystemParam system = systemParamService.querySystemParamByCodeKey("TIKTOK.plat_rate");
            if (null == system) {
                throw new NoSuchElementException("平台费率为空");
            }
            double platRate = Double.parseDouble(system.getParamValue());

            // 获取汇率
            ApiResult<Double> usdRateResult = PriceCalculatedUtil.getExchangeRate(CurrencyConstant.CNY, CurrencyConstant.USD);
            if (!usdRateResult.isSuccess()) {
                throw new RuntimeException(String.format("%s获取汇率失败：%s", CurrencyConstant.CNY, usdRateResult.getErrorMsg()));
            }
            Double usdRate = usdRateResult.getResult();

            for (SkuCalcPriceDTO skuCalcPriceDTO : skuCalcPriceDTOList) {
                if (skuCalcPriceDTO.getSaleCost() == null || skuCalcPriceDTO.getWeight() == null) {
                    continue;
                }

                // 毛利率
                Double grossProfitRate = skuCalcPriceDTO.getGrossProfitRate() == null ? accountGrossProfitRate : skuCalcPriceDTO.getGrossProfitRate();

                // 成本价
                double saleCost = skuCalcPriceDTO.getSaleCost().doubleValue();
                Double costPrice = saleCost + saleCost * 0.08;

                // 运费
                Double freight = getFreightByWeight(skuCalcPriceDTO.getWeight());

                Double price = BigDecimal.valueOf((((costPrice + freight) / (1 - platRate - grossProfitRate)) - freight) * usdRate)
                        .setScale(2, RoundingMode.UP).doubleValue();
                skuPriceMap.put(skuCalcPriceDTO.getSku(), price);
            }
        }

        return skuPriceMap;
    }

    private static List<BatchPriceCalculatorRequest> buildUSCalcRequests(List<SkuCalcPriceDTO> skuCalcPriceDTOList, String site, Double accountGrossProfitRate) {
        String shippingMethod = TiktokTrialRuleEnum.getLogisticsBySite(site);

        List<BatchPriceCalculatorRequest> params = new ArrayList<>(skuCalcPriceDTOList.size());
        for (SkuCalcPriceDTO bean : skuCalcPriceDTOList) {
            BatchPriceCalculatorRequest obj = new BatchPriceCalculatorRequest();
            obj.setId(bean.getSku());
            obj.setSaleChannel(SaleChannel.CHANNEL_TIKTOK);
            obj.setArticleNumber(bean.getSku());
            obj.setShippingMethod(shippingMethod);
            obj.setTotalPurchaseCost(Optional.ofNullable(bean.getSaleCost()).map(BigDecimal::doubleValue).orElse(null));
            obj.setGrossProfitRate(bean.getGrossProfitRate() == null ? accountGrossProfitRate : bean.getGrossProfitRate());
            obj.setTotalWeight(bean.getWeight());
            obj.setSite(site);
            obj.setCountryCode(site);
            obj.setQuantity(1);
            //经产品确认,当为冠通-大健云仓产品时,不需要物流、销售成本、重量去调用试算器,因此清空
            if (bean.isDaJianCloudWareHouse()) {
                obj.setShippingMethod(null);
                obj.setTotalPurchaseCost(null);
                obj.setTotalWeight(null);
                //需设置运费为0
                obj.setPostFee(0d);
            }
            params.add(obj);
        }

        return params;
    }

    private static Double getFreightByWeight(Double weight) {
        double freight;

        // 重量十位向上取整
        weight = Math.ceil(weight / 10) * 10;

        BigDecimal bigDecimal = BigDecimal.valueOf(weight);
        BigDecimal bigDecimal100 = new BigDecimal(100);
        BigDecimal bigDecimal500 = new BigDecimal(500);
        if (bigDecimal.compareTo(BigDecimal.ZERO) > 0 && bigDecimal.compareTo(bigDecimal100) <= 0) {
            freight = weight / 1000 * 25 + 3;
        } else if (bigDecimal.compareTo(bigDecimal100) > 0 && bigDecimal.compareTo(bigDecimal500) <= 0) {
            freight = weight / 1000 * 25 + 5;
        } else {
            freight =  weight / 1000 * 25 + 8;
        }

        return freight;
    }
}

package com.estone.erp.publish.shopify.controller;

import com.alibaba.fastjson.TypeReference;
import com.estone.erp.common.model.api.ApiRequestParam;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.model.api.CQuery;
import com.estone.erp.common.model.api.CQueryResult;
import com.estone.erp.common.util.Asserts;
import com.estone.erp.publish.common.ErrorCode;
import com.estone.erp.publish.shopify.model.ShopifyAccount;
import com.estone.erp.publish.shopify.model.ShopifyAccountCriteria;
import com.estone.erp.publish.shopify.service.ShopifyAccountService;
import javax.annotation.Resource;
import org.apache.commons.lang.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR> shopify_account
 * 2021-05-28 09:30:07
 */
@RestController
@RequestMapping("shopifyAccount")
public class ShopifyAccountController {
    @Resource
    private ShopifyAccountService shopifyAccountService;

    @PostMapping
    public ApiResult<?> postShopifyAccount(@RequestBody(required = true) ApiRequestParam<String> requestParam) {
        String method = requestParam.getMethod();
        if (StringUtils.isNotBlank(method)) {
            switch (method) {
                case "searchShopifyAccount": // 查询列表
                    CQuery<ShopifyAccountCriteria> cquery = requestParam.getArgsValue(new TypeReference<CQuery<ShopifyAccountCriteria>>() {});
                    Asserts.isTrue(cquery != null, ErrorCode.PARAM_EMPTY_ERROR);
                    CQueryResult<ShopifyAccount> results = shopifyAccountService.search(cquery);
                    return results;
                case "addShopifyAccount": // 添加
                    ShopifyAccount shopifyAccount = requestParam.getArgsValue(new TypeReference<ShopifyAccount>() {});
                    shopifyAccountService.insert(shopifyAccount);
                    return ApiResult.newSuccess(shopifyAccount);
                }
        }
        return ApiResult.newSuccess();
    }

    @GetMapping(value = "/{id}")
    public ApiResult<?> getShopifyAccount(@PathVariable(value = "id", required = true) Integer id) {
        ShopifyAccount shopifyAccount = shopifyAccountService.selectByPrimaryKey(id);
        return ApiResult.newSuccess(shopifyAccount);
    }

    @PutMapping(value = "/{id}")
    public ApiResult<?> putShopifyAccount(@PathVariable(value = "id", required = true) Integer id, @RequestBody ApiRequestParam<String> requestParam) {
        String method = requestParam.getMethod();
        if (StringUtils.isNotBlank(method)) {
            switch (method) {
                case "updateShopifyAccount": // 单个修改
                    ShopifyAccount shopifyAccount = requestParam.getArgsValue(new TypeReference<ShopifyAccount>() {});
                    shopifyAccountService.updateByPrimaryKeySelective(shopifyAccount);
                    return ApiResult.newSuccess(shopifyAccount);
                }
        }
        return ApiResult.newSuccess();
    }
}
package com.estone.erp.publish.tiktok.model.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 设置产品信息
 */
@Data
public class TiktokSyncPageItemAndProductDto extends TiktokSyncPageItemDTO {

    /**
     * SPU
     */
    private String spu;

    /**
     * SKU
     */
    private String sku;

    /**
     * 单品状态（产品信息字段）
     */
    private String skuStatus;

    /**
     * 类目id
     */
    private String proCategoryId;

    /**
     * 类目中文名
     */
    private String proCategoryCnName;

    /**
     * 禁售平台（逗号拼接）
     */
    private String forbidChannel;

    /**
     * 禁售站点
     */
    private List<String> prohibitionSites;

    /**
     * 禁售类型
     */
    private String infringementTypeName;

    /**
     * 禁售原因
     */
    private String infringementObj;

    /**
     * 产品标签code
     */
    private String tagCodes;

    /**
     * 产品标签中文名
     */
    private String tagNames;

    /**
     * 特殊标签code
     */
    private String specialGoodsCode;

    /**
     * 特殊标签中文名
     */
    private String specialGoodsName;

    /**
     * 促销状态 0 没有  1，是  2，否  特别注意 0 和 2 都是(否)，1是（是）
     */
    private Integer promotion;

    /**
     * 新品状态
     */
    private Boolean newState;

    /**
     * 数据来源
     */
    private Integer skuDataSource;

    /**
     * 组合状态 8003 启用 8004 禁用
     */
    private Integer composeStatus;

    /**
     * product 同步时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern ="yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date syncProdDate;

}

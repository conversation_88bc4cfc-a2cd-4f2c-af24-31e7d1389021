package com.estone.erp.publish.shopify.mapper;

import com.estone.erp.publish.shopify.model.ShopifyItem;
import com.estone.erp.publish.shopify.model.ShopifyItemExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface ShopifyItemMapper {
    int countByExample(ShopifyItemExample example);

    int deleteByPrimaryKey(List<Integer> ids);

    int insert(ShopifyItem record);

    ShopifyItem selectByPrimaryKey(Integer id);

    List<ShopifyItem> selectByExample(ShopifyItemExample example);

    int updateByExampleSelective(@Param("record") ShopifyItem record, @Param("example") ShopifyItemExample example);

    int updateByPrimaryKeySelective(ShopifyItem record);
}
package com.estone.erp.publish.temu.handler;

import com.estone.erp.common.util.DateUtils;
import com.estone.erp.common.util.PagingUtils;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.elasticsearch.service.EsTemuShopItemInfoService;
import com.estone.erp.publish.elasticsearch.util.EsAccountUtils;
import com.estone.erp.publish.elasticsearch4.model.beanrequest.EsTemuListingRequest;
import com.estone.erp.publish.elasticsearch4.service.EsTemuListingService;
import com.estone.erp.publish.system.account.modle.SalesmanAccountDetail;
import com.estone.erp.publish.temu.enums.TemuListingEnums;
import com.estone.erp.publish.temu.enums.TemuStatisticalDataTypeEnums;
import com.estone.erp.publish.temu.enums.TemuStatisticalTypeEnum;
import com.estone.erp.publish.temu.model.TemuSkuPublishStatistics;
import com.estone.erp.publish.temu.model.TemuSkuPublishStatisticsExample;
import com.estone.erp.publish.temu.service.TemuSkuPublishStatisticsService;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.sql.Timestamp;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

@Component
public class TemuSkuPublishStatisticsHandler {

    @Autowired
    private TemuSkuPublishStatisticsService temuSkuPublishStatisticsService;

    @Autowired
    private EsTemuListingService esTemuListingService;

    @Autowired
    private EsTemuShopItemInfoService esTemuShopItemInfoService;

    public void statisticListing(List<String> accountList, Map<String, SalesmanAccountDetail> salesmanAccountDetailMap, TemuStatisticalTypeEnum type, Date statisticalTime) {
        String start = "";
        String end = "";
        String format = DateUtils.format(statisticalTime, "yyyy-MM-dd");
        if (type == TemuStatisticalTypeEnum.NEW_INCREMENT) {
            start = format + " 00:00:00";
            end = format + " 23:59:59";
        } else {
            end = format + " 23:59:59";
        }

        // 统计数据
        for (TemuStatisticalDataTypeEnums dataType : TemuStatisticalDataTypeEnums.values()) {
            statisticListingByDataType(accountList, salesmanAccountDetailMap, dataType, type, statisticalTime, start, end);
        }
    }

    private void statiscListingSpu(List<String> accountList, Map<String, SalesmanAccountDetail> salesmanAccountDetailMap, TemuStatisticalTypeEnum type, Date statisticalTime, String start, String end) {
        List<TemuSkuPublishStatistics> spuTemuSkuPublishStatisticsList = new ArrayList<>(accountList.size());
        for (String accountNumber : accountList) {
            EsTemuListingRequest request = new EsTemuListingRequest();
            request.setAccountNumber(accountNumber);
            request.setFromCreateDate(start);
            request.setToCreateDate(end);

            Map<String, Long> statusOverviewMap = new HashMap<>();
            for (TemuListingEnums.StatusType value : TemuListingEnums.StatusType.values()) {
                request.setStatusType(value.getCode());
                Long listingCount = esTemuListingService.searchSpuCount(request);
                statusOverviewMap.put(value.getCode().toString(), listingCount);
            }

            int sum = statusOverviewMap.values().stream().mapToInt(Long::intValue).sum();

            TemuSkuPublishStatistics temuSkuPublishStatistics = new TemuSkuPublishStatistics();
            temuSkuPublishStatistics.setDataType(2);
            temuSkuPublishStatistics.setAccountNumber(accountNumber);
            temuSkuPublishStatistics.setStatisticalType(type.getCode());
            temuSkuPublishStatistics.setStatisticalTime(statisticalTime);
            temuSkuPublishStatistics.setUnPublishCount(statusOverviewMap.getOrDefault(TemuListingEnums.StatusType.UN_PUBLISH.getCode().toString(), 0L).intValue());
            temuSkuPublishStatistics.setWaitSampleCount(statusOverviewMap.getOrDefault(TemuListingEnums.StatusType.WAIT_SAMPLE.getCode().toString(), 0L).intValue());
            temuSkuPublishStatistics.setInReviewCount(statusOverviewMap.getOrDefault(TemuListingEnums.StatusType.IN_REVIEW.getCode().toString(), 0L).intValue());
            temuSkuPublishStatistics.setInPriceDeclarationCount(statusOverviewMap.getOrDefault(TemuListingEnums.StatusType.IN_PRICE_DECLARATION.getCode().toString(), 0L).intValue());
            temuSkuPublishStatistics.setWaitFirstOrderCount(statusOverviewMap.getOrDefault(TemuListingEnums.StatusType.WAIT_FIRST_ORDER.getCode().toString(), 0L).intValue());
            temuSkuPublishStatistics.setOnFirstOrderCount(statusOverviewMap.getOrDefault(TemuListingEnums.StatusType.ON_FIRST_ORDER.getCode().toString(), 0L).intValue());
            temuSkuPublishStatistics.setPublishedSiteOrderCount(statusOverviewMap.getOrDefault(TemuListingEnums.StatusType.PUBLISHED_SITE.getCode().toString(), 0L).intValue());
            temuSkuPublishStatistics.setOfflineEndCount(statusOverviewMap.getOrDefault(TemuListingEnums.StatusType.OFFLINE_END.getCode().toString(), 0L).intValue());
            temuSkuPublishStatistics.setListingCount(sum);

            temuSkuPublishStatistics.setCreateTime(new Timestamp(new Date().getTime()));
            SalesmanAccountDetail salesmanAccountDetail = salesmanAccountDetailMap.get(accountNumber);
            if (salesmanAccountDetail != null) {
                temuSkuPublishStatistics.setSaleId(EsAccountUtils.getSaleId(salesmanAccountDetail));
            }
            spuTemuSkuPublishStatisticsList.add(temuSkuPublishStatistics);
        }

        if (type == TemuStatisticalTypeEnum.TOTAL) {
            for (TemuSkuPublishStatistics temuSkuPublishStatistics : spuTemuSkuPublishStatisticsList) {
                String accountNumber = temuSkuPublishStatistics.getAccountNumber();
                Integer size = esTemuShopItemInfoService.getAccountItem(accountNumber);
                temuSkuPublishStatistics.setItemCount(size);
            }
        }

        List<List<TemuSkuPublishStatistics>> spuLists = PagingUtils.newPagingList(spuTemuSkuPublishStatisticsList, 100);
        for (List<TemuSkuPublishStatistics> list : spuLists) {
            List<String> accountNumbers = list.stream().map(TemuSkuPublishStatistics::getAccountNumber).collect(Collectors.toList());
            if (accountNumbers.isEmpty()) {
                continue;
            }
            temuSkuPublishStatisticsService.deleteByDayAndAccount(2, type.getCode(), statisticalTime, accountNumbers);
            temuSkuPublishStatisticsService.batchInsert(list);
        }
    }

    private void statiscListingSku(List<String> accountList, Map<String, SalesmanAccountDetail> salesmanAccountDetailMap, TemuStatisticalTypeEnum type, Date statisticalTime, String start, String end) {
        List<TemuSkuPublishStatistics> temuSkuPublishStatisticsList = new ArrayList<>(accountList.size());
        for (String accountNumber : accountList) {
            EsTemuListingRequest request = new EsTemuListingRequest();
            request.setAccountNumber(accountNumber);
            request.setFromCreateDate(start);
            request.setToCreateDate(end);

            Long listingCount = esTemuListingService.searchCount(request);
            request.setStatusType(TemuListingEnums.StatusType.UN_PUBLISH.getCode());
            Long unPublishCount = esTemuListingService.searchCount(request);
            request.setStatusType(TemuListingEnums.StatusType.WAIT_SAMPLE.getCode());
            Long waitSampleCount = esTemuListingService.searchCount(request);
            request.setStatusType(TemuListingEnums.StatusType.IN_REVIEW.getCode());
            Long inReviewCount = esTemuListingService.searchCount(request);
            request.setStatusType(TemuListingEnums.StatusType.IN_PRICE_DECLARATION.getCode());
            Long inPriceDeclarationCount = esTemuListingService.searchCount(request);
            request.setStatusType(TemuListingEnums.StatusType.WAIT_FIRST_ORDER.getCode());
            Long waitFirstOrderCount = esTemuListingService.searchCount(request);
            request.setStatusType(TemuListingEnums.StatusType.ON_FIRST_ORDER.getCode());
            Long onFirstOrderCount = esTemuListingService.searchCount(request);
            request.setStatusType(TemuListingEnums.StatusType.PUBLISHED_SITE.getCode());
            Long publishedSiteCount = esTemuListingService.searchCount(request);
            request.setStatusType(TemuListingEnums.StatusType.OFFLINE_END.getCode());
            Long offlineEndCount = esTemuListingService.searchCount(request);

            TemuSkuPublishStatistics temuSkuPublishStatistics = new TemuSkuPublishStatistics();
            temuSkuPublishStatistics.setDataType(1);
            temuSkuPublishStatistics.setAccountNumber(accountNumber);
            temuSkuPublishStatistics.setStatisticalType(type.getCode());
            temuSkuPublishStatistics.setStatisticalTime(statisticalTime);
            temuSkuPublishStatistics.setListingCount(listingCount.intValue());
            temuSkuPublishStatistics.setUnPublishCount(unPublishCount.intValue());
            temuSkuPublishStatistics.setWaitSampleCount(waitSampleCount.intValue());
            temuSkuPublishStatistics.setInReviewCount(inReviewCount.intValue());
            temuSkuPublishStatistics.setInPriceDeclarationCount(inPriceDeclarationCount.intValue());
            temuSkuPublishStatistics.setWaitFirstOrderCount(waitFirstOrderCount.intValue());
            temuSkuPublishStatistics.setOnFirstOrderCount(onFirstOrderCount.intValue());
            temuSkuPublishStatistics.setPublishedSiteOrderCount(publishedSiteCount.intValue());
            temuSkuPublishStatistics.setOfflineEndCount(offlineEndCount.intValue());
            temuSkuPublishStatistics.setCreateTime(new Timestamp(new Date().getTime()));
            SalesmanAccountDetail salesmanAccountDetail = salesmanAccountDetailMap.get(accountNumber);
            if (salesmanAccountDetail != null) {
                temuSkuPublishStatistics.setSaleId(EsAccountUtils.getSaleId(salesmanAccountDetail));
            }
            temuSkuPublishStatisticsList.add(temuSkuPublishStatistics);
        }

        if (type == TemuStatisticalTypeEnum.TOTAL) {
            for (TemuSkuPublishStatistics temuSkuPublishStatistics : temuSkuPublishStatisticsList) {
                String accountNumber = temuSkuPublishStatistics.getAccountNumber();
                Integer size = esTemuShopItemInfoService.getAccountItem(accountNumber);
                temuSkuPublishStatistics.setItemCount(size);
            }
        }

        List<List<TemuSkuPublishStatistics>> lists = PagingUtils.newPagingList(temuSkuPublishStatisticsList, 100);
        for (List<TemuSkuPublishStatistics> list : lists) {
            List<String> accountNumbers = list.stream().map(TemuSkuPublishStatistics::getAccountNumber).collect(Collectors.toList());
            if (accountNumbers.isEmpty()) {
                continue;
            }
            temuSkuPublishStatisticsService.deleteByDayAndAccount(1, type.getCode(), statisticalTime, accountNumbers);
            temuSkuPublishStatisticsService.batchInsert(list);
        }
    }

    private void statisticListingByDataType(List<String> accountList, Map<String, SalesmanAccountDetail> salesmanAccountDetailMap, TemuStatisticalDataTypeEnums dataType, TemuStatisticalTypeEnum type, Date statisticalTime, String start, String end) {
        List<CompletableFuture<TemuSkuPublishStatistics>> futures = accountList.stream()
                .map(accountNumber -> CompletableFuture.supplyAsync(() -> {
                    // 基础信息
                    TemuSkuPublishStatistics temuSkuPublishStatistics = new TemuSkuPublishStatistics();
                    temuSkuPublishStatistics.setDataType(dataType.getCode());
                    temuSkuPublishStatistics.setAccountNumber(accountNumber);
                    temuSkuPublishStatistics.setStatisticalType(type.getCode());
                    temuSkuPublishStatistics.setStatisticalTime(statisticalTime);

                    // 上线数量
                    temuSkuPublishStatistics.setListingCount(
                            getCount(createRequest(accountNumber, start, end, null), dataType)
                    );
                    // 未发布
                    temuSkuPublishStatistics.setUnPublishCount(
                            getCount(createUnpublishRequest(accountNumber, start, end), dataType)
                    );
                    // 待寄样数量
                    temuSkuPublishStatistics.setWaitSampleCount(
                            getCount(createSpecificRequest(accountNumber, start, end, TemuListingEnums.StatusType.WAIT_SAMPLE), dataType)
                    );
                    // 审版中
                    temuSkuPublishStatistics.setInReviewCount(
                            getCount(createSpecificRequest(accountNumber, start, end, TemuListingEnums.StatusType.IN_REVIEW), dataType)
                    );
                    // 价格申报中
                    temuSkuPublishStatistics.setInPriceDeclarationCount(
                            getCount(createSpecificRequest(accountNumber, start, end, TemuListingEnums.StatusType.IN_PRICE_DECLARATION), dataType)
                    );
                    // 待创建收单数量
                    temuSkuPublishStatistics.setWaitFirstOrderCount(
                            getCount(createSpecificRequest(accountNumber, start, end, TemuListingEnums.StatusType.WAIT_FIRST_ORDER), dataType)
                    );
                    // 已创建收单数量
                    temuSkuPublishStatistics.setOnFirstOrderCount(
                            getCount(createSpecificRequest(accountNumber, start, end, TemuListingEnums.StatusType.ON_FIRST_ORDER), dataType)
                    );
                    // 已发布到站点数量
                    temuSkuPublishStatistics.setPublishedSiteOrderCount(
                            getCount(createSpecificRequest(accountNumber, start, end, TemuListingEnums.StatusType.PUBLISHED_SITE), dataType)
                    );
                    // 已下架/终止数量
                    temuSkuPublishStatistics.setOfflineEndCount(
                            getCount(createSpecificRequest(accountNumber, start, end, TemuListingEnums.StatusType.OFFLINE_END), dataType)
                    );
                    temuSkuPublishStatistics.setCreateTime(new Timestamp(new Date().getTime()));
                    SalesmanAccountDetail salesmanAccountDetail = salesmanAccountDetailMap.get(accountNumber);
                    if (salesmanAccountDetail != null) {
                        temuSkuPublishStatistics.setSaleId(EsAccountUtils.getSaleId(salesmanAccountDetail));
                    }
                    return temuSkuPublishStatistics;
                })).collect(Collectors.toList());

        List<TemuSkuPublishStatistics> temuSkuPublishStatisticsList = futures.stream().map(CompletableFuture::join).collect(Collectors.toList());
        if (type == TemuStatisticalTypeEnum.TOTAL) {
            for (TemuSkuPublishStatistics temuSkuPublishStatistics : temuSkuPublishStatisticsList) {
                String accountNumber = temuSkuPublishStatistics.getAccountNumber();
                Integer size = esTemuShopItemInfoService.getAccountItem(accountNumber);
                temuSkuPublishStatistics.setItemCount(size);
            }
        }

        List<List<TemuSkuPublishStatistics>> spuLists = PagingUtils.newPagingList(temuSkuPublishStatisticsList, 100);
        for (List<TemuSkuPublishStatistics> list : spuLists) {
            List<String> accountNumbers = list.stream().map(TemuSkuPublishStatistics::getAccountNumber).collect(Collectors.toList());
            if (accountNumbers.isEmpty()) {
                continue;
            }
            temuSkuPublishStatisticsService.deleteByDayAndAccount(dataType.getCode(), type.getCode(), statisticalTime, accountNumbers);
            temuSkuPublishStatisticsService.batchInsert(list);
        }
    }

    private EsTemuListingRequest createUnpublishRequest(String accountNumber, String start, String end) {
        EsTemuListingRequest request = new EsTemuListingRequest();
        request.setAccountNumber(accountNumber);
        request.setFromCreateDate(start);
        request.setToCreateDate(end);
        request.setEmptyDate(true);
        return request;
    }

    private EsTemuListingRequest createRequest(String accountNumber, String start, String end, TemuListingEnums.StatusType statusType) {
        EsTemuListingRequest request = new EsTemuListingRequest();
        request.setAccountNumber(accountNumber);
        request.setFromCreateDate(start);
        request.setToCreateDate(end);
        if (statusType != null) {
            request.setStatusType(statusType.getCode());
        }
        return request;
    }

    private EsTemuListingRequest createSpecificRequest(String accountNumber, String start, String end, TemuListingEnums.StatusType statusType) {
        EsTemuListingRequest request = new EsTemuListingRequest();
        request.setAccountNumber(accountNumber);
        switch (statusType) {
            case WAIT_SAMPLE:
            case IN_REVIEW:
            case IN_PRICE_DECLARATION:
                request.setFromSelectedTime(start);
                request.setToSelectedTime(end);
                request.setStatusType(statusType.getCode());
                break;
            case WAIT_FIRST_ORDER:
                request.setFromPriceVerificationTime(start);
                request.setToPriceVerificationTime(end);
                break;
            case ON_FIRST_ORDER:
                request.setFromFirstPurchaseTime(start);
                request.setToFirstPurchaseTime(end);
                break;
            case PUBLISHED_SITE:
                request.setFromAddedToSiteTime(start);
                request.setToAddedToSiteTime(end);
                break;
            case OFFLINE_END:
                request.setFromOfflineTime(start);
                request.setToOfflineTime(end);
                break;
        }
        return request;
    }

    private int getCount(EsTemuListingRequest request, TemuStatisticalDataTypeEnums dataType) {
        Long count = null;
        if (TemuStatisticalDataTypeEnums.SPU.equals(dataType)) {
            count = esTemuListingService.searchSpuCount(request);
        } else {
            count = esTemuListingService.searchCount(request);
        }

        return Optional.ofNullable(count).orElse(0L).intValue();
    }

    public void doSaleIdIsNull() {
        int gtId = 0;
        List<TemuSkuPublishStatistics> updateList = new ArrayList<>();

        while (true) {
            TemuSkuPublishStatisticsExample example = new TemuSkuPublishStatisticsExample();
            TemuSkuPublishStatisticsExample.Criteria criteria = example.createCriteria();
            criteria.andSaleIdIsNull();
            criteria.andIdGreaterThan(gtId);
            example.setOrderByClause("id asc");
            example.setLimit(1000);
            example.setOffset(0);
            List<TemuSkuPublishStatistics> temuSkuPublishStatistics = temuSkuPublishStatisticsService.selectByExample(example);
            if (CollectionUtils.isEmpty(temuSkuPublishStatistics)) {
                break;
            }

            gtId = temuSkuPublishStatistics.get(temuSkuPublishStatistics.size() - 1).getId();

            List<String> accountNumberList = temuSkuPublishStatistics.stream().map(TemuSkuPublishStatistics::getAccountNumber).filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());

            Map<String, SalesmanAccountDetail> salesmanAccountDetailMap = new HashMap<>();
            if (CollectionUtils.isEmpty(salesmanAccountDetailMap)) {
                salesmanAccountDetailMap.putAll(EsAccountUtils.getSalesmanAccountDetailMapByEs(accountNumberList, SaleChannel.CHANNEL_TEMU));
            }
            if (accountNumberList.isEmpty()) {
                continue;
            }

            for (TemuSkuPublishStatistics temuSkuPublishStatistic : temuSkuPublishStatistics) {
                String accountNumber = temuSkuPublishStatistic.getAccountNumber();
                if (StringUtils.isBlank(accountNumber)) {
                    continue;
                }
                SalesmanAccountDetail salesmanAccountDetail = salesmanAccountDetailMap.get(accountNumber);
                if (salesmanAccountDetail == null) {
                    continue;
                }
                String saleId = EsAccountUtils.getSaleId(accountNumber);
                if (StringUtils.isBlank(saleId)) {
                    continue;
                }
                temuSkuPublishStatistic.setSaleId(saleId);
                updateList.add(temuSkuPublishStatistic);
            }
        }


        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(updateList)) {
            List<List<TemuSkuPublishStatistics>> lists = PagingUtils.newPagingList(updateList, 100);
            for (List<TemuSkuPublishStatistics> list : lists) {
                temuSkuPublishStatisticsService.batchUpdateSaleId(list);
            }
        }
    }
}

package com.estone.erp.publish.fyndiq.job;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.estone.erp.common.jobHandler.AbstractJobHandler;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.common.executors.FyndiqExecutors;
import com.estone.erp.publish.elasticsearch4.model.EsFyndiqItem;
import com.estone.erp.publish.elasticsearch4.model.beanrequest.EsFyndiqItemRequest;
import com.estone.erp.publish.elasticsearch4.service.EsFyndiqItemService;
import com.estone.erp.publish.fyndiq.call.articles.FyndiqArticlesCall;
import com.estone.erp.publish.fyndiq.componet.FyndiqFeedTaskHelper;
import com.estone.erp.publish.fyndiq.enums.FyndiqOperateTypeEnum;
import com.estone.erp.publish.fyndiq.enums.FyndiqProductStatusEnum;
import com.estone.erp.publish.fyndiq.model.dto.FyndiqCallResponse;
import com.estone.erp.publish.system.account.AccountUtils;
import com.estone.erp.publish.system.account.SaleAccountAndBusinessResponse;
import com.estone.erp.publish.system.feedTask.model.FeedTask;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * Fyndiq  更新价格和税率任务
 *
 * <AUTHOR>
 * @date 2024-10-08 下午12:05
 */
@Component
public class FyndiqUpdatePriceVatJobHandler extends AbstractJobHandler {

    @Resource
    private EsFyndiqItemService esFyndiqItemService;
    @Resource
    private FyndiqFeedTaskHelper fyndiqFeedTaskHelper;

    public FyndiqUpdatePriceVatJobHandler() {
        super(FyndiqUpdatePriceVatJobHandler.class.getName());
    }

    @Data
    private static class InnerParam {
        private String site;
        private String vatRate;
        private List<String> sellerSkus;
        private List<String> accountNumbers;
    }

    @Override
    @XxlJob("fyndiqUpdatePriceVatJobHandler")
    public ReturnT<String> run(String param) throws Exception {
        InnerParam innerParam = passParam(param, InnerParam.class);

        List<SaleAccountAndBusinessResponse> saleAccounts = AccountUtils.getSaleAccountListBySaleChannel(SaleChannel.CHANNEL_FYNDIQ);
        if (CollectionUtils.isNotEmpty(innerParam.getAccountNumbers())) {
            saleAccounts.removeIf(account -> !innerParam.getAccountNumbers().contains(account.getAccountNumber()));
        }

        for (SaleAccountAndBusinessResponse saleAccount : saleAccounts) {
            try {
                updatePriceAndVat(saleAccount, innerParam);
            } catch (Exception e) {
                XxlJobLogger.log("店铺：{}，更新价格和税率失败，原因：{}", saleAccount.getAccountNumber(), e.getMessage());
            }
        }
        return ReturnT.SUCCESS;
    }

    private void updatePriceAndVat(SaleAccountAndBusinessResponse saleAccount, InnerParam innerParam) {
        EsFyndiqItemRequest request = new EsFyndiqItemRequest();
        request.setAccountNumber(saleAccount.getAccountNumber());
        if (CollectionUtils.isNotEmpty(innerParam.getSellerSkus())) {
            request.setSellerSkuList(innerParam.getSellerSkus());
        }
        request.setProductStatus(FyndiqProductStatusEnum.FOR_SALE.getStatusMsgEn());
        request.setOrderBy("creationDate");
        request.setSequence("ASC");

        List<String> sellerSkus = new ArrayList<>();
        esFyndiqItemService.scrollQueryExecutorTask(request, items -> {
            if (CollectionUtils.isNotEmpty(items)) {
                items.forEach(item -> sellerSkus.add(item.getSellerSku()));
            }
        });

        if (CollectionUtils.isEmpty(sellerSkus)) {
            return;
        }
        String site = innerParam.getSite();
        String vatRate = innerParam.getVatRate();
        for (String sellerSku : sellerSkus) {
            FyndiqExecutors.syncAccountItem(() -> {
                try {
                    executeUpdateHandler(saleAccount, sellerSku, site, vatRate);
                } catch (Exception e) {
                    XxlJobLogger.log("店铺：{}，sellerSku：{}，更新价格和税率失败，原因：{}", saleAccount.getAccountNumber(), sellerSku, e.getMessage());
                }
            });
        }
    }

    private void executeUpdateHandler(SaleAccountAndBusinessResponse saleAccount, String sellerSku, String site, String vatRate) {
        List<EsFyndiqItem> esFyndiqItems = new FyndiqArticlesCall(saleAccount).getItemBySku(sellerSku);
        if (CollectionUtils.isEmpty(esFyndiqItems)) {
            XxlJobLogger.log("店铺：{}，sellerSku：{}，更新价格和税率失败，原因：同步sku失败", saleAccount.getAccountNumber(), sellerSku);
            return;
        }

        FeedTask feedTask = fyndiqFeedTaskHelper.initFeedTask(SaleChannel.CHANNEL_FYNDIQ, saleAccount.getAccountNumber(), FyndiqOperateTypeEnum.UPDATE_VAR.getStatusMsgEn(), sellerSku, site);
        EsFyndiqItem esFyndiqItem = esFyndiqItems.get(0);
        String priceText = esFyndiqItem.getPriceText();
        JSONArray priceArray = JSON.parseArray(priceText);
        for (int i = 0; i < priceArray.size(); i++) {
            JSONObject priceJson = priceArray.getJSONObject(i);
            String market = priceJson.getString("market");
            if (!site.equals(market)) {
                continue;
            }
            JSONObject priceValue = priceJson.getJSONObject("value");
            feedTask.setAttribute1(priceValue.getString("vat_rate"));
            priceValue.put("vat_rate", vatRate);
            feedTask.setAttribute2(vatRate);
        }

        esFyndiqItem.setPriceText(priceArray.toJSONString());
        FyndiqArticlesCall call = new FyndiqArticlesCall(saleAccount);
        Map<String, FyndiqCallResponse> callResponseMap = call.batchUpdateArticle(List.of(esFyndiqItem), FyndiqOperateTypeEnum.UPDATE_VAR.getStatusMsgEn());
        FyndiqCallResponse fyndiqCallResponse = callResponseMap.get(esFyndiqItem.getId());
        if (null != fyndiqCallResponse && HttpStatus.ACCEPTED.value() == fyndiqCallResponse.getStatusCode()) {
            fyndiqFeedTaskHelper.succeedFeedTask(feedTask, "更新成功");
        } else {
            fyndiqFeedTaskHelper.failFeedTask(feedTask, JSON.toJSONString(fyndiqCallResponse));
        }
    }


}

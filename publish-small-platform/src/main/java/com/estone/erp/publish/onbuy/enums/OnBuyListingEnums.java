package com.estone.erp.publish.onbuy.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

public interface OnBuyListingEnums {


    @Getter
    @AllArgsConstructor
    enum Status {
        ACTIVE(1000,"有库存"),
        IN_ACTIVE(1001, "无库存"),

        BOOSTED(2000, "Boosted 值大于0"),
        UN_BOOSTED (2001, "Boosted 值等于0"),

        SALE_PRICE_SET (3000, "有促销价"),
        NOT_SALE_PRICE_SET(3001, "无促销价");

        private final Integer code;
        private final String desc;

        public boolean isTrue(long val) {
            return this.code == val;
        }

        public static String getNameByCode(Integer code) {
            for (Status value : values()) {
                if (value.code.equals(code)) {
                    return value.name();
                }
            }
            return "UN KNOW";
        }
    }

    @Getter
    @AllArgsConstructor
    enum Boosted {
        PAUSED("0.0","Paused"),
        LIGHT("5.0","Light"),
        PLUS("10.0","Plus"),
        PRO("15.0","Pro"),
        MAX("20.0","Max"),
        ULTRA("30.0","Ultra");

        private final String code;
        private final String desc;
    }
}

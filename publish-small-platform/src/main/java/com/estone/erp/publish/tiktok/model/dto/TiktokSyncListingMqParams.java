package com.estone.erp.publish.tiktok.model.dto;

import lombok.Data;


/**
 * 全量同步传递
 */
@Data
public class TiktokSyncListingMqParams {

    /**
     * 操作人 null为admin
     */
    private String operator;

    /**
     * 任务id
     */
    private Long feedTaskId;

    /**
     * 店铺账号
     */
    private String accountNumber;

    /**
     * 大于等于
     */
    private String updatedGe;

    /**
     * 小于等于
     */
    private String updatedLe;

    /**
     * 同步失败是否需要重试
     */
    private Boolean needRetry = true;

}

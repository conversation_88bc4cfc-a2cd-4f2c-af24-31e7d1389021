package com.estone.erp.publish.joom.service;

import com.estone.erp.publish.joom.BO.DashboardRequestDO;
import com.estone.erp.publish.joom.BO.SalesStatisticsDataVO;
import com.estone.erp.publish.system.newUsermgt.model.NewUser;

/**
 * 刊登首页-数据看板服务
 */
public interface DashboardService {


    /**
     * 加载首页数据
     * @param param        页面入参
     * @param currentUser  当前登录用户
     */
    SalesStatisticsDataVO loadIndexData(DashboardRequestDO param, NewUser currentUser);
}

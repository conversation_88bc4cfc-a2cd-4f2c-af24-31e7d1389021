package com.estone.erp.publish.joom.jobHandler;

import com.estone.erp.common.jobHandler.AbstractJobHandler;
import com.estone.erp.publish.common.util.DateUtils;
import com.estone.erp.publish.elasticsearch.model.beanrequest.EsRecommendProductRequest;
import com.estone.erp.publish.elasticsearch.service.EsRecommendProductService;
import com.estone.erp.publish.mq.bean.ChangeSku;
import com.google.common.collect.Lists;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.amqp.AmqpException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @description 刷新推荐产品单品信息任务
 */
@Component
@Slf4j
public class RefreshEsRecommendProductMsgJobHandler extends AbstractJobHandler {

    @Autowired
    private EsRecommendProductService esRecommendProductService;

    public RefreshEsRecommendProductMsgJobHandler() {
        super(RefreshEsRecommendProductMsgJobHandler.class.getName());
    }

    @Override
    @XxlJob("RefreshEsRecommendProductMsgJobHandler")
    public ReturnT<String> run(String param) throws Exception {
        XxlJobLogger.log("-------刷新推荐产品单品信息开始--------");
        // 更新单品信息为空的当天数据
        EsRecommendProductRequest request = new EsRecommendProductRequest();
        String [] fileds = {"articleNumber"};
        request.setFields(fileds);
        if (StringUtils.isNotBlank(param)){
            request.setRecommendDate(param);
        }else {
            request.setRecommendDate(DateUtils.dateToString(DateUtils.getToday(), "yyyy-MM-dd HH:mm:ss"));
        }
        request.setIsExistSkuStatus(false);
        List<String> articleNumberListList =esRecommendProductService .getArticleNumberListList(request);
        if (CollectionUtils.isEmpty(articleNumberListList)){
            XxlJobLogger.log("-------查询推荐产品货号条数为空--------");
        }
        XxlJobLogger.log("-------查询推荐产品货号条数为： " + articleNumberListList.size());
        List<List<String>> articleNumbers = Lists.partition(articleNumberListList, 200);
        for (List<String> list : articleNumbers) {
            try {
                ChangeSku changeSku = new ChangeSku();
                changeSku.setSkuList(list);
                // 发送消息
                esRecommendProductService.sendMqRefreshEsRecommendProduct(changeSku);
            }catch (AmqpException e){
                XxlJobLogger.log("-------发送推荐产品货号到队列异常--------" + e.getMessage());
            }
        }
        XxlJobLogger.log("-------刷新推荐产品单品信息结束--------");
        return ReturnT.SUCCESS;
    }

}

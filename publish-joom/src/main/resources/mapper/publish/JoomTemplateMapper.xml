<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.estone.erp.publish.joom.mapper.JoomTemplateMapper" >
  <resultMap id="BaseResultMap" type="com.estone.erp.publish.joom.model.JoomTemplate" >
    <id column="template_id" property="templateId" jdbcType="INTEGER" />
    <result column="seller_id" property="sellerId" jdbcType="VARCHAR" />
    <result column="name" property="name" jdbcType="VARCHAR" />
    <result column="sku" property="sku" jdbcType="VARCHAR" />
    <result column="description" property="description" jdbcType="VARCHAR" />
    <result column="tags" property="tags" jdbcType="VARCHAR" />
    <result column="sensitive_goods" property="sensitiveGoods" jdbcType="VARCHAR" />
    <result column="price" property="price" jdbcType="DOUBLE" />
    <result column="msrp" property="msrp" jdbcType="DOUBLE" />
    <result column="inventory" property="inventory" jdbcType="INTEGER" />
    <result column="brand" property="brand" jdbcType="VARCHAR" />
    <result column="upc" property="upc" jdbcType="VARCHAR" />
    <result column="landing_page_url" property="landingPageUrl" jdbcType="VARCHAR" />
    <result column="content" property="content" jdbcType="VARCHAR" />
    <result column="main_image" property="mainImage" jdbcType="VARCHAR" />
    <result column="shipping" property="shipping" jdbcType="DOUBLE" />
    <result column="shipping_time" property="shippingTime" jdbcType="VARCHAR" />
    <result column="variations" property="variations" jdbcType="VARCHAR" />
    <result column="extra_images" property="extraImages" jdbcType="VARCHAR" />
    <result column="status" property="status" jdbcType="INTEGER" />
    <result column="category_id" property="categoryId" jdbcType="INTEGER" />
    <result column="is_multiple_attribute" property="isMultipleAttribute" jdbcType="BIT" />
    <result column="is_lock" property="isLock" jdbcType="BIT" />
    <result column="full_path_code" property="fullPathCode" jdbcType="VARCHAR" />
    <result column="display_name" property="displayName" jdbcType="VARCHAR" />
    <result column="creation_date" property="creationDate" jdbcType="TIMESTAMP" />
    <result column="created_by" property="createdBy" jdbcType="VARCHAR" />
    <result column="last_update_date" property="lastUpdateDate" jdbcType="TIMESTAMP" />
    <result column="last_updated_by" property="lastUpdatedBy" jdbcType="VARCHAR" />
    <result column="copy_count" property="copyCount" jdbcType="INTEGER" />
    <result column="upload_count" property="uploadCount" jdbcType="INTEGER" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    template_id, seller_id, `name`, sku, description, tags, sensitive_goods, price, msrp, 
    inventory, brand, upc, landing_page_url, content, main_image, shipping, shipping_time, 
    variations, extra_images, `status`, category_id, is_multiple_attribute, is_lock, 
    full_path_code, display_name, creation_date, created_by, last_update_date, last_updated_by, 
    copy_count, upload_count
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.estone.erp.publish.joom.model.JoomTemplateExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from joom_template
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
    <if test="limit != null" >
      <if test="offset != null" >
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null" >
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    select 
    <include refid="Base_Column_List" />
    from joom_template
    where template_id = #{templateId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer" >
    delete from joom_template
    where template_id = #{templateId,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.estone.erp.publish.joom.model.JoomTemplateExample" >
    delete from joom_template
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <delete id="batchDelete">
    delete from joom_template
    where template_id in
   		<foreach close=")" collection="ids" item="id" open="(" separator=",">
			#{id}
		</foreach>
  </delete>
  <insert id="insert" parameterType="com.estone.erp.publish.joom.model.JoomTemplate" >
    <selectKey resultType="java.lang.Integer" keyProperty="templateId" order="AFTER" >
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into joom_template (seller_id, `name`, sku, 
      description, tags, sensitive_goods, 
      price, msrp, inventory, 
      brand, upc, landing_page_url, 
      content, main_image, shipping, 
      shipping_time, variations, extra_images, 
      `status`, category_id, is_multiple_attribute, 
      is_lock, full_path_code, display_name, 
      creation_date, created_by, last_update_date, 
      last_updated_by, copy_count, upload_count
      )
    values (#{sellerId,jdbcType=VARCHAR}, #{name,jdbcType=VARCHAR}, #{sku,jdbcType=VARCHAR}, 
      #{description,jdbcType=VARCHAR}, #{tags,jdbcType=VARCHAR}, #{sensitiveGoods,jdbcType=VARCHAR}, 
      #{price,jdbcType=DOUBLE}, #{msrp,jdbcType=DOUBLE}, #{inventory,jdbcType=INTEGER}, 
      #{brand,jdbcType=VARCHAR}, #{upc,jdbcType=VARCHAR}, #{landingPageUrl,jdbcType=VARCHAR}, 
      #{content,jdbcType=VARCHAR}, #{mainImage,jdbcType=VARCHAR}, #{shipping,jdbcType=DOUBLE}, 
      #{shippingTime,jdbcType=VARCHAR}, #{variations,jdbcType=VARCHAR}, #{extraImages,jdbcType=VARCHAR}, 
      #{status,jdbcType=INTEGER}, #{categoryId,jdbcType=INTEGER}, #{isMultipleAttribute,jdbcType=BIT}, 
      #{isLock,jdbcType=BIT}, #{fullPathCode,jdbcType=VARCHAR}, #{displayName,jdbcType=VARCHAR}, 
      #{creationDate,jdbcType=TIMESTAMP}, #{createdBy,jdbcType=VARCHAR}, #{lastUpdateDate,jdbcType=TIMESTAMP}, 
      #{lastUpdatedBy,jdbcType=VARCHAR}, #{copyCount,jdbcType=INTEGER}, #{uploadCount,jdbcType=INTEGER}
      )
  </insert>
  <insert id="batchInsertJoomTemplate">
    <foreach collection="recordList" item="record" open="" separator=";" close=";"> 
    insert into joom_template (seller_id, `name`, sku, 
      description, tags, sensitive_goods, 
      price, msrp, inventory, 
      brand, upc, landing_page_url, 
      content, main_image, shipping, 
      shipping_time, variations, extra_images, 
      `status`, category_id, is_multiple_attribute, 
      is_lock, full_path_code, display_name, 
      creation_date, created_by, last_update_date, 
      last_updated_by, copy_count, upload_count
      ) values (
      #{record.sellerId,jdbcType=VARCHAR}, #{record.name,jdbcType=VARCHAR}, #{record.sku,jdbcType=VARCHAR}, 
      #{record.description,jdbcType=VARCHAR}, #{record.tags,jdbcType=VARCHAR}, #{record.sensitiveGoods,jdbcType=VARCHAR}, 
      #{record.price,jdbcType=DOUBLE}, #{record.msrp,jdbcType=DOUBLE}, #{record.inventory,jdbcType=INTEGER}, 
      #{record.brand,jdbcType=VARCHAR}, #{record.upc,jdbcType=VARCHAR}, #{record.landingPageUrl,jdbcType=VARCHAR}, 
      #{record.content,jdbcType=VARCHAR}, #{record.mainImage,jdbcType=VARCHAR}, #{record.shipping,jdbcType=DOUBLE}, 
      #{record.shippingTime,jdbcType=VARCHAR}, #{record.variations,jdbcType=VARCHAR}, #{record.extraImages,jdbcType=VARCHAR}, 
      #{record.status,jdbcType=INTEGER}, #{record.categoryId,jdbcType=INTEGER}, #{record.isMultipleAttribute,jdbcType=BIT}, 
      #{record.isLock,jdbcType=BIT}, #{record.fullPathCode,jdbcType=VARCHAR}, #{record.displayName,jdbcType=VARCHAR}, 
      #{record.creationDate,jdbcType=TIMESTAMP}, #{record.createdBy,jdbcType=VARCHAR}, #{record.lastUpdateDate,jdbcType=TIMESTAMP}, 
      #{record.lastUpdatedBy,jdbcType=VARCHAR}, #{record.copyCount,jdbcType=INTEGER}, #{record.uploadCount,jdbcType=INTEGER})
    </foreach>
  </insert>
  <insert id="insertSelective" parameterType="com.estone.erp.publish.joom.model.JoomTemplate" >
    <selectKey resultType="java.lang.Integer" keyProperty="templateId" order="AFTER" >
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into joom_template
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="sellerId != null" >
        seller_id,
      </if>
      <if test="name != null" >
        `name`,
      </if>
      <if test="sku != null" >
        sku,
      </if>
      <if test="description != null" >
        description,
      </if>
      <if test="tags != null" >
        tags,
      </if>
      <if test="sensitiveGoods != null" >
        sensitive_goods,
      </if>
      <if test="price != null" >
        price,
      </if>
      <if test="msrp != null" >
        msrp,
      </if>
      <if test="inventory != null" >
        inventory,
      </if>
      <if test="brand != null" >
        brand,
      </if>
      <if test="upc != null" >
        upc,
      </if>
      <if test="landingPageUrl != null" >
        landing_page_url,
      </if>
      <if test="content != null" >
        content,
      </if>
      <if test="mainImage != null" >
        main_image,
      </if>
      <if test="shipping != null" >
        shipping,
      </if>
      <if test="shippingTime != null" >
        shipping_time,
      </if>
      <if test="variations != null" >
        variations,
      </if>
      <if test="extraImages != null" >
        extra_images,
      </if>
      <if test="status != null" >
        `status`,
      </if>
      <if test="categoryId != null" >
        category_id,
      </if>
      <if test="isMultipleAttribute != null" >
        is_multiple_attribute,
      </if>
      <if test="isLock != null" >
        is_lock,
      </if>
      <if test="fullPathCode != null" >
        full_path_code,
      </if>
      <if test="displayName != null" >
        display_name,
      </if>
      <if test="creationDate != null" >
        creation_date,
      </if>
      <if test="createdBy != null" >
        created_by,
      </if>
      <if test="lastUpdateDate != null" >
        last_update_date,
      </if>
      <if test="lastUpdatedBy != null" >
        last_updated_by,
      </if>
      <if test="copyCount != null" >
        copy_count,
      </if>
      <if test="uploadCount != null" >
        upload_count,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="sellerId != null" >
        #{sellerId,jdbcType=VARCHAR},
      </if>
      <if test="name != null" >
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="sku != null" >
        #{sku,jdbcType=VARCHAR},
      </if>
      <if test="description != null" >
        #{description,jdbcType=VARCHAR},
      </if>
      <if test="tags != null" >
        #{tags,jdbcType=VARCHAR},
      </if>
      <if test="sensitiveGoods != null" >
        #{sensitiveGoods,jdbcType=VARCHAR},
      </if>
      <if test="price != null" >
        #{price,jdbcType=DOUBLE},
      </if>
      <if test="msrp != null" >
        #{msrp,jdbcType=DOUBLE},
      </if>
      <if test="inventory != null" >
        #{inventory,jdbcType=INTEGER},
      </if>
      <if test="brand != null" >
        #{brand,jdbcType=VARCHAR},
      </if>
      <if test="upc != null" >
        #{upc,jdbcType=VARCHAR},
      </if>
      <if test="landingPageUrl != null" >
        #{landingPageUrl,jdbcType=VARCHAR},
      </if>
      <if test="content != null" >
        #{content,jdbcType=VARCHAR},
      </if>
      <if test="mainImage != null" >
        #{mainImage,jdbcType=VARCHAR},
      </if>
      <if test="shipping != null" >
        #{shipping,jdbcType=DOUBLE},
      </if>
      <if test="shippingTime != null" >
        #{shippingTime,jdbcType=VARCHAR},
      </if>
      <if test="variations != null" >
        #{variations,jdbcType=VARCHAR},
      </if>
      <if test="extraImages != null" >
        #{extraImages,jdbcType=VARCHAR},
      </if>
      <if test="status != null" >
        #{status,jdbcType=INTEGER},
      </if>
      <if test="categoryId != null" >
        #{categoryId,jdbcType=INTEGER},
      </if>
      <if test="isMultipleAttribute != null" >
        #{isMultipleAttribute,jdbcType=BIT},
      </if>
      <if test="isLock != null" >
        #{isLock,jdbcType=BIT},
      </if>
      <if test="fullPathCode != null" >
        #{fullPathCode,jdbcType=VARCHAR},
      </if>
      <if test="displayName != null" >
        #{displayName,jdbcType=VARCHAR},
      </if>
      <if test="creationDate != null" >
        #{creationDate,jdbcType=TIMESTAMP},
      </if>
      <if test="createdBy != null" >
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="lastUpdateDate != null" >
        #{lastUpdateDate,jdbcType=TIMESTAMP},
      </if>
      <if test="lastUpdatedBy != null" >
        #{lastUpdatedBy,jdbcType=VARCHAR},
      </if>
      <if test="copyCount != null" >
        #{copyCount,jdbcType=INTEGER},
      </if>
      <if test="uploadCount != null" >
        #{uploadCount,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.estone.erp.publish.joom.model.JoomTemplateExample" resultType="java.lang.Integer" >
    select count(*) from joom_template
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update joom_template
    <set >
      <if test="record.templateId != null" >
        template_id = #{record.templateId,jdbcType=INTEGER},
      </if>
      <if test="record.sellerId != null" >
        seller_id = #{record.sellerId,jdbcType=VARCHAR},
      </if>
      <if test="record.name != null" >
        `name` = #{record.name,jdbcType=VARCHAR},
      </if>
      <if test="record.sku != null" >
        sku = #{record.sku,jdbcType=VARCHAR},
      </if>
      <if test="record.description != null" >
        description = #{record.description,jdbcType=VARCHAR},
      </if>
      <if test="record.tags != null" >
        tags = #{record.tags,jdbcType=VARCHAR},
      </if>
      <if test="record.sensitiveGoods != null" >
        sensitive_goods = #{record.sensitiveGoods,jdbcType=VARCHAR},
      </if>
      <if test="record.price != null" >
        price = #{record.price,jdbcType=DOUBLE},
      </if>
      <if test="record.msrp != null" >
        msrp = #{record.msrp,jdbcType=DOUBLE},
      </if>
      <if test="record.inventory != null" >
        inventory = #{record.inventory,jdbcType=INTEGER},
      </if>
      <if test="record.brand != null" >
        brand = #{record.brand,jdbcType=VARCHAR},
      </if>
      <if test="record.upc != null" >
        upc = #{record.upc,jdbcType=VARCHAR},
      </if>
      <if test="record.landingPageUrl != null" >
        landing_page_url = #{record.landingPageUrl,jdbcType=VARCHAR},
      </if>
      <if test="record.content != null" >
        content = #{record.content,jdbcType=VARCHAR},
      </if>
      <if test="record.mainImage != null" >
        main_image = #{record.mainImage,jdbcType=VARCHAR},
      </if>
      <if test="record.shipping != null" >
        shipping = #{record.shipping,jdbcType=DOUBLE},
      </if>
      <if test="record.shippingTime != null" >
        shipping_time = #{record.shippingTime,jdbcType=VARCHAR},
      </if>
      <if test="record.variations != null" >
        variations = #{record.variations,jdbcType=VARCHAR},
      </if>
      <if test="record.extraImages != null" >
        extra_images = #{record.extraImages,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null" >
        `status` = #{record.status,jdbcType=INTEGER},
      </if>
      <if test="record.categoryId != null" >
        category_id = #{record.categoryId,jdbcType=INTEGER},
      </if>
      <if test="record.isMultipleAttribute != null" >
        is_multiple_attribute = #{record.isMultipleAttribute,jdbcType=BIT},
      </if>
      <if test="record.isLock != null" >
        is_lock = #{record.isLock,jdbcType=BIT},
      </if>
      <if test="record.fullPathCode != null" >
        full_path_code = #{record.fullPathCode,jdbcType=VARCHAR},
      </if>
      <if test="record.displayName != null" >
        display_name = #{record.displayName,jdbcType=VARCHAR},
      </if>
      <if test="record.creationDate != null" >
        creation_date = #{record.creationDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.createdBy != null" >
        created_by = #{record.createdBy,jdbcType=VARCHAR},
      </if>
      <if test="record.lastUpdateDate != null" >
        last_update_date = #{record.lastUpdateDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.lastUpdatedBy != null" >
        last_updated_by = #{record.lastUpdatedBy,jdbcType=VARCHAR},
      </if>
      <if test="record.copyCount != null" >
        copy_count = #{record.copyCount,jdbcType=INTEGER},
      </if>
      <if test="record.uploadCount != null" >
        upload_count = #{record.uploadCount,jdbcType=INTEGER},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="batchUpdateJoomTemplate" parameterType="java.util.List">
  	<foreach collection="recordList" index="index" item="record" open="" separator=";" close=";">
	    update joom_template
	    <set >
	      <if test="record.sellerId != null" >
	        seller_id = #{record.sellerId,jdbcType=VARCHAR},
	      </if>
	      <if test="record.name != null" >
	        `name` = #{record.name,jdbcType=VARCHAR},
	      </if>
	      <if test="record.sku != null" >
	        sku = #{record.sku,jdbcType=VARCHAR},
	      </if>
	      <if test="record.description != null" >
	        description = #{record.description,jdbcType=VARCHAR},
	      </if>
	      <if test="record.tags != null" >
	        tags = #{record.tags,jdbcType=VARCHAR},
	      </if>
	      <if test="record.sensitiveGoods != null" >
	        sensitive_goods = #{record.sensitiveGoods,jdbcType=VARCHAR},
	      </if>
	      <if test="record.price != null" >
	        price = #{record.price,jdbcType=DOUBLE},
	      </if>
	      <if test="record.msrp != null" >
	        msrp = #{record.msrp,jdbcType=DOUBLE},
	      </if>
	      <if test="record.inventory != null" >
	        inventory = #{record.inventory,jdbcType=INTEGER},
	      </if>
	      <if test="record.brand != null" >
	        brand = #{record.brand,jdbcType=VARCHAR},
	      </if>
	      <if test="record.upc != null" >
	        upc = #{record.upc,jdbcType=VARCHAR},
	      </if>
	      <if test="record.landingPageUrl != null" >
	        landing_page_url = #{record.landingPageUrl,jdbcType=VARCHAR},
	      </if>
	      <if test="record.content != null" >
	        content = #{record.content,jdbcType=VARCHAR},
	      </if>
	      <if test="record.mainImage != null" >
	        main_image = #{record.mainImage,jdbcType=VARCHAR},
	      </if>
	      <if test="record.shipping != null" >
	        shipping = #{record.shipping,jdbcType=DOUBLE},
	      </if>
	      <if test="record.shippingTime != null" >
	        shipping_time = #{record.shippingTime,jdbcType=VARCHAR},
	      </if>
	      <if test="record.variations != null" >
	        variations = #{record.variations,jdbcType=VARCHAR},
	      </if>
	      <if test="record.extraImages != null" >
	        extra_images = #{record.extraImages,jdbcType=VARCHAR},
	      </if>
	      <if test="record.status != null" >
	        `status` = #{record.status,jdbcType=INTEGER},
	      </if>
	      <if test="record.categoryId != null" >
	        category_id = #{record.categoryId,jdbcType=INTEGER},
	      </if>
	      <if test="record.isMultipleAttribute != null" >
	        is_multiple_attribute = #{record.isMultipleAttribute,jdbcType=BIT},
	      </if>
	      <if test="record.isLock != null" >
	        is_lock = #{record.isLock,jdbcType=BIT},
	      </if>
	      <if test="record.fullPathCode != null" >
	        full_path_code = #{record.fullPathCode,jdbcType=VARCHAR},
	      </if>
	      <if test="record.displayName != null" >
	        display_name = #{record.displayName,jdbcType=VARCHAR},
	      </if>
	      <if test="record.creationDate != null" >
	        creation_date = #{record.creationDate,jdbcType=TIMESTAMP},
	      </if>
	      <if test="record.createdBy != null" >
	        created_by = #{record.createdBy,jdbcType=VARCHAR},
	      </if>
	      <if test="record.lastUpdateDate != null" >
	        last_update_date = #{record.lastUpdateDate,jdbcType=TIMESTAMP},
	      </if>
	      <if test="record.lastUpdatedBy != null" >
	        last_updated_by = #{record.lastUpdatedBy,jdbcType=VARCHAR},
	      </if>
	      <if test="record.copyCount != null" >
	        copy_count = #{record.copyCount,jdbcType=INTEGER},
	      </if>
	      <if test="record.uploadCount != null" >
	        upload_count = #{record.uploadCount,jdbcType=INTEGER},
	      </if>
	      <if test="record.templateId != null" >
	        template_id = #{record.templateId,jdbcType=INTEGER}
	      </if>
	      where template_id = #{record.templateId,jdbcType=INTEGER}
	    </set>
    </foreach>
  </update>
  <update id="updateByExample" parameterType="map" >
    update joom_template
    set template_id = #{record.templateId,jdbcType=INTEGER},
      seller_id = #{record.sellerId,jdbcType=VARCHAR},
      `name` = #{record.name,jdbcType=VARCHAR},
      sku = #{record.sku,jdbcType=VARCHAR},
      description = #{record.description,jdbcType=VARCHAR},
      tags = #{record.tags,jdbcType=VARCHAR},
      sensitive_goods = #{record.sensitiveGoods,jdbcType=VARCHAR},
      price = #{record.price,jdbcType=DOUBLE},
      msrp = #{record.msrp,jdbcType=DOUBLE},
      inventory = #{record.inventory,jdbcType=INTEGER},
      brand = #{record.brand,jdbcType=VARCHAR},
      upc = #{record.upc,jdbcType=VARCHAR},
      landing_page_url = #{record.landingPageUrl,jdbcType=VARCHAR},
      content = #{record.content,jdbcType=VARCHAR},
      main_image = #{record.mainImage,jdbcType=VARCHAR},
      shipping = #{record.shipping,jdbcType=DOUBLE},
      shipping_time = #{record.shippingTime,jdbcType=VARCHAR},
      variations = #{record.variations,jdbcType=VARCHAR},
      extra_images = #{record.extraImages,jdbcType=VARCHAR},
      `status` = #{record.status,jdbcType=INTEGER},
      category_id = #{record.categoryId,jdbcType=INTEGER},
      is_multiple_attribute = #{record.isMultipleAttribute,jdbcType=BIT},
      is_lock = #{record.isLock,jdbcType=BIT},
      full_path_code = #{record.fullPathCode,jdbcType=VARCHAR},
      display_name = #{record.displayName,jdbcType=VARCHAR},
      creation_date = #{record.creationDate,jdbcType=TIMESTAMP},
      created_by = #{record.createdBy,jdbcType=VARCHAR},
      last_update_date = #{record.lastUpdateDate,jdbcType=TIMESTAMP},
      last_updated_by = #{record.lastUpdatedBy,jdbcType=VARCHAR},
      copy_count = #{record.copyCount,jdbcType=INTEGER},
      upload_count = #{record.uploadCount,jdbcType=INTEGER}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.estone.erp.publish.joom.model.JoomTemplate" >
    update joom_template
    <set >
      <if test="sellerId != null" >
        seller_id = #{sellerId,jdbcType=VARCHAR},
      </if>
      <if test="name != null" >
        `name` = #{name,jdbcType=VARCHAR},
      </if>
      <if test="sku != null" >
        sku = #{sku,jdbcType=VARCHAR},
      </if>
      <if test="description != null" >
        description = #{description,jdbcType=VARCHAR},
      </if>
      <if test="tags != null" >
        tags = #{tags,jdbcType=VARCHAR},
      </if>
      <if test="sensitiveGoods != null" >
        sensitive_goods = #{sensitiveGoods,jdbcType=VARCHAR},
      </if>
      <if test="price != null" >
        price = #{price,jdbcType=DOUBLE},
      </if>
      <if test="msrp != null" >
        msrp = #{msrp,jdbcType=DOUBLE},
      </if>
      <if test="inventory != null" >
        inventory = #{inventory,jdbcType=INTEGER},
      </if>
      <if test="brand != null" >
        brand = #{brand,jdbcType=VARCHAR},
      </if>
      <if test="upc != null" >
        upc = #{upc,jdbcType=VARCHAR},
      </if>
      <if test="landingPageUrl != null" >
        landing_page_url = #{landingPageUrl,jdbcType=VARCHAR},
      </if>
      <if test="content != null" >
        content = #{content,jdbcType=VARCHAR},
      </if>
      <if test="mainImage != null" >
        main_image = #{mainImage,jdbcType=VARCHAR},
      </if>
      <if test="shipping != null" >
        shipping = #{shipping,jdbcType=DOUBLE},
      </if>
      <if test="shippingTime != null" >
        shipping_time = #{shippingTime,jdbcType=VARCHAR},
      </if>
      <if test="variations != null" >
        variations = #{variations,jdbcType=VARCHAR},
      </if>
      <if test="extraImages != null" >
        extra_images = #{extraImages,jdbcType=VARCHAR},
      </if>
      <if test="status != null" >
        `status` = #{status,jdbcType=INTEGER},
      </if>
      <if test="categoryId != null" >
        category_id = #{categoryId,jdbcType=INTEGER},
      </if>
      <if test="isMultipleAttribute != null" >
        is_multiple_attribute = #{isMultipleAttribute,jdbcType=BIT},
      </if>
      <if test="isLock != null" >
        is_lock = #{isLock,jdbcType=BIT},
      </if>
      <if test="fullPathCode != null" >
        full_path_code = #{fullPathCode,jdbcType=VARCHAR},
      </if>
      <if test="displayName != null" >
        display_name = #{displayName,jdbcType=VARCHAR},
      </if>
      <if test="creationDate != null" >
        creation_date = #{creationDate,jdbcType=TIMESTAMP},
      </if>
      <if test="createdBy != null" >
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="lastUpdateDate != null" >
        last_update_date = #{lastUpdateDate,jdbcType=TIMESTAMP},
      </if>
      <if test="lastUpdatedBy != null" >
        last_updated_by = #{lastUpdatedBy,jdbcType=VARCHAR},
      </if>
      <if test="copyCount != null" >
        copy_count = #{copyCount,jdbcType=INTEGER},
      </if>
      <if test="uploadCount != null" >
        upload_count = #{uploadCount,jdbcType=INTEGER},
      </if>
    </set>
    where template_id = #{templateId,jdbcType=INTEGER}
  </update>

  <update id="updateJoomTemplate" parameterType="com.estone.erp.publish.joom.model.JoomTemplate" >
    update joom_template
    <set >
      <if test="sellerId != null" >
        seller_id = #{sellerId,jdbcType=VARCHAR},
      </if>
      <if test="name != null" >
        `name` = #{name,jdbcType=VARCHAR},
      </if>
      <if test="sku != null" >
        sku = #{sku,jdbcType=VARCHAR},
      </if>
      <if test="description != null" >
        description = #{description,jdbcType=VARCHAR},
      </if>
      <if test="tags != null" >
        tags = #{tags,jdbcType=VARCHAR},
      </if>
      <if test="sensitiveGoods != null" >
        sensitive_goods = #{sensitiveGoods,jdbcType=VARCHAR},
      </if>
        price = #{price,jdbcType=DOUBLE},
        msrp = #{msrp,jdbcType=DOUBLE},
        inventory = #{inventory,jdbcType=INTEGER},
      <if test="brand != null" >
        brand = #{brand,jdbcType=VARCHAR},
      </if>
      <if test="upc != null" >
        upc = #{upc,jdbcType=VARCHAR},
      </if>
      <if test="landingPageUrl != null" >
        landing_page_url = #{landingPageUrl,jdbcType=VARCHAR},
      </if>
      <if test="content != null" >
        content = #{content,jdbcType=VARCHAR},
      </if>
      <if test="mainImage != null" >
        main_image = #{mainImage,jdbcType=VARCHAR},
      </if>
        shipping = #{shipping,jdbcType=DOUBLE},
      <if test="shippingTime != null" >
        shipping_time = #{shippingTime,jdbcType=VARCHAR},
      </if>
      <if test="variations != null" >
        variations = #{variations,jdbcType=VARCHAR},
      </if>
      <if test="extraImages != null" >
        extra_images = #{extraImages,jdbcType=VARCHAR},
      </if>
      <if test="status != null" >
        `status` = #{status,jdbcType=INTEGER},
      </if>
      <if test="categoryId != null" >
        category_id = #{categoryId,jdbcType=INTEGER},
      </if>
      <if test="isMultipleAttribute != null" >
        is_multiple_attribute = #{isMultipleAttribute,jdbcType=BIT},
      </if>
      <if test="isLock != null" >
        is_lock = #{isLock,jdbcType=BIT},
      </if>
      <if test="fullPathCode != null" >
        full_path_code = #{fullPathCode,jdbcType=VARCHAR},
      </if>
      <if test="displayName != null" >
        display_name = #{displayName,jdbcType=VARCHAR},
      </if>
      <if test="creationDate != null" >
        creation_date = #{creationDate,jdbcType=TIMESTAMP},
      </if>
      <if test="createdBy != null" >
        created_by = #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="lastUpdateDate != null" >
        last_update_date = #{lastUpdateDate,jdbcType=TIMESTAMP},
      </if>
      <if test="lastUpdatedBy != null" >
        last_updated_by = #{lastUpdatedBy,jdbcType=VARCHAR},
      </if>
      <if test="copyCount != null" >
        copy_count = #{copyCount,jdbcType=INTEGER},
      </if>
      <if test="uploadCount != null" >
        upload_count = #{uploadCount,jdbcType=INTEGER},
      </if>
    </set>
    where template_id = #{templateId,jdbcType=INTEGER}
  </update>

  <update id="updateByPrimaryKey" parameterType="com.estone.erp.publish.joom.model.JoomTemplate" >
    update joom_template
    set seller_id = #{sellerId,jdbcType=VARCHAR},
      `name` = #{name,jdbcType=VARCHAR},
      sku = #{sku,jdbcType=VARCHAR},
      description = #{description,jdbcType=VARCHAR},
      tags = #{tags,jdbcType=VARCHAR},
      sensitive_goods = #{sensitiveGoods,jdbcType=VARCHAR},
      price = #{price,jdbcType=DOUBLE},
      msrp = #{msrp,jdbcType=DOUBLE},
      inventory = #{inventory,jdbcType=INTEGER},
      brand = #{brand,jdbcType=VARCHAR},
      upc = #{upc,jdbcType=VARCHAR},
      landing_page_url = #{landingPageUrl,jdbcType=VARCHAR},
      content = #{content,jdbcType=VARCHAR},
      main_image = #{mainImage,jdbcType=VARCHAR},
      shipping = #{shipping,jdbcType=DOUBLE},
      shipping_time = #{shippingTime,jdbcType=VARCHAR},
      variations = #{variations,jdbcType=VARCHAR},
      extra_images = #{extraImages,jdbcType=VARCHAR},
      `status` = #{status,jdbcType=INTEGER},
      category_id = #{categoryId,jdbcType=INTEGER},
      is_multiple_attribute = #{isMultipleAttribute,jdbcType=BIT},
      is_lock = #{isLock,jdbcType=BIT},
      full_path_code = #{fullPathCode,jdbcType=VARCHAR},
      display_name = #{displayName,jdbcType=VARCHAR},
      creation_date = #{creationDate,jdbcType=TIMESTAMP},
      created_by = #{createdBy,jdbcType=VARCHAR},
      last_update_date = #{lastUpdateDate,jdbcType=TIMESTAMP},
      last_updated_by = #{lastUpdatedBy,jdbcType=VARCHAR},
      copy_count = #{copyCount,jdbcType=INTEGER},
      upload_count = #{uploadCount,jdbcType=INTEGER}
    where template_id = #{templateId,jdbcType=INTEGER}
  </update>
  <delete id="deleteParentsBySkuStatus">
    delete tem from joom_template tem
      left join joom_item item on tem.sku = item.parent_sku
    where tem.is_lock = 1 and item.sku_status in ('Stop','Archived');
  </delete>
</mapper>
package com.estone.erp.publish.smt.service;

import com.estone.erp.publish.smt.jobHandler.SmtViolationSkuBanJobHandler;
import com.estone.erp.publish.smt.service.SmtViolationPunishRecordService;
import com.xxl.job.core.biz.model.ReturnT;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.when;

/**
 * SMT违规处罚SKU禁售规则自动处理定时任务测试类
 * 
 * <AUTHOR>
 * @date 2025-07-22
 */
@RunWith(MockitoJUnitRunner.class)
public class SmtViolationSkuBanJobHandlerTest {

    @Mock
    private SmtViolationPunishRecordService smtViolationPunishRecordService;

    @InjectMocks
    private SmtViolationSkuBanJobHandler jobHandler;

    /**
     * 测试任务正常执行
     */
    @Test
    public void testRunSuccess() throws Exception {
        // 模拟服务返回成功结果
        String expectedResult = "违规处罚SKU禁售规则自动处理完成，共处理5个SKU，标记禁售10条记录";
        when(smtViolationPunishRecordService.processSkuBanByRules()).thenReturn(expectedResult);

        // 执行任务
        ReturnT<String> result = jobHandler.run("");

        // 验证结果
        assertEquals(ReturnT.SUCCESS_CODE, result.getCode());
    }

    /**
     * 测试任务执行异常
     */
    @Test
    public void testRunWithException() throws Exception {
        // 模拟服务抛出异常
        String errorMessage = "数据库连接失败";
        when(smtViolationPunishRecordService.processSkuBanByRules())
                .thenThrow(new RuntimeException(errorMessage));

        // 执行任务
        ReturnT<String> result = jobHandler.run("");

        // 验证结果
        assertEquals(ReturnT.FAIL_CODE, result.getCode());
        assertEquals("任务执行失败: " + errorMessage, result.getMsg());
    }

    /**
     * 测试无规则情况
     */
    @Test
    public void testRunWithNoRules() throws Exception {
        // 模拟服务返回无规则结果
        String expectedResult = "未找到启用状态的SKU禁售规则";
        when(smtViolationPunishRecordService.processSkuBanByRules()).thenReturn(expectedResult);

        // 执行任务
        ReturnT<String> result = jobHandler.run("");

        // 验证结果
        assertEquals(ReturnT.SUCCESS_CODE, result.getCode());
    }

    /**
     * 测试无候选记录情况
     */
    @Test
    public void testRunWithNoRecords() throws Exception {
        // 模拟服务返回无记录结果
        String expectedResult = "未找到符合条件的违规处罚记录";
        when(smtViolationPunishRecordService.processSkuBanByRules()).thenReturn(expectedResult);

        // 执行任务
        ReturnT<String> result = jobHandler.run("");

        // 验证结果
        assertEquals(ReturnT.SUCCESS_CODE, result.getCode());
    }
}

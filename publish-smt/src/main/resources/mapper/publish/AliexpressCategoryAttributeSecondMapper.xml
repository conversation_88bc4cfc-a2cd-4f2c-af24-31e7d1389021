<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.estone.erp.publish.smt.mapper.AliexpressCategoryAttributeSecondMapper" >
  <resultMap id="BaseResultMap" type="com.estone.erp.publish.smt.model.AliexpressCategoryAttributeSecond" >
    <id column="id" property="id" jdbcType="INTEGER" />
    <result column="category_id" property="categoryId" jdbcType="INTEGER" />
    <result column="attribute_id" property="attributeId" jdbcType="BIGINT" />
    <result column="attribute_name_zh" property="attributeNameZh" jdbcType="VARCHAR" />
    <result column="attribute_name_en" property="attributeNameEn" jdbcType="VARCHAR" />
    <result column="value_id" property="valueId" jdbcType="BIGINT" />
    <result column="value_name_zh" property="valueNameZh" jdbcType="VARCHAR" />
    <result column="value_name_en" property="valueNameEn" jdbcType="VARCHAR" />
    <result column="second_attribute_id" property="secondAttributeId" jdbcType="BIGINT" />
    <result column="second_attribute_name_zh" property="secondAttributeNameZh" jdbcType="VARCHAR" />
    <result column="second_attribute_name_en" property="secondAttributeNameEn" jdbcType="VARCHAR" />
    <result column="query_param" property="queryParam" jdbcType="VARCHAR" />
    <result column="required" property="required" jdbcType="BIT" />
    <result column="input_type" property="inputType" jdbcType="VARCHAR" />
    <result column="customized_name" property="customizedName" jdbcType="BIT" />
    <result column="visible" property="visible" jdbcType="BIT" />
    <result column="customized_pic" property="customizedPic" jdbcType="BIT" />
    <result column="key_attribute" property="keyAttribute" jdbcType="BIT" />
    <result column="attribute_show_type_value" property="attributeShowTypeValue" jdbcType="VARCHAR" />
    <result column="spec" property="spec" jdbcType="INTEGER" />
    <result column="features" property="features" jdbcType="VARCHAR" />
    <result column="support_enum_input" property="supportEnumInput" jdbcType="BIT" />
    <result column="sku" property="sku" jdbcType="BIT" />
    <result column="values_json" property="valuesJson" jdbcType="VARCHAR" />
    <result column="create_date" property="createDate" jdbcType="TIMESTAMP" />
    <result column="create_by" property="createBy" jdbcType="VARCHAR" />
    <result column="update_date" property="updateDate" jdbcType="TIMESTAMP" />
    <result column="update_by" property="updateBy" jdbcType="VARCHAR" />
  </resultMap>

  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>

  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>

  <sql id="Base_Column_List" >
    id, category_id, attribute_id, attribute_name_zh, attribute_name_en, value_id, value_name_zh, value_name_en, second_attribute_id, 
    second_attribute_name_zh, second_attribute_name_en, query_param, required, input_type, 
    customized_name, visible, customized_pic, key_attribute, attribute_show_type_value, 
    spec, features, support_enum_input, sku, values_json, create_date, create_by, update_date, update_by
  </sql>

  <select id="countByExample" parameterType="com.estone.erp.publish.smt.model.AliexpressCategoryAttributeSecondExample" resultType="java.lang.Integer" >
    select count(*) from aliexpress_category_attribute_second
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>

  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.estone.erp.publish.smt.model.AliexpressCategoryAttributeSecondExample" >
    select
    <choose>
      <when test="fields != null and fields != ''">
        ${fields}
      </when>
      <otherwise>
        <include refid="Base_Column_List" />
      </otherwise>
    </choose>
    from aliexpress_category_attribute_second
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
    <if test="limit != null" >
      <if test="offset != null" >
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null" >
        limit ${limit}
      </if>
    </if>
  </select>

  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    select 
    <include refid="Base_Column_List" />
    from aliexpress_category_attribute_second
    where id = #{id,jdbcType=INTEGER}
  </select>

  <select id="selectByCategoryId" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    select 
    <include refid="Base_Column_List" />
    from aliexpress_category_attribute_second
    where category_id = #{categoryId,jdbcType=INTEGER}
  </select>

  <select id="selectByAttributeIdAndValueId" resultMap="BaseResultMap" >
    select 
    <include refid="Base_Column_List" />
    from aliexpress_category_attribute_second
    where attribute_id = #{attributeId,jdbcType=BIGINT} and value_id = #{valueId,jdbcType=BIGINT}
  </select>

  <delete id="deleteByPrimaryKey" >
    delete from aliexpress_category_attribute_second
    where id IN 
    <foreach collection="list" item="listItem" open="(" close=")" separator="," >
      #{listItem}
    </foreach>
  </delete>

  <delete id="deleteByCategoryId" parameterType="java.lang.Integer" >
    delete from aliexpress_category_attribute_second
    where category_id = #{categoryId,jdbcType=INTEGER}
  </delete>

  <insert id="insert" parameterType="com.estone.erp.publish.smt.model.AliexpressCategoryAttributeSecond" >
    <selectKey resultType="java.lang.Integer" keyProperty="id" order="AFTER" >
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into aliexpress_category_attribute_second (category_id, attribute_id, attribute_name_zh, attribute_name_en, value_id, 
      value_name_zh, value_name_en, second_attribute_id, 
      second_attribute_name_zh, second_attribute_name_en, query_param, 
      required, input_type, customized_name, 
      visible, customized_pic, key_attribute, 
      attribute_show_type_value, spec, features, 
      support_enum_input, sku, values_json, 
      create_date, create_by, update_date, update_by)
    values (#{categoryId,jdbcType=INTEGER}, #{attributeId,jdbcType=BIGINT}, #{attributeNameZh,jdbcType=VARCHAR}, #{attributeNameEn,jdbcType=VARCHAR}, #{valueId,jdbcType=BIGINT}, 
      #{valueNameZh,jdbcType=VARCHAR}, #{valueNameEn,jdbcType=VARCHAR}, #{secondAttributeId,jdbcType=BIGINT}, 
      #{secondAttributeNameZh,jdbcType=VARCHAR}, #{secondAttributeNameEn,jdbcType=VARCHAR}, #{queryParam,jdbcType=VARCHAR}, 
      #{required,jdbcType=BIT}, #{inputType,jdbcType=VARCHAR}, #{customizedName,jdbcType=BIT}, 
      #{visible,jdbcType=BIT}, #{customizedPic,jdbcType=BIT}, #{keyAttribute,jdbcType=BIT}, 
      #{attributeShowTypeValue,jdbcType=VARCHAR}, #{spec,jdbcType=INTEGER}, #{features,jdbcType=VARCHAR}, 
      #{supportEnumInput,jdbcType=BIT}, #{sku,jdbcType=BIT}, #{valuesJson,jdbcType=VARCHAR}, 
      #{createDate,jdbcType=TIMESTAMP}, #{createBy,jdbcType=VARCHAR}, #{updateDate,jdbcType=TIMESTAMP}, #{updateBy,jdbcType=VARCHAR})
  </insert>

  <update id="updateByExampleSelective" parameterType="map" >
    update aliexpress_category_attribute_second
    <set >
      <if test="record.id != null" >
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.categoryId != null" >
        category_id = #{record.categoryId,jdbcType=INTEGER},
      </if>
      <if test="record.attributeId != null" >
        attribute_id = #{record.attributeId,jdbcType=BIGINT},
      </if>
      <if test="record.attributeNameZh != null" >
        attribute_name_zh = #{record.attributeNameZh,jdbcType=VARCHAR},
      </if>
      <if test="record.attributeNameEn != null" >
        attribute_name_en = #{record.attributeNameEn,jdbcType=VARCHAR},
      </if>
      <if test="record.valueId != null" >
        value_id = #{record.valueId,jdbcType=BIGINT},
      </if>
      <if test="record.valueNameZh != null" >
        value_name_zh = #{record.valueNameZh,jdbcType=VARCHAR},
      </if>
      <if test="record.valueNameEn != null" >
        value_name_en = #{record.valueNameEn,jdbcType=VARCHAR},
      </if>
      <if test="record.secondAttributeId != null" >
        second_attribute_id = #{record.secondAttributeId,jdbcType=BIGINT},
      </if>
      <if test="record.secondAttributeNameZh != null" >
        second_attribute_name_zh = #{record.secondAttributeNameZh,jdbcType=VARCHAR},
      </if>
      <if test="record.secondAttributeNameEn != null" >
        second_attribute_name_en = #{record.secondAttributeNameEn,jdbcType=VARCHAR},
      </if>
      <if test="record.queryParam != null" >
        query_param = #{record.queryParam,jdbcType=VARCHAR},
      </if>
      <if test="record.required != null" >
        required = #{record.required,jdbcType=BIT},
      </if>
      <if test="record.inputType != null" >
        input_type = #{record.inputType,jdbcType=VARCHAR},
      </if>
      <if test="record.customizedName != null" >
        customized_name = #{record.customizedName,jdbcType=BIT},
      </if>
      <if test="record.visible != null" >
        visible = #{record.visible,jdbcType=BIT},
      </if>
      <if test="record.customizedPic != null" >
        customized_pic = #{record.customizedPic,jdbcType=BIT},
      </if>
      <if test="record.keyAttribute != null" >
        key_attribute = #{record.keyAttribute,jdbcType=BIT},
      </if>
      <if test="record.attributeShowTypeValue != null" >
        attribute_show_type_value = #{record.attributeShowTypeValue,jdbcType=VARCHAR},
      </if>
      <if test="record.spec != null" >
        spec = #{record.spec,jdbcType=INTEGER},
      </if>
      <if test="record.features != null" >
        features = #{record.features,jdbcType=VARCHAR},
      </if>
      <if test="record.supportEnumInput != null" >
        support_enum_input = #{record.supportEnumInput,jdbcType=BIT},
      </if>
      <if test="record.sku != null" >
        sku = #{record.sku,jdbcType=BIT},
      </if>
      <if test="record.valuesJson != null" >
        values_json = #{record.valuesJson,jdbcType=VARCHAR},
      </if>
      <if test="record.createDate != null" >
        create_date = #{record.createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.createBy != null" >
        create_by = #{record.createBy,jdbcType=VARCHAR},
      </if>
      <if test="record.updateDate != null" >
        update_date = #{record.updateDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateBy != null" >
        update_by = #{record.updateBy,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>

  <update id="updateByPrimaryKeySelective" parameterType="com.estone.erp.publish.smt.model.AliexpressCategoryAttributeSecond" >
    update aliexpress_category_attribute_second
    <set >
      <if test="categoryId != null" >
        category_id = #{categoryId,jdbcType=INTEGER},
      </if>
      <if test="attributeId != null" >
        attribute_id = #{attributeId,jdbcType=BIGINT},
      </if>
      <if test="attributeNameZh != null" >
        attribute_name_zh = #{attributeNameZh,jdbcType=VARCHAR},
      </if>
      <if test="attributeNameEn != null" >
        attribute_name_en = #{attributeNameEn,jdbcType=VARCHAR},
      </if>
      <if test="valueId != null" >
        value_id = #{valueId,jdbcType=BIGINT},
      </if>
      <if test="valueNameZh != null" >
        value_name_zh = #{valueNameZh,jdbcType=VARCHAR},
      </if>
      <if test="valueNameEn != null" >
        value_name_en = #{valueNameEn,jdbcType=VARCHAR},
      </if>
      <if test="secondAttributeId != null" >
        second_attribute_id = #{secondAttributeId,jdbcType=BIGINT},
      </if>
      <if test="secondAttributeNameZh != null" >
        second_attribute_name_zh = #{secondAttributeNameZh,jdbcType=VARCHAR},
      </if>
      <if test="secondAttributeNameEn != null" >
        second_attribute_name_en = #{secondAttributeNameEn,jdbcType=VARCHAR},
      </if>
      <if test="queryParam != null" >
        query_param = #{queryParam,jdbcType=VARCHAR},
      </if>
      <if test="required != null" >
        required = #{required,jdbcType=BIT},
      </if>
      <if test="inputType != null" >
        input_type = #{inputType,jdbcType=VARCHAR},
      </if>
      <if test="customizedName != null" >
        customized_name = #{customizedName,jdbcType=BIT},
      </if>
      <if test="visible != null" >
        visible = #{visible,jdbcType=BIT},
      </if>
      <if test="customizedPic != null" >
        customized_pic = #{customizedPic,jdbcType=BIT},
      </if>
      <if test="keyAttribute != null" >
        key_attribute = #{keyAttribute,jdbcType=BIT},
      </if>
      <if test="attributeShowTypeValue != null" >
        attribute_show_type_value = #{attributeShowTypeValue,jdbcType=VARCHAR},
      </if>
      <if test="spec != null" >
        spec = #{spec,jdbcType=INTEGER},
      </if>
      <if test="features != null" >
        features = #{features,jdbcType=VARCHAR},
      </if>
      <if test="supportEnumInput != null" >
        support_enum_input = #{supportEnumInput,jdbcType=BIT},
      </if>
      <if test="sku != null" >
        sku = #{sku,jdbcType=BIT},
      </if>
      <if test="valuesJson != null" >
        values_json = #{valuesJson,jdbcType=VARCHAR},
      </if>
      <if test="createDate != null" >
        create_date = #{createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="createBy != null" >
        create_by = #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="updateDate != null" >
        update_date = #{updateDate,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null" >
        update_by = #{updateBy,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>

  <insert id="batchInsert" parameterType="java.util.List" >
    insert into aliexpress_category_attribute_second (category_id, attribute_id, attribute_name_zh, attribute_name_en, value_id, 
      value_name_zh, value_name_en, second_attribute_id, 
      second_attribute_name_zh, second_attribute_name_en, query_param, 
      required, input_type, customized_name, 
      visible, customized_pic, key_attribute, 
      attribute_show_type_value, spec, features, 
      support_enum_input, sku, values_json, 
      create_date, create_by, update_date, update_by)
    values
    <foreach collection="list" item="item" index="index" separator=",">
      (#{item.categoryId,jdbcType=INTEGER}, #{item.attributeId,jdbcType=BIGINT}, #{item.attributeNameZh,jdbcType=VARCHAR}, #{item.attributeNameEn,jdbcType=VARCHAR}, #{item.valueId,jdbcType=BIGINT}, 
      #{item.valueNameZh,jdbcType=VARCHAR}, #{item.valueNameEn,jdbcType=VARCHAR}, #{item.secondAttributeId,jdbcType=BIGINT}, 
      #{item.secondAttributeNameZh,jdbcType=VARCHAR}, #{item.secondAttributeNameEn,jdbcType=VARCHAR}, #{item.queryParam,jdbcType=VARCHAR}, 
      #{item.required,jdbcType=BIT}, #{item.inputType,jdbcType=VARCHAR}, #{item.customizedName,jdbcType=BIT}, 
      #{item.visible,jdbcType=BIT}, #{item.customizedPic,jdbcType=BIT}, #{item.keyAttribute,jdbcType=BIT}, 
      #{item.attributeShowTypeValue,jdbcType=VARCHAR}, #{item.spec,jdbcType=INTEGER}, #{item.features,jdbcType=VARCHAR}, 
      #{item.supportEnumInput,jdbcType=BIT}, #{item.sku,jdbcType=BIT}, #{item.valuesJson,jdbcType=VARCHAR}, 
      #{item.createDate,jdbcType=TIMESTAMP}, #{item.createBy,jdbcType=VARCHAR}, #{item.updateDate,jdbcType=TIMESTAMP}, #{item.updateBy,jdbcType=VARCHAR})
    </foreach>
  </insert>

  <update id="batchUpdate" parameterType="java.util.List" >
    <foreach collection="list" index="index" item="record" open="" separator=";" close=";">
      update aliexpress_category_attribute_second
      <set >
        <if test="record.categoryId != null" >
          category_id = #{record.categoryId,jdbcType=INTEGER},
        </if>
        <if test="record.attributeId != null" >
          attribute_id = #{record.attributeId,jdbcType=BIGINT},
        </if>
        <if test="record.attributeNameZh != null" >
          attribute_name_zh = #{record.attributeNameZh,jdbcType=VARCHAR},
        </if>
        <if test="record.attributeNameEn != null" >
          attribute_name_en = #{record.attributeNameEn,jdbcType=VARCHAR},
        </if>
        <if test="record.valueId != null" >
          value_id = #{record.valueId,jdbcType=BIGINT},
        </if>
        <if test="record.valueNameZh != null" >
          value_name_zh = #{record.valueNameZh,jdbcType=VARCHAR},
        </if>
        <if test="record.valueNameEn != null" >
          value_name_en = #{record.valueNameEn,jdbcType=VARCHAR},
        </if>
        <if test="record.secondAttributeId != null" >
          second_attribute_id = #{record.secondAttributeId,jdbcType=BIGINT},
        </if>
        <if test="record.secondAttributeNameZh != null" >
          second_attribute_name_zh = #{record.secondAttributeNameZh,jdbcType=VARCHAR},
        </if>
        <if test="record.secondAttributeNameEn != null" >
          second_attribute_name_en = #{record.secondAttributeNameEn,jdbcType=VARCHAR},
        </if>
        <if test="record.queryParam != null" >
          query_param = #{record.queryParam,jdbcType=VARCHAR},
        </if>
        <if test="record.required != null" >
          required = #{record.required,jdbcType=BIT},
        </if>
        <if test="record.inputType != null" >
          input_type = #{record.inputType,jdbcType=VARCHAR},
        </if>
        <if test="record.customizedName != null" >
          customized_name = #{record.customizedName,jdbcType=BIT},
        </if>
        <if test="record.visible != null" >
          visible = #{record.visible,jdbcType=BIT},
        </if>
        <if test="record.customizedPic != null" >
          customized_pic = #{record.customizedPic,jdbcType=BIT},
        </if>
        <if test="record.keyAttribute != null" >
          key_attribute = #{record.keyAttribute,jdbcType=BIT},
        </if>
        <if test="record.attributeShowTypeValue != null" >
          attribute_show_type_value = #{record.attributeShowTypeValue,jdbcType=VARCHAR},
        </if>
        <if test="record.spec != null" >
          spec = #{record.spec,jdbcType=INTEGER},
        </if>
        <if test="record.features != null" >
          features = #{record.features,jdbcType=VARCHAR},
        </if>
        <if test="record.supportEnumInput != null" >
          support_enum_input = #{record.supportEnumInput,jdbcType=BIT},
        </if>
        <if test="record.sku != null" >
          sku = #{record.sku,jdbcType=BIT},
        </if>
        <if test="record.valuesJson != null" >
          values_json = #{record.valuesJson,jdbcType=VARCHAR},
        </if>
        <if test="record.createDate != null" >
          create_date = #{record.createDate,jdbcType=TIMESTAMP},
        </if>
        <if test="record.createBy != null" >
          create_by = #{record.createBy,jdbcType=VARCHAR},
        </if>
        <if test="record.updateDate != null" >
          update_date = #{record.updateDate,jdbcType=TIMESTAMP},
        </if>
        <if test="record.updateBy != null" >
          update_by = #{record.updateBy,jdbcType=VARCHAR},
        </if>
      </set>
      where id = #{record.id,jdbcType=INTEGER}
    </foreach>
  </update>
</mapper>

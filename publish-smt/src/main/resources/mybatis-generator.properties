#Mybatis Generator configuration
projectModel=D:/workSpace/publish/publish-smt/src/main/java
packageModel=com.estone.erp.publish.smt.model
projectMapper=D:/workSpace/publish/publish-smt/src/main/resources
packageMapper=mapper/publish
projectDao=D:/workSpace/publish/publish-smt/src/main/java
packageDao=com.estone.erp.publish.smt.mapper
classPath= D:/soft/maven/reposity/mysql/mysql-connector-java/5.1.25/mysql-connector-java-5.1.25.jar
jdbc_driver = com.mysql.jdbc.Driver
jdbc_url=******************************************************************************************************************************
jdbc_user=root
jdbc_password=!QAZxsw2
tableName=aliexpress_spu_category_forbid_publish
domainObjectName=AliexpressSpuCategoryForbidPublish
pkColumn=id
sqlStatement=MySql
identity=true
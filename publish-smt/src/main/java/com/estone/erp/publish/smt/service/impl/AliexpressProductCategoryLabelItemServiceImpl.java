package com.estone.erp.publish.smt.service.impl;

import com.estone.erp.common.model.api.CQuery;
import com.estone.erp.common.model.api.CQueryResult;
import com.estone.erp.publish.smt.mapper.AliexpressProductCategoryLabelItemMapper;
import com.estone.erp.publish.smt.model.AliexpressProductCategoryLabelItem;
import com.estone.erp.publish.smt.model.AliexpressProductCategoryLabelItemCriteria;
import com.estone.erp.publish.smt.model.AliexpressProductCategoryLabelItemExample;
import com.estone.erp.publish.smt.service.AliexpressProductCategoryLabelItemService;
import java.sql.Timestamp;
import java.util.List;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

/**
 * <AUTHOR> aliexpress_product_category_label_item
 * 2023-09-11 16:09:08
 */
@Service("aliexpressProductCategoryLabelItemService")
@Slf4j
public class AliexpressProductCategoryLabelItemServiceImpl implements AliexpressProductCategoryLabelItemService {
    @Resource
    private AliexpressProductCategoryLabelItemMapper aliexpressProductCategoryLabelItemMapper;

    @Override
    public int countByExample(AliexpressProductCategoryLabelItemExample example) {
        Assert.notNull(example, "example is null!");
        return aliexpressProductCategoryLabelItemMapper.countByExample(example);
    }

    @Override
    public CQueryResult<AliexpressProductCategoryLabelItem> search(CQuery<AliexpressProductCategoryLabelItemCriteria> cquery) {
        Assert.notNull(cquery, "cquery is null!");
        AliexpressProductCategoryLabelItemCriteria query = cquery.getSearch();
        AliexpressProductCategoryLabelItemExample example = query.getExample();
        long total = 0;
        int totalPages = 0;
        // 是否分页
        if (cquery.isPageReqired()) {
            total = aliexpressProductCategoryLabelItemMapper.countByExample(example);
            totalPages = (int) Math.ceil((double) total / cquery.getLimit());
            example.setLimit(cquery.getLimit());
            example.setOffset(cquery.getOffset());
        }
        List<AliexpressProductCategoryLabelItem> aliexpressProductCategoryLabelItems = aliexpressProductCategoryLabelItemMapper.selectByExample(example);
        // 组装结果
        CQueryResult<AliexpressProductCategoryLabelItem> result = new CQueryResult<>();
        result.setTotal(total);
        result.setTotalPages(totalPages);
        result.setRows(aliexpressProductCategoryLabelItems);
        return result;
    }

    @Override
    public AliexpressProductCategoryLabelItem selectByPrimaryKey(Integer id) {
        Assert.notNull(id, "id is null!");
        return aliexpressProductCategoryLabelItemMapper.selectByPrimaryKey(id);
    }

    @Override
    public List<AliexpressProductCategoryLabelItem> selectByExample(AliexpressProductCategoryLabelItemExample example) {
        Assert.notNull(example, "example is null!");
        return aliexpressProductCategoryLabelItemMapper.selectByExample(example);
    }

    @Override
    public int insert(AliexpressProductCategoryLabelItem record) {
        Assert.notNull(record, "record is null!");
        // 默认加时间和人
        record.setLastUpdateDate(new Timestamp(System.currentTimeMillis()));
        return aliexpressProductCategoryLabelItemMapper.insert(record);
    }

    @Override
    public int updateByPrimaryKeySelective(AliexpressProductCategoryLabelItem record) {
        Assert.notNull(record, "record is null!");
        // 默认加修改时间和修改人
        record.setLastUpdateDate(new Timestamp(System.currentTimeMillis()));
        return aliexpressProductCategoryLabelItemMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public int updateByExampleSelective(AliexpressProductCategoryLabelItem record, AliexpressProductCategoryLabelItemExample example) {
        Assert.notNull(record, "record is null!");
        // 默认加修改时间和修改人
        record.setLastUpdateDate(new Timestamp(System.currentTimeMillis()));
        return aliexpressProductCategoryLabelItemMapper.updateByExampleSelective(record, example);
    }

    @Override
    public int deleteByPrimaryKey(List<Integer> ids) {
        Assert.notNull(ids, "ids is null!");
        return aliexpressProductCategoryLabelItemMapper.deleteByPrimaryKey(ids);
    }
}
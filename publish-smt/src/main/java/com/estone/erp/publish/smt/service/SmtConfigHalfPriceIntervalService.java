package com.estone.erp.publish.smt.service;

import com.estone.erp.common.model.api.CQuery;
import com.estone.erp.common.model.api.CQueryResult;
import com.estone.erp.publish.smt.model.SmtConfigHalfPriceInterval;
import com.estone.erp.publish.smt.model.SmtConfigHalfPriceIntervalCriteria;
import com.estone.erp.publish.smt.model.SmtConfigHalfPriceIntervalExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR> smt_config_half_price_interval
 * 2024-01-17 15:49:58
 */
public interface SmtConfigHalfPriceIntervalService {
    int countByExample(SmtConfigHalfPriceIntervalExample example);

    CQueryResult<SmtConfigHalfPriceInterval> search(CQuery<SmtConfigHalfPriceIntervalCriteria> cquery);

    List<SmtConfigHalfPriceInterval> selectByExample(SmtConfigHalfPriceIntervalExample example);

    SmtConfigHalfPriceInterval selectByPrimaryKey(Integer id);

    int insert(SmtConfigHalfPriceInterval record);

    int updateByPrimaryKeySelective(SmtConfigHalfPriceInterval record);

    int updateByExampleSelective(SmtConfigHalfPriceInterval record, SmtConfigHalfPriceIntervalExample example);

    int deleteByPrimaryKey(List<Integer> ids);

    int deleteByAccount(String account);
}
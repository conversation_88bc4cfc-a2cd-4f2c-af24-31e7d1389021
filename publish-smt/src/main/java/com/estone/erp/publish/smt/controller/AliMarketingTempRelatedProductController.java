package com.estone.erp.publish.smt.controller;

import com.alibaba.fastjson.TypeReference;
import com.estone.erp.common.model.api.ApiRequestParam;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.model.api.CQuery;
import com.estone.erp.common.model.api.CQueryResult;
import com.estone.erp.common.util.Asserts;
import com.estone.erp.publish.common.ErrorCode;
import com.estone.erp.publish.smt.model.AliMarketingTempRelatedProduct;
import com.estone.erp.publish.smt.model.AliMarketingTempRelatedProductCriteria;
import com.estone.erp.publish.smt.service.AliMarketingTempRelatedProductService;
import javax.annotation.Resource;
import org.apache.commons.lang.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR> ali_marketing_temp_related_product
 * 2020-10-16 18:32:07
 */
@RestController
@RequestMapping("aliMarketingTempRelatedProduct")
public class AliMarketingTempRelatedProductController {
    @Resource
    private AliMarketingTempRelatedProductService aliMarketingTempRelatedProductService;

    @PostMapping
    public ApiResult<?> postAliMarketingTempRelatedProduct(@RequestBody(required = true) ApiRequestParam<String> requestParam) {
        String method = requestParam.getMethod();
        if (StringUtils.isNotBlank(method)) {
            switch (method) {
                case "searchAliMarketingTempRelatedProduct": // 查询列表
                    CQuery<AliMarketingTempRelatedProductCriteria> cquery = requestParam.getArgsValue(new TypeReference<CQuery<AliMarketingTempRelatedProductCriteria>>() {});
                    Asserts.isTrue(cquery != null, ErrorCode.PARAM_EMPTY_ERROR);
                    CQueryResult<AliMarketingTempRelatedProduct> results = aliMarketingTempRelatedProductService.search(cquery);
                    return results;
                case "addAliMarketingTempRelatedProduct": // 添加
                    AliMarketingTempRelatedProduct aliMarketingTempRelatedProduct = requestParam.getArgsValue(new TypeReference<AliMarketingTempRelatedProduct>() {});
                    aliMarketingTempRelatedProductService.insert(aliMarketingTempRelatedProduct);
                    return ApiResult.newSuccess(aliMarketingTempRelatedProduct);
                }
        }
        return ApiResult.newSuccess();
    }

    @GetMapping(value = "/{id}")
    public ApiResult<?> getAliMarketingTempRelatedProduct(@PathVariable(value = "id", required = true) Integer id) {
        AliMarketingTempRelatedProduct aliMarketingTempRelatedProduct = aliMarketingTempRelatedProductService.selectByPrimaryKey(id);
        return ApiResult.newSuccess(aliMarketingTempRelatedProduct);
    }

    @PutMapping(value = "/{id}")
    public ApiResult<?> putAliMarketingTempRelatedProduct(@PathVariable(value = "id", required = true) Integer id, @RequestBody ApiRequestParam<String> requestParam) {
        String method = requestParam.getMethod();
        if (StringUtils.isNotBlank(method)) {
            switch (method) {
                case "updateAliMarketingTempRelatedProduct": // 单个修改
                    AliMarketingTempRelatedProduct aliMarketingTempRelatedProduct = requestParam.getArgsValue(new TypeReference<AliMarketingTempRelatedProduct>() {});
                    aliMarketingTempRelatedProductService.updateByPrimaryKeySelective(aliMarketingTempRelatedProduct);
                    return ApiResult.newSuccess(aliMarketingTempRelatedProduct);
                }
        }
        return ApiResult.newSuccess();
    }
}
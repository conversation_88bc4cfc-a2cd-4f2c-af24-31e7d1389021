package com.estone.erp.publish.smt.jobHandler;

import com.estone.erp.common.jobHandler.AbstractJobHandler;
import com.estone.erp.common.util.SpringUtils;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.common.executors.AliexpressExecutors;
import com.estone.erp.publish.common.json.ResponseJson;
import com.estone.erp.publish.elasticsearch2.model.EsAliexpressProductListing;
import com.estone.erp.publish.elasticsearch2.model.beanrequest.EsAliexpressProductListingRequest;
import com.estone.erp.publish.elasticsearch2.service.EsAliexpressProductListingService;
import com.estone.erp.publish.smt.call.direct.CategoryOpenCall;
import com.estone.erp.publish.smt.call.direct.EditCategoryAttributesOpenCall;
import com.estone.erp.publish.smt.call.direct.SynchItemOpenCall;
import com.estone.erp.publish.smt.model.AliexpressCategory;
import com.estone.erp.publish.smt.model.AliexpressEsExtend;
import com.estone.erp.publish.smt.service.AliexpressCategoryService;
import com.estone.erp.publish.smt.service.AliexpressEsExtendService;
import com.estone.erp.publish.smt.util.AliexpressBrandUtils;
import com.estone.erp.publish.smt.util.AliexpressCategoryUtils;
import com.estone.erp.publish.system.account.AccountUtils;
import com.estone.erp.publish.system.account.SaleAccountAndBusinessResponse;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/** 修改smt省份
 * <AUTHOR>
 * @description:
 * @date 2019/11/3010:17
 */
@Component
@Slf4j
public class UpdateSmtProvinceJobHandler extends AbstractJobHandler {

    private AliexpressCategoryService aliexpressCategoryService = SpringUtils.getBean(AliexpressCategoryService.class);

    private EsAliexpressProductListingService esAliexpressProductListingService = SpringUtils.getBean(EsAliexpressProductListingService.class);

    private AliexpressEsExtendService aliexpressEsExtendService = SpringUtils.getBean(AliexpressEsExtendService.class);

    public UpdateSmtProvinceJobHandler() {
        super("UpdateSmtProvinceJobHandler");
    }

    @Override
    @XxlJob("UpdateSmtProvinceJobHandler")
    public ReturnT<String> run(String param) throws Exception {
        log.warn("############修改smt省份");
        EsAliexpressProductListingRequest productListingRequest = new EsAliexpressProductListingRequest();
        productListingRequest.setIsCnProvinceCategory(true);
        productListingRequest.setCnProvince("无省份");
        productListingRequest.setQueryFields(new String[]{"id", "productId", "aliexpressAccountNumber", "categoryId"});

        //设置账号
        if(StringUtils.isNotBlank(param)){
            productListingRequest.setAliexpressAccountNumber(param);
        }

        List<EsAliexpressProductListing> esAliexpressProductListing = esAliexpressProductListingService.getEsAliexpressProductListing(productListingRequest);
        if(CollectionUtils.isEmpty(esAliexpressProductListing)){
            log.warn("没有数据需要修改");
            return ReturnT.SUCCESS;
        }

        Map<String, SaleAccountAndBusinessResponse> accountMap = new HashMap<>();
        Set<String> collect = esAliexpressProductListing.stream().map(t -> t.getAliexpressAccountNumber()).collect(Collectors.toSet());
        for (String aliexpressAccountNumber : collect) {
            SaleAccountAndBusinessResponse saleAccountAndBusinessResponse = accountMap.get(aliexpressAccountNumber);
            if(saleAccountAndBusinessResponse == null){
                saleAccountAndBusinessResponse = AccountUtils
                        .getSaleAccountByAccountNumber(SaleChannel.CHANNEL_SMT, aliexpressAccountNumber);
                accountMap.put(aliexpressAccountNumber, saleAccountAndBusinessResponse);
            }
        }

        //通过分类分组
        Map<Integer, List<EsAliexpressProductListing>> categoryIdMap = esAliexpressProductListing.stream().collect(Collectors.groupingBy(t -> t.getCategoryId()));
        for (Map.Entry<Integer, List<EsAliexpressProductListing>> integerListEntry : categoryIdMap.entrySet()) {
            Integer key = integerListEntry.getKey();
            List<EsAliexpressProductListing> valueList = integerListEntry.getValue();

            AliexpressCategory aliexpressCategory = aliexpressCategoryService.selectByCategoryId(key);
            String provinceAttributes = aliexpressCategory.getProvinceAttributes();

            //省份数据本来就依赖 本地类目判断，确定有值
            String childAttributesJson = aliexpressCategory.getChildAttributesJson();

            if(StringUtils.isBlank(provinceAttributes)){
                EsAliexpressProductListing aliexpressProductListing = valueList.get(0);
                String aliexpressAccountNumber = aliexpressProductListing.getAliexpressAccountNumber();
                SaleAccountAndBusinessResponse saleAccountAndBusinessResponse = accountMap.get(aliexpressAccountNumber);

                //接口重新获取一次
                CategoryOpenCall call = new CategoryOpenCall();
                String provinceCategoryAttributes = call.getCategoryAttributes(saleAccountAndBusinessResponse, key.toString(), "219=**********");
                provinceAttributes = AliexpressCategoryUtils.parseCategoryAttributes(provinceCategoryAttributes);
            }

            if(StringUtils.isBlank(provinceAttributes)){
                XxlJobLogger.log("分类省份属性为空 类目id" + key);
                continue;
            }

            //省份属性
            String provinceAttributesInterface = provinceAttributes;

            //通过产品id分组
            Map<Long, List<EsAliexpressProductListing>> productIdMap = valueList.stream().collect(Collectors.groupingBy(t -> t.getProductId()));
            for (Map.Entry<Long, List<EsAliexpressProductListing>> longListEntry : productIdMap.entrySet()) {
                AliexpressExecutors.smtProvince(()->{
                    EsAliexpressProductListing productListing = longListEntry.getValue().get(0);
                    String aliexpressAccountNumber = productListing.getAliexpressAccountNumber();
                    Long productId = productListing.getProductId();
                    AliexpressEsExtend aliexpressEsExtend = aliexpressEsExtendService.selectByAccountandProductId(aliexpressAccountNumber, productId);
                    String aeopAeProductPropertysJson = aliexpressEsExtend.getAeopAeProductPropertysJson();

                    SaleAccountAndBusinessResponse saleAccountAndBusinessResponse = accountMap.get(aliexpressAccountNumber);

                    //根据省份优先级 匹配省份属性
                    String addOrigin = AliexpressBrandUtils.addOrigin(aeopAeProductPropertysJson, childAttributesJson, provinceAttributesInterface, aliexpressCategory);

                    EditCategoryAttributesOpenCall editCall = new EditCategoryAttributesOpenCall();
                    ResponseJson responseJson = editCall
                            .editCategoryAttributes(saleAccountAndBusinessResponse,
                                    String.valueOf(productId), addOrigin);
                    //请求成功后，同步产品id
                    if(responseJson.isSuccess()){
                        SynchItemOpenCall synchItemCall = new SynchItemOpenCall();
                        synchItemCall.syncAliexpressProductInfo(saleAccountAndBusinessResponse, productId);
                    }else{
                        XxlJobLogger.log(aliexpressAccountNumber + ":" + productId + "修改失败:" + responseJson.getMessage());
                    }
                });
            }
        }
        return ReturnT.SUCCESS;
    }
}

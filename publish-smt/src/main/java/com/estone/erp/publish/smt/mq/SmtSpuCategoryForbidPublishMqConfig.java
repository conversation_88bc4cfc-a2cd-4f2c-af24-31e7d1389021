package com.estone.erp.publish.smt.mq;

import com.estone.erp.common.mq.PublishQueues;
import com.estone.erp.common.mq.RabbitMqManualFactory;
import com.estone.erp.common.mq.RabbitMqVirtualHosts;
import com.estone.erp.common.mq.model.VhBinding;
import com.estone.erp.common.mq.model.VhQueue;
import com.estone.erp.publish.mq.PublishMqConfig;
import lombok.Data;
import org.springframework.amqp.core.AcknowledgeMode;
import org.springframework.amqp.rabbit.connection.ConnectionFactory;
import org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * @Author：gt
 * @Date：2024/9/10 17:49
 */
@Configuration
@ConfigurationProperties(prefix = "yml-config")
@Data
public class SmtSpuCategoryForbidPublishMqConfig {
    private int spuCategoryForbidPublishMqConsumers;
    private int spuCategoryForbidPublishMqPrefetchCount;
    private boolean spuCategoryForbidPublishMqListener;

    @Bean
    public VhQueue spuCategoryForbidPublishqueue() {
        // 队列持久化
        return new VhQueue(RabbitMqVirtualHosts.PUBLISH_SMT_VIRTUAL_HOST, PublishQueues.SMT_SPU_CATEGORY_FORBID_PUBLISH_QUEUE, true, false, false, null);
    }

    @Bean
    public SmtSpuCategoryForbidPublishMqListener smtSpuCategoryForbidPublishMqListener(){
        return new SmtSpuCategoryForbidPublishMqListener();
    }

    @Bean
    public VhBinding spuCategoryForbidPublishBinding() {
        return new VhBinding(RabbitMqVirtualHosts.PUBLISH_SMT_VIRTUAL_HOST, PublishQueues.SMT_SPU_CATEGORY_FORBID_PUBLISH_QUEUE, VhBinding.DestinationType.QUEUE, PublishMqConfig.SMT_API_DIRECT_EXCHANGE, PublishQueues.SMT_SPU_CATEGORY_FORBID_PUBLISH_KEY, null);
    }

    @Bean
    public SimpleMessageListenerContainer smtSpuCategoryForbidPublishMqListenerContainer(SmtSpuCategoryForbidPublishMqListener smtSpuCategoryForbidPublishMqListener,
                                                                         RabbitMqManualFactory rabbitMqManualFactory) {
        ConnectionFactory connectionFactory = rabbitMqManualFactory.customerConnectionFactory(RabbitMqVirtualHosts.PUBLISH_SMT_VIRTUAL_HOST);
        SimpleMessageListenerContainer container = new SimpleMessageListenerContainer(connectionFactory);
        if (spuCategoryForbidPublishMqListener) {
            container.setQueueNames(PublishQueues.SMT_SPU_CATEGORY_FORBID_PUBLISH_QUEUE);
            container.setExposeListenerChannel(true);
            container.setPrefetchCount(spuCategoryForbidPublishMqPrefetchCount);// 设置每个消费者获取的最大的消息数量
            container.setConcurrentConsumers(spuCategoryForbidPublishMqConsumers);// 消费者个数
            container.setAcknowledgeMode(AcknowledgeMode.MANUAL);// 设置确认模式为手工确认
            container.setMessageListener(smtSpuCategoryForbidPublishMqListener);// 监听处理类
        }
        return container;
    }
}

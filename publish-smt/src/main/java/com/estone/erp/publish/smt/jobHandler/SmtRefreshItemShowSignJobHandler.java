package com.estone.erp.publish.smt.jobHandler;

import com.alibaba.fastjson.JSON;
import com.estone.erp.common.jobHandler.AbstractJobHandler;
import com.estone.erp.common.util.PublishRedisClusterUtils;
import com.estone.erp.common.util.SpringUtils;
import com.estone.erp.publish.common.util.DateUtils;
import com.estone.erp.publish.common.util.PagingUtils;
import com.estone.erp.publish.elasticsearch2.model.EsAliexpressProductListing;
import com.estone.erp.publish.elasticsearch2.model.beanrequest.EsAliexpressProductListingRequest;
import com.estone.erp.publish.elasticsearch2.service.EsAliexpressProductListingService;
import com.estone.erp.publish.smt.enums.ItemShowEnum;
import com.estone.erp.publish.smt.enums.ProductStatusTypeEnum;
import com.estone.erp.publish.smt.model.AliexpressConfig;
import com.estone.erp.publish.smt.model.AliexpressConfigExample;
import com.estone.erp.publish.smt.service.AliexpressConfigService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;


/**
 * 每天刷新商品表现不佳的产品 记录标签
 * <AUTHOR>
 * @description:
 * @date 2019/11/3010:17
 */
@Component
@Slf4j
public class SmtRefreshItemShowSignJobHandler extends AbstractJobHandler {

    private EsAliexpressProductListingService esAliexpressProductListingService = SpringUtils.getBean(EsAliexpressProductListingService.class);

    private AliexpressConfigService aliexpressConfigService = SpringUtils.getBean(AliexpressConfigService.class);

    //分页数量
    public static final Integer pageNum = 500;

    public SmtRefreshItemShowSignJobHandler() {
        super(SmtRefreshItemShowSignJobHandler.class.getName());
    }

    //大数据刷新时间，是昨天的日期
    private static String ads180dKey = "ads_publish_listing_sales_180d";
    //标记
    private static String ads180dSignKey = "ads_publish_listing_sales_180d_sign";

    @Override
    @XxlJob("SmtRefreshItemShowSignJobHandler")
    public ReturnT<String> run(String param) throws Exception {
        XxlJobLogger.log("SmtRefreshItemShowSignJobHandler begin");
        if(StringUtils.isNotBlank(param)){
            //为了重刷数据
            PublishRedisClusterUtils.set(ads180dSignKey, "error");
        }

        //昨天
        Date fromDate = DateUtils.getNewDateBeforeDay(1);
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        String yesterdayDate = dateFormat.format(fromDate);

        //标记时间
        String signDate = PublishRedisClusterUtils.get(ads180dSignKey);
        if(StringUtils.isNotBlank(signDate) && StringUtils.equalsIgnoreCase(yesterdayDate, signDate)){
            String appendLogPattern = "SmtRefreshItemShowSignJobHandler end 已经跑过标记任务了" + signDate;
            XxlJobLogger.log(appendLogPattern);
            log.info(appendLogPattern);
            return ReturnT.FAIL;
        }

        //存的是昨天的日期
        String ads180dDate = PublishRedisClusterUtils.get(ads180dKey);
        if(StringUtils.isBlank(ads180dDate) || !StringUtils.equalsIgnoreCase(yesterdayDate, ads180dDate)){
            String appendLogPattern = "SmtRefreshItemShowSignJobHandler end 大数据任务没有执行完成" + ads180dDate;
            XxlJobLogger.log(appendLogPattern);
            log.info(appendLogPattern);
            return ReturnT.FAIL;
        }

        //获取店铺配置账号
        List<AliexpressConfig> aliexpressConfigs = aliexpressConfigService.selectByExample(new AliexpressConfigExample());
        List<String> accountList = aliexpressConfigs.stream().map(t -> t.getAccount()).collect(Collectors.toList());

        //账号分页list
        List<List<String>> accountPagingList = PagingUtils.newPagingList(accountList, 10);

        long b = System.currentTimeMillis();

        try {
            //执行的时候设置好value值
            PublishRedisClusterUtils.set(ads180dSignKey, yesterdayDate);
            log.info("SmtRefreshItemShowSignJobHandler 标记redis标签" + ads180dSignKey + ":" + yesterdayDate);

            for (List<String> accounts : accountPagingList) {
                //刷新之前需要查下 有标签但是也存在180天销量，去除标签
                EsAliexpressProductListingRequest listingRequest = new EsAliexpressProductListingRequest();
                String join = StringUtils.join(accounts, ",");
                listingRequest.setAliexpressAccountNumber(join);
                listingRequest.setProductStatusType(ProductStatusTypeEnum.onSelling.getCode());
                listingRequest.setItemShow(ItemShowEnum.low.getCode());
                listingRequest.setFrom_order_last_180d_count(1);
                listingRequest.setQueryFields(new String[]{"id","productId"}); //只需要产品id
                //产品在线，商品被标记为商品竞争力不佳，但180天又有销量，需要去除标签
                List<EsAliexpressProductListing> removeSignList = esAliexpressProductListingService.getEsAliexpressProductListing(listingRequest);
                if(CollectionUtils.isNotEmpty(removeSignList)){
                    XxlJobLogger.log("SmtRefreshItemShowSignJobHandler 去除标签数量" + removeSignList.size() + "店铺：" + join);
//                    Set<Long> productIdSet = removeSignList.stream().map(t -> t.getProductId()).collect(Collectors.toSet());
//                    EsAliexpressProductListingRequest removeRequest = new EsAliexpressProductListingRequest();
//                    removeRequest.setAliexpressAccountNumber(StringUtils.join(accounts, ","));
//                    removeRequest.setProductIdList(new ArrayList<>(productIdSet));
//                    removeRequest.setQueryFields(null); //全字段
//                    List<EsAliexpressProductListing> esAliexpressProductListing = esAliexpressProductListingService.getEsAliexpressProductListing(removeRequest);
//                    //标签置空
//                    esAliexpressProductListing.forEach(t->t.setItemShow(null));
//                    List<List<EsAliexpressProductListing>> removelists = PagingUtils.newPagingList(esAliexpressProductListing, pageNum);
//                    removelists.forEach(t->esAliexpressProductListingService.saveAll(t));

                    //需要把标签置空
                    for (EsAliexpressProductListing aliexpressProductListing : removeSignList) {
                        esAliexpressProductListingService.updateRequest("{\"itemShow\":null}", aliexpressProductListing.getId());
                    }
                }

                //上架时间超过90天
                Date nowDate = new Date();
                int days = 90;
                //上架时间超过90天，并且没有180天销量的产品
                Date beforeDate = DateUtils.getBeforeDate(nowDate, days);
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                String toGmtDate = sdf.format(beforeDate);

                //需要排除的产品id
                List<Long> excludeProductId = new ArrayList<>();

                //先查询 状态在线，上架时间超过90天，并且有销量的产品id 需要排除掉（因为多属性有的子sku没有销量，但是产品是一个整体）
                EsAliexpressProductListingRequest saleRequest = new EsAliexpressProductListingRequest();
                saleRequest.setAliexpressAccountNumber(join);
                saleRequest.setProductStatusType(ProductStatusTypeEnum.onSelling.getCode());
                saleRequest.setToGmtCreateDate(toGmtDate);
                saleRequest.setFrom_order_last_180d_count(1);
                saleRequest.setQueryFields(new String[]{"productId"});
                List<EsAliexpressProductListing> esAliexpressProductListing = esAliexpressProductListingService.getEsAliexpressProductListing(saleRequest);
                if(CollectionUtils.isNotEmpty(esAliexpressProductListing)){
                    Set<Long> collect = esAliexpressProductListing.stream().map(t -> t.getProductId()).collect(Collectors.toSet());
                    excludeProductId = new ArrayList<>(collect);
                }

                List<EsAliexpressProductListing> allList = new ArrayList<>();

                long begin = System.currentTimeMillis();
                EsAliexpressProductListingRequest request = new EsAliexpressProductListingRequest();
                request.setAliexpressAccountNumber(join);
                request.setProductStatusType(ProductStatusTypeEnum.onSelling.getCode());
                request.setToGmtCreateDate(toGmtDate);
                request.setTo_order_last_180d_count(0);
                request.setNotInItemShow(ItemShowEnum.low.getCode());
                if(CollectionUtils.isNotEmpty(excludeProductId)){
                    request.setNotInProductIdList(excludeProductId);
                }
                request.setQueryFields(new String[]{"id"});//需要查询全字段 保存使用
                List<EsAliexpressProductListing> noSaleList = esAliexpressProductListingService.getEsAliexpressProductListing(request);
                if(CollectionUtils.isNotEmpty(noSaleList)){
                    allList.addAll(noSaleList);
                }
                long end = System.currentTimeMillis();
                XxlJobLogger.log("SmtRefreshItemShowSignJobHandler 查询180天销量为0 数量:{},耗时:{}, 店铺:{}", noSaleList.size(), (end -begin), join);
                log.info("SmtRefreshItemShowSignJobHandler 查询180天销量为0 数量:{},耗时:{}, 店铺:{}", noSaleList.size(), (end -begin), join);
                begin = System.currentTimeMillis();
                //一直未更新的数据
                request.setTo_order_last_180d_count(null);
                request.setOrder_last_180d_countIsNull(true);
                List<EsAliexpressProductListing> noUpdateList = esAliexpressProductListingService.getEsAliexpressProductListing(request);
                if(CollectionUtils.isNotEmpty(noUpdateList)){
                    allList.addAll(noUpdateList);
                }
                end = System.currentTimeMillis();
                XxlJobLogger.log("SmtRefreshItemShowSignJobHandler 查询180天销量不存在数据 数量:{},耗时:{}, 店铺:{}", noUpdateList.size(), (end -begin), join);
                log.info("SmtRefreshItemShowSignJobHandler 查询180天销量不存在数据 数量:{},耗时:{}, 店铺:{}", noUpdateList.size(), (end -begin), join);
                begin = System.currentTimeMillis();
                if(CollectionUtils.isNotEmpty(allList)){
                    allList.forEach(t->t.setItemShow(ItemShowEnum.low.getCode()));
                    List<List<EsAliexpressProductListing>> lists = PagingUtils.newPagingList(allList, pageNum);
                    for (List<EsAliexpressProductListing> list : lists) {
                        for (EsAliexpressProductListing aliexpressProductListing : list) {
                            esAliexpressProductListingService.updateRequest(aliexpressProductListing);
                        }
                    }
//                    lists.forEach(t->esAliexpressProductListingService.saveAll(t));
                }
                end = System.currentTimeMillis();
                XxlJobLogger.log("SmtRefreshItemShowSignJobHandler 保存数据 数量:{},耗时:{}, 店铺:{}", allList.size(), (end -begin), join);
                log.info("SmtRefreshItemShowSignJobHandler 查询180天销量不存在数据 数量:{},耗时:{}, 店铺:{}", noUpdateList.size(), (end -begin), join);
            }
        } catch (Exception e) {
            //异常设置error
            PublishRedisClusterUtils.set(ads180dSignKey, "error");
            log.error(e.getMessage(), e);
            XxlJobLogger.log(e.getMessage(), e);
            return ReturnT.FAIL;
        }
        long e = System.currentTimeMillis();
        XxlJobLogger.log("SmtRefreshItemShowSignJobHandler end");
        log.info("SmtRefreshItemShowSignJobHandler 耗时" + (e - b));
        return ReturnT.SUCCESS;
    }
}

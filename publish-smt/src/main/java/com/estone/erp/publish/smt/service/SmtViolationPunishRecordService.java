package com.estone.erp.publish.smt.service;

import com.estone.erp.common.model.api.CQuery;
import com.estone.erp.common.model.api.CQueryResult;
import com.estone.erp.publish.smt.model.SmtViolationPunishRecord;
import com.estone.erp.publish.smt.model.SmtViolationPunishRecordCriteria;
import com.estone.erp.publish.smt.model.SmtViolationPunishRecordExample;
import com.estone.erp.publish.smt.model.dto.DataCleanResultDto;
import com.estone.erp.publish.smt.model.dto.SmtBatchSetBanStatusDTO;

import java.util.List;

/**
 * <AUTHOR>
 * 2024-05-24 11:25:09
 */
public interface SmtViolationPunishRecordService {
    int countByExample(SmtViolationPunishRecordExample example);

    CQueryResult<SmtViolationPunishRecord> search(CQuery<SmtViolationPunishRecordCriteria> cquery);

    List<SmtViolationPunishRecord> selectByExample(SmtViolationPunishRecordExample example);

    SmtViolationPunishRecord selectByPrimaryKey(Long id);

    int insert(SmtViolationPunishRecord record);

    void batchInsert(List<SmtViolationPunishRecord> list);

    /**
     * 批量更新插入违规处罚记录
     * 若存在数据则覆盖更新数据，若不存在则插入新数据，记录同步时间
     * 唯一键判断：account + productId + punishId + skuCode（支持空值）
     *
     * @param list 违规处罚记录列表
     */
    void batchUpsert(List<SmtViolationPunishRecord> list);

    int updateByPrimaryKeySelective(SmtViolationPunishRecord record);

    int updateByExampleSelective(SmtViolationPunishRecord record, SmtViolationPunishRecordExample example);

    int deleteByPrimaryKey(List<Long> ids);

    int deleteByAccount(String account);

    List<String> selectType(String type);

    List<String> selectAccountsByExample(SmtViolationPunishRecordExample example);

    /**
     * 清理重复数据
     * 以店铺账号(account)、产品ID(productId)、违规编号(punishId)、SKU编码(skuCode)四个字段组合作为唯一性判断标准
     * 对于相同组合的多条记录，仅保留爬取时间(crawlTime)最新的一条数据，爬取时间相同时保留ID更大的一条，删除其他重复记录
     *
     * @return 数据清理结果详情
     */
    DataCleanResultDto cleanDuplicateRecords();

    /**
     * 批量设置SKU禁售
     */
    String batchSetBanStatus(SmtBatchSetBanStatusDTO dto);

    /**
     * 根据违规处罚SKU禁售规则自动处理SKU禁售
     * 1. 根据启用状态的规则匹配违规处罚列表数据
     * 2. 若数据已设置禁售则过滤不处理
     * 3. 若列表SKU为空则过滤不处理
     * 4. 匹配成功的SKU标记禁售，记录设置禁售时间
     * 5. 同一SKU的所有相同记录均标记禁售
     *
     * @return 处理结果信息
     */
    String processSkuBanByRules();
}
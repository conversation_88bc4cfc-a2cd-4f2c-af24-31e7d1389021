package com.estone.erp.publish.smt.service.impl;

import com.estone.erp.common.model.api.CQuery;
import com.estone.erp.common.model.api.CQueryResult;
import com.estone.erp.publish.smt.mapper.AdsPublishSkuOrderStatsSmtResult2Mapper;
import com.estone.erp.publish.smt.model.AdsPublishSkuOrderStatsSmtResult2;
import com.estone.erp.publish.smt.model.AdsPublishSkuOrderStatsSmtResult2Criteria;
import com.estone.erp.publish.smt.model.AdsPublishSkuOrderStatsSmtResult2Example;
import com.estone.erp.publish.smt.service.AdsPublishSkuOrderStatsSmtResult2Service;
import java.util.List;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

/**
 * <AUTHOR> ads_publish_sku_order_stats_smt_result2
 * 2024-02-21 11:29:37
 */
@Service("adsPublishSkuOrderStatsSmtResult2Service")
@Slf4j
public class AdsPublishSkuOrderStatsSmtResult2ServiceImpl implements AdsPublishSkuOrderStatsSmtResult2Service {
    @Resource
    private AdsPublishSkuOrderStatsSmtResult2Mapper adsPublishSkuOrderStatsSmtResult2Mapper;

    @Override
    public int countByExample(AdsPublishSkuOrderStatsSmtResult2Example example) {
        Assert.notNull(example, "example is null!");
        return adsPublishSkuOrderStatsSmtResult2Mapper.countByExample(example);
    }

    @Override
    public CQueryResult<AdsPublishSkuOrderStatsSmtResult2> search(CQuery<AdsPublishSkuOrderStatsSmtResult2Criteria> cquery) {
        Assert.notNull(cquery, "cquery is null!");
        AdsPublishSkuOrderStatsSmtResult2Criteria query = cquery.getSearch();
        AdsPublishSkuOrderStatsSmtResult2Example example = query.getExample();
        long total = 0;
        int totalPages = 0;
        // 是否分页
        if (cquery.isPageReqired()) {
            total = adsPublishSkuOrderStatsSmtResult2Mapper.countByExample(example);
            totalPages = (int) Math.ceil((double) total / cquery.getLimit());
            example.setLimit(cquery.getLimit());
            example.setOffset(cquery.getOffset());
        }
        List<AdsPublishSkuOrderStatsSmtResult2> adsPublishSkuOrderStatsSmtResult2s = adsPublishSkuOrderStatsSmtResult2Mapper.selectByExample(example);
        // 组装结果
        CQueryResult<AdsPublishSkuOrderStatsSmtResult2> result = new CQueryResult<>();
        result.setTotal(total);
        result.setTotalPages(totalPages);
        result.setRows(adsPublishSkuOrderStatsSmtResult2s);
        return result;
    }

    @Override
    public AdsPublishSkuOrderStatsSmtResult2 selectByPrimaryKey(Integer id) {
        Assert.notNull(id, "id is null!");
        return adsPublishSkuOrderStatsSmtResult2Mapper.selectByPrimaryKey(id);
    }

    @Override
    public List<AdsPublishSkuOrderStatsSmtResult2> selectByExample(AdsPublishSkuOrderStatsSmtResult2Example example) {
        Assert.notNull(example, "example is null!");
        return adsPublishSkuOrderStatsSmtResult2Mapper.selectByExample(example);
    }

    @Override
    public int insert(AdsPublishSkuOrderStatsSmtResult2 record) {
        Assert.notNull(record, "record is null!");
        // 默认加时间和人
        return adsPublishSkuOrderStatsSmtResult2Mapper.insert(record);
    }

    @Override
    public int updateByPrimaryKeySelective(AdsPublishSkuOrderStatsSmtResult2 record) {
        Assert.notNull(record, "record is null!");
        // 默认加修改时间和修改人
        return adsPublishSkuOrderStatsSmtResult2Mapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public int updateByExampleSelective(AdsPublishSkuOrderStatsSmtResult2 record, AdsPublishSkuOrderStatsSmtResult2Example example) {
        Assert.notNull(record, "record is null!");
        // 默认加修改时间和修改人
        return adsPublishSkuOrderStatsSmtResult2Mapper.updateByExampleSelective(record, example);
    }

    @Override
    public int deleteByPrimaryKey(List<Integer> ids) {
        Assert.notNull(ids, "ids is null!");
        return adsPublishSkuOrderStatsSmtResult2Mapper.deleteByPrimaryKey(ids);
    }
}
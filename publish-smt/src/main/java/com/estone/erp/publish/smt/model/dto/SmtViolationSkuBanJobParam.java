package com.estone.erp.publish.smt.model.dto;

import lombok.Data;

import java.util.List;

/**
 * SMT违规处罚SKU禁售规则自动处理定时任务参数
 * 
 * <AUTHOR>
 * @date 2025-07-22
 */
@Data
public class SmtViolationSkuBanJobParam {
    
    /**
     * 指定的规则ID列表
     * 如果为空或null，则处理所有启用状态的规则
     */
    private List<Integer> ruleIds;
    
    /**
     * 是否强制执行
     * 默认为false，如果为true则忽略某些安全检查
     */
    private Boolean forceExecute = false;
    
    /**
     * 批处理大小
     * 默认为1000，用于控制单次处理的记录数量
     */
    private Integer batchSize = 1000;
    
    /**
     * 是否仅模拟执行
     * 如果为true，则只输出匹配结果，不实际更新数据
     */
    private Boolean dryRun = false;
}

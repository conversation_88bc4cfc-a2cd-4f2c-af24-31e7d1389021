package com.estone.erp.publish.smt.mq.listener;

import com.alibaba.fastjson.JSON;
import com.estone.erp.publish.smt.dto.UpdateStockZeroByStopAccountDTO;
import com.estone.erp.publish.smt.enums.ProductStatusTypeEnum;
import com.estone.erp.publish.system.account.SaleAccountAndBusinessResponse;
import com.estone.erp.publish.system.account.AccountUtils;
import com.estone.erp.publish.tidb.publishtidb.model.AliexpressHalfTgItem;
import com.estone.erp.publish.tidb.publishtidb.model.AliexpressHalfTgItemExample;
import com.estone.erp.publish.tidb.publishtidb.service.AliexpressHalfTgItemService;
import com.estone.erp.publish.smt.util.AliexpressLogUtils;
import com.estone.erp.publish.smt.bean.HalfTgItemRequest;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.system.product.util.CheckSkuUtils;
import com.estone.erp.publish.common.util.CommonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.listener.api.ChannelAwareMessageListener;
import org.springframework.stereotype.Component;
import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;
import com.rabbitmq.client.Channel;

@Slf4j
@Component
public class SmtUpdateStockZeroByStopTgMqListener implements ChannelAwareMessageListener {
    @Resource
    private AliexpressHalfTgItemService aliexpressHalfTgItemService;

    @Override
    public void onMessage(Message message, Channel channel) throws Exception {
        try {
            UpdateStockZeroByStopAccountDTO dto = JSON.parseObject(message.getBody(), UpdateStockZeroByStopAccountDTO.class);
            String accountNumber = dto.getAccountNumber();
            List<String> skuList = dto.getSkuList();
            SaleAccountAndBusinessResponse saleAccountAndBusinessResponse = AccountUtils.getSaleAccountByAccountNumber(SaleChannel.CHANNEL_SMT, accountNumber);
            if (saleAccountAndBusinessResponse == null) {
                log.warn("未找到店铺信息: {}", accountNumber);
                channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
                return;
            }
            String skulifecyclephase = "Archived,Stop";
            List<String> stopSkuStatusList = CommonUtils.splitList(skulifecyclephase, ",");
            AliexpressHalfTgItemExample halfTgItemExample = new AliexpressHalfTgItemExample();
            AliexpressHalfTgItemExample.Criteria criteria = halfTgItemExample.createCriteria();
            criteria.andAccountEqualTo(accountNumber);
            criteria.andProductStatusEqualTo(ProductStatusTypeEnum.onSelling.getCode());
            criteria.andSkuStatusIn(stopSkuStatusList);
            if(CollectionUtils.isNotEmpty(skuList)){
                criteria.andArticleNumberIn(skuList);
            }
            halfTgItemExample.setFields("id, account, product_id, article_number, sku_code, sku_status, sku_id, pop_choice_sku_warehouse_stock_list, sku_data_source");
            List<AliexpressHalfTgItem> dbList = aliexpressHalfTgItemService.selectByExample(halfTgItemExample);
            if(CollectionUtils.isEmpty(dbList)){
                channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
                return;
            }
            dbList.removeIf(item -> !CheckSkuUtils.checkSkuStatus(item.getArticleNumber(), item.getSkuDataSource(), SaleChannel.CHANNEL_AMAZON));
            if (CollectionUtils.isEmpty(dbList)) {
                channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
                return;
            }
            Map<Long, List<AliexpressHalfTgItem>> productIdMap = dbList.stream().collect(Collectors.groupingBy(t -> t.getProductId()));
            for (Map.Entry<Long, List<AliexpressHalfTgItem>> longListEntry : productIdMap.entrySet()) {
                try {
                    Long key = longListEntry.getKey();
                    List<HalfTgItemRequest> requestsList = new ArrayList<>();
                    List<AliexpressHalfTgItem> value = longListEntry.getValue();
                    for (AliexpressHalfTgItem aliexpressHalfTgItem : value) {
                        String popChoiceSkuWarehouseStockList = aliexpressHalfTgItem.getPopChoiceSkuWarehouseStockList();
                        com.alibaba.fastjson.JSONArray dbJsonArray = com.alibaba.fastjson.JSONObject.parseArray(popChoiceSkuWarehouseStockList);
                        for (int i = 0; i < dbJsonArray.size(); i++) {
                            com.alibaba.fastjson.JSONObject dbJsonArrayJSONObject = dbJsonArray.getJSONObject(i);
                            String warehouse_code = dbJsonArrayJSONObject.getString("warehouse_code");
                            String warehouse_name = dbJsonArrayJSONObject.getString("warehouse_name");
                            Integer sellable_quantity = dbJsonArrayJSONObject.getInteger("sellable_quantity");
                            if(sellable_quantity != null && sellable_quantity > 0){
                                HalfTgItemRequest itemRequest = new HalfTgItemRequest();
                                itemRequest.setAccount(accountNumber);
                                itemRequest.setSkuId(aliexpressHalfTgItem.getSkuId());
                                itemRequest.setWarehouseName(warehouse_name);
                                itemRequest.setWarehouseCode(warehouse_code);
                                itemRequest.setSellableQuantity(0);
                                itemRequest.setProductId(key);
                                requestsList.add(itemRequest);
                            }
                        }
                    }
                    if(CollectionUtils.isNotEmpty(requestsList)){
                        aliexpressHalfTgItemService.updateStocks(saleAccountAndBusinessResponse, key, requestsList, dto.getCreateBy());
                    }
                } catch (Exception e) {
                    log.error(e.getMessage(), e);
                }
            }
            channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
        } catch (Exception e) {
            log.error("归零定时任务队列消费异常", e);
            channel.basicNack(message.getMessageProperties().getDeliveryTag(), false, true);
        }
    }
} 
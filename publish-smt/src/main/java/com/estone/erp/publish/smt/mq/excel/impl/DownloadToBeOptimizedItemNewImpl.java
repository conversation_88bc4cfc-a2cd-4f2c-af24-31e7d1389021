package com.estone.erp.publish.smt.mq.excel.impl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.estone.erp.common.constant.StrConstant;
import com.estone.erp.common.model.SeaweedFile;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.model.api.CQuery;
import com.estone.erp.common.model.api.CQueryResult;
import com.estone.erp.common.util.DateUtils;
import com.estone.erp.common.util.SpringUtils;
import com.estone.erp.common.util.upload.FmsApiUtil;
import com.estone.erp.publish.common.component.StatusCode;
import com.estone.erp.publish.common.json.ResponseJson;
import com.estone.erp.publish.smt.bean.excel.AliexpressProductDiagnosisExcel;
import com.estone.erp.publish.smt.enums.ExcelTypeEnum;
import com.estone.erp.publish.smt.model.AliexpressProductDiagnosis;
import com.estone.erp.publish.smt.model.AliexpressProductDiagnosisCriteria;
import com.estone.erp.publish.smt.model.AliexpressProductDiagnosisExample;
import com.estone.erp.publish.smt.mq.excel.ExcelAbstract;
import com.estone.erp.publish.smt.mq.excel.bean.ExcelBean;
import com.estone.erp.publish.smt.mq.excel.config.ExcelContext;
import com.estone.erp.publish.smt.mq.excel.constant.ExcelConstant;
import com.estone.erp.publish.smt.service.AliexpressProductDiagnosisService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import java.io.File;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 下载待优化产品 new
 *
 * <AUTHOR>
 * @date 2023年8月22日16:39:49
 */
public class DownloadToBeOptimizedItemNewImpl extends ExcelAbstract {

    AliexpressProductDiagnosisService aliexpressProductDiagnosisService = SpringUtils.getBean(AliexpressProductDiagnosisService.class);

    @Override
    public ResponseJson execute(ExcelContext context) {
        ResponseJson rsp = new ResponseJson(StatusCode.FAIL);
        ExcelBean excelBean = context.getExcelBean();
        CQuery<AliexpressProductDiagnosisCriteria> diagnosisCriteriaQuery = excelBean.getDiagnosisCriteriaQuery();

        // 获取下载数量
        try {

            AliexpressProductDiagnosisCriteria search = diagnosisCriteriaQuery.getSearch();
            AliexpressProductDiagnosisExample example = search.getExample();
            long count = aliexpressProductDiagnosisService.countByExample(example);
            if (count == 0) {
                rsp.setMessage("没有需要下载的数据！");
                return rsp;
            }
            if (count > 500000) {
                rsp.setMessage("下载数据不能超过50万条");
                return rsp;
            }
            rsp.getBody().put(ExcelConstant.itemCountConstant, count);
        } catch (Exception e) {
            rsp.setMessage(e.getMessage());
        }

        File file = null;
        try {
            List<String> accountList = new ArrayList<>();
            String fileName = "aliexpressToBeOptimizedItem-" + DateUtils.format(new Date(), "yyyy-MM-dd") + UUID.randomUUID();
            String suffix = ".xlsx";
            file = File.createTempFile(fileName, suffix);

            // 写入excel
            writeToExcel(diagnosisCriteriaQuery, file, accountList);
            rsp.getBody().put(ExcelConstant.accountStrConstant, StringUtils.join(accountList, ","));

            // 上传文件服务器并返回url
            uploadFile(file, fileName, rsp);
        } catch (Exception e) {
            rsp.setMessage(e.getMessage());
        } finally {
            if (null != file) {
                file.deleteOnExit();
            }
        }

        return rsp;
    }

    private void uploadFile(File file, String fileName, ResponseJson rsp) {
        ApiResult<SeaweedFile> uploadResult = FmsApiUtil.publishFileUpload(file, fileName, "productInfo", StrConstant.ADMIN);
        String url = null;
        if (!uploadResult.isSuccess()) {
            rsp.setMessage("上传文件服务器报错：" + uploadResult.getErrorMsg());
        }
        SeaweedFile seaweedFile = uploadResult.getResult();
        if (null != seaweedFile) {
            url = seaweedFile.getUrl2();
        }

        rsp.setMessage(url);
        rsp.setStatus(StatusCode.SUCCESS);
    }

    private void writeToExcel(CQuery<AliexpressProductDiagnosisCriteria> diagnosisCriteriaQuery, File file, List<String> accountList) {
        Set<String> accountSet = new HashSet<>();
        ExcelWriter excelWriter = EasyExcel.write(file, AliexpressProductDiagnosisExcel.class).build();
        WriteSheet writeSheet = EasyExcel.writerSheet("sheet").build();
//        diagnosisCriteriaQuery.getSearch().getExample().setOrderByClause("id"); //正序
        int offset = 0;
        int limt = 1000;
        while (true) {
            diagnosisCriteriaQuery.setOffset(offset);
            diagnosisCriteriaQuery.setLimit(limt);
            CQueryResult<AliexpressProductDiagnosis> search = aliexpressProductDiagnosisService.search(diagnosisCriteriaQuery);
            if (null == search || CollectionUtils.isEmpty(search.getRows())) {
                break;
            }
            offset += limt;
            List<AliexpressProductDiagnosisExcel> aliexpressProductDiagnosisExcels = changeDownloadItem(search.getRows());
            // 写入excel
            excelWriter.write(aliexpressProductDiagnosisExcels, writeSheet);

            // 获取账号
            if (accountSet.size() < 50) {
                List<String> accounts = aliexpressProductDiagnosisExcels.stream()
                        .map(AliexpressProductDiagnosisExcel::getAccount).distinct()
                        .filter(o -> !accountSet.contains(o)).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(accounts)) {
                    continue;
                }
                if (accountSet.size() + accounts.size() > 50) {
                    accounts = accounts.subList(0, 50 - accountSet.size());
                }
                accountSet.addAll(accounts);
            }
        }
        accountList.addAll(accountSet);
        excelWriter.finish();
    }

    private List<AliexpressProductDiagnosisExcel> changeDownloadItem(List<AliexpressProductDiagnosis> optimizedItems) {
        if(CollectionUtils.isEmpty(optimizedItems)){
            return Collections.emptyList();
        }

        SimpleDateFormat sdf = new SimpleDateFormat(com.estone.erp.common.util.DateUtils.STANDARD_DATE_PATTERN);

        List<AliexpressProductDiagnosisExcel> excelList = new ArrayList<>();
        for (AliexpressProductDiagnosis optimizedItem : optimizedItems) {
            AliexpressProductDiagnosisExcel excel = new AliexpressProductDiagnosisExcel();
            excel.setAccount(optimizedItem.getAccount());
            excel.setProductId(optimizedItem.getProductId().toString());
            excel.setProductTitle(optimizedItem.getProductTitle());
            excel.setMainPicture(optimizedItem.getMainPicture());
            excel.setProblemDescription(optimizedItem.getProblemDescription());
            excel.setProblemType(optimizedItem.getProblemType());
            excel.setProductImpact(optimizedItem.getProductImpact());
            excel.setOptimizationSuggestion(optimizedItem.getOptimizationSuggestion());
            excel.setLastUpdateDate(sdf.format(optimizedItem.getLastUpdateDate()));
            excel.setSalemanager(optimizedItem.getSalemanager());
            excel.setSalemanagerLeader(optimizedItem.getSalemanagerLeader());
            excel.setSalesSupervisorName(optimizedItem.getSalesSupervisorName());
            excelList.add(excel);
        }
        return excelList;
    }

    @Override
    public int type() {
        return ExcelTypeEnum.downloadToBeOptimizedItemNew.getCode();
    }
}

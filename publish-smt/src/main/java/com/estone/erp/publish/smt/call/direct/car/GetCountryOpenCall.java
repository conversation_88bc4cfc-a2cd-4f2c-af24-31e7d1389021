package com.estone.erp.publish.smt.call.direct.car;


import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.estone.erp.publish.common.component.StatusCode;
import com.estone.erp.publish.common.json.ResponseJson;
import com.estone.erp.publish.smt.call.direct.AbstractSmtOpenCall;
import com.estone.erp.publish.system.account.SaleAccountAndBusinessResponse;
import com.global.iop.api.IopRequest;
import com.global.iop.api.IopResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * 查询车型库支持的国家
 */
@Slf4j
public class GetCountryOpenCall {

    public ResponseJson getCountry(SaleAccountAndBusinessResponse saleAccountByAccountNumber, String lang) {
        ResponseJson rsp = new ResponseJson();
        rsp.setStatus(StatusCode.FAIL);
        if (saleAccountByAccountNumber == null || StringUtils.isBlank(saleAccountByAccountNumber.getAccessToken())) {
            rsp.setMessage("请求参数为空！");
            return rsp;
        }

        if(StringUtils.isBlank(lang)){
            lang = "zh";
        }
        Map<String, String> map = new HashMap<>();
        map.put("lang", lang);

        String callRspStr = "";
        try {
            IopRequest request = new IopRequest();
            request.setApiName("aliexpress.carmodel.get.country");
            request.addApiParameter("param0", JSONObject.toJSONString(map));
            long begin = System.currentTimeMillis();
            IopResponse iopResponse = AbstractSmtOpenCall.execute(saleAccountByAccountNumber,request);
            callRspStr = iopResponse.getBody();
            long end = System.currentTimeMillis();
            long l = (end - begin) / 1000;
            if (l > AbstractSmtOpenCall.logTime) {
//                log.warn(String.format("carmodel.get.country不通过奇门%s秒 rsp%s", l, callRspStr));
            }
            if(StringUtils.isNotBlank(callRspStr)){
                JSONObject callRspJson = JSONObject.parseObject(callRspStr);
                if (callRspJson.containsKey(
                        "aliexpress_carmodel_get_country_response")) {
                    JSONObject editRsp = callRspJson.getJSONObject(
                            "aliexpress_carmodel_get_country_response");
                    if (editRsp.containsKey("result")) {
                        JSONObject result = editRsp.getJSONObject("result");
                        if (result.containsKey("success") && result.getBoolean("success")) {
                            JSONArray data = result.getJSONArray("data");
                            rsp.getBody().put(AbstractSmtOpenCall.resultKey, data);
                            rsp.setStatus(StatusCode.SUCCESS);
                        }
                        if (result.containsKey("error_msg")) {
                            String errorMessage = result.getString("error_msg");
                            if (StringUtils.isNotBlank(errorMessage)) {
                                rsp.setMessage(errorMessage);
                            }
                        }
                    }
                }
            }
        }
        catch (Exception e) {
            log.error(e.getMessage(), e);
            rsp.setMessage(e.getMessage());
        }
        if(StringUtils.equalsIgnoreCase(rsp.getStatus(), StatusCode.FAIL) && StringUtils.isEmpty(rsp.getMessage())){
            rsp.setMessage(callRspStr);
        }
        return rsp;
    }
}

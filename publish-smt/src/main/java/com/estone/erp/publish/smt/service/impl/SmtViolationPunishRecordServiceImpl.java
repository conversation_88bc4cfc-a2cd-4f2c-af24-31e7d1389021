package com.estone.erp.publish.smt.service.impl;

import com.estone.erp.common.model.api.CQuery;
import com.estone.erp.common.model.api.CQueryResult;
import com.estone.erp.common.util.WebUtils;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.common.context.DataContextHolder;
import com.estone.erp.publish.elasticsearch.util.EsAccountUtils;
import com.estone.erp.publish.smt.mapper.SmtViolationPunishRecordMapper;
import com.estone.erp.publish.smt.model.SmtViolationPunishRecord;
import com.estone.erp.publish.smt.model.SmtViolationPunishRecordCriteria;
import com.estone.erp.publish.smt.model.SmtViolationPunishRecordExample;
import com.estone.erp.publish.smt.model.dto.DataCleanResultDto;
import com.estone.erp.publish.smt.model.dto.SmtBatchSetBanStatusDTO;
import com.estone.erp.publish.tidb.publishtidb.model.SmtViolationSkuInfringementRule;
import com.estone.erp.publish.tidb.publishtidb.service.SmtViolationSkuInfringementRuleService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.estone.erp.publish.smt.service.SmtViolationPunishRecordService;
import com.estone.erp.publish.system.account.modle.SalesmanAccountDetail;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * 2024-05-24 11:25:09
 */
@Service("smtViolationPunishRecordService")
@Slf4j
public class SmtViolationPunishRecordServiceImpl implements SmtViolationPunishRecordService {
    @Resource
    private SmtViolationPunishRecordMapper smtViolationPunishRecordMapper;

    @Resource
    private SmtViolationSkuInfringementRuleService smtViolationSkuInfringementRuleService;

    @Override
    public int countByExample(SmtViolationPunishRecordExample example) {
        Assert.notNull(example, "example is null!");
        return smtViolationPunishRecordMapper.countByExample(example);
    }

    @Override
    public CQueryResult<SmtViolationPunishRecord> search(CQuery<SmtViolationPunishRecordCriteria> cquery) {
        Assert.notNull(cquery, "cquery is null!");
        SmtViolationPunishRecordCriteria query = cquery.getSearch();
        SmtViolationPunishRecordExample example = query.getExample();
        long total = 0;
        int totalPages = 0;
        // 是否分页
        if (cquery.isPageReqired()) {
            total = smtViolationPunishRecordMapper.countByExample(example);
            totalPages = (int) Math.ceil((double) total / cquery.getLimit());
            example.setLimit(cquery.getLimit());
            example.setOffset(cquery.getOffset());
        }
        List<SmtViolationPunishRecord> smtViolationPunishRecords = smtViolationPunishRecordMapper.selectByExample(example);
        saleInfo(smtViolationPunishRecords);
        // 组装结果
        CQueryResult<SmtViolationPunishRecord> result = new CQueryResult<>();
        result.setTotal(total);
        result.setTotalPages(totalPages);
        result.setRows(smtViolationPunishRecords);
        return result;
    }

    public static void saleInfo(List<SmtViolationPunishRecord> itemList){
        long begin = System.currentTimeMillis();
        if(CollectionUtils.isNotEmpty(itemList)){
            List<String> accountList = itemList.stream().map(t -> t.getAccount()).distinct()
                    .collect(Collectors.toList());
            Map<String, SalesmanAccountDetail> salesmanAccountMap = EsAccountUtils.getSalesmanAccountDetailMapByEs(accountList, SaleChannel.CHANNEL_SMT);

            for (SmtViolationPunishRecord item : itemList) {
                String account = item.getAccount();
                SalesmanAccountDetail salesmanAccountDetail = salesmanAccountMap.get(account);
                if(salesmanAccountDetail == null || salesmanAccountDetail.getSalesmanSet() == null || salesmanAccountDetail.getSalesmanSet().isEmpty()){
                    continue;
                }
                String salemanager = new ArrayList<>(salesmanAccountDetail.getSalesmanSet()).get(0);
                item.setSalemanager(salemanager);
                item.setSalemanagerLeader(salesmanAccountDetail.getSalesTeamLeaderName());
                item.setSalesSupervisorName(salesmanAccountDetail.getSalesSupervisorName());
            }
        }
        long end = System.currentTimeMillis();
        log.warn("扩展销售.组长.主管耗时:" + (end - begin));
    }

    @Override
    public SmtViolationPunishRecord selectByPrimaryKey(Long id) {
        Assert.notNull(id, "id is null!");
        return smtViolationPunishRecordMapper.selectByPrimaryKey(id);
    }

    @Override
    public List<SmtViolationPunishRecord> selectByExample(SmtViolationPunishRecordExample example) {
        Assert.notNull(example, "example is null!");
        return smtViolationPunishRecordMapper.selectByExample(example);
    }

    @Override
    public int insert(SmtViolationPunishRecord record) {
        Assert.notNull(record, "record is null!");
        // 默认加时间和人
        return smtViolationPunishRecordMapper.insert(record);
    }

    @Override
    public void batchInsert(List<SmtViolationPunishRecord> list) {
        Assert.notNull(list, "list is null!");
         smtViolationPunishRecordMapper.batchInsert(list);
    }

    @Override
    public void batchUpsert(List<SmtViolationPunishRecord> list) {
        if (list.isEmpty()) {
            return;
        }

        SmtViolationPunishRecordExample example = new SmtViolationPunishRecordExample();
        // 批量查询现有记录，提升性能
        List<SmtViolationPunishRecord> existingRecords = smtViolationPunishRecordMapper.selectByExample(example);

        // 构建现有记录的唯一键映射，便于快速查找
        Map<String, SmtViolationPunishRecord> existingMap = new LinkedHashMap<>();
        for (SmtViolationPunishRecord existing : existingRecords) {
            String uniqueKey = buildGroupKey(existing.getAccount(), existing.getProductId(),
                    existing.getPunishId(), existing.getArticleNumber());
            existingMap.put(uniqueKey, existing);
        }

        List<SmtViolationPunishRecord> toInsert = new ArrayList<>();
        List<SmtViolationPunishRecord> toUpdate = new ArrayList<>();

        for (SmtViolationPunishRecord record : list) {
            String uniqueKey = buildGroupKey(record.getAccount(), record.getProductId(),
                    record.getPunishId(), record.getArticleNumber());
            SmtViolationPunishRecord existing = existingMap.get(uniqueKey);

            if (existing != null) {
                // 存在则更新，保留原有ID
                record.setId(existing.getId());
                record.setUpdateDate(new Timestamp(System.currentTimeMillis()));
                record.setCreateDate(existing.getCreateDate());
                record.setInfringementTypeName(existing.getInfringementTypeName());
                record.setInfringementObj(existing.getInfringementObj());
                record.setBanFlag(existing.getBanFlag());
                record.setBanTime(existing.getBanTime());
                record.setOperatedBy(existing.getOperatedBy());
                toUpdate.add(record);
            } else {
                // 不存在则插入
                record.setCreateDate(new Timestamp(System.currentTimeMillis()));
                record.setUpdateDate(new Timestamp(System.currentTimeMillis()));
                // 默认为否
                record.setBanFlag(false);
                toInsert.add(record);
            }
        }

        // 批量插入新记录
        if (!toInsert.isEmpty()) {
            smtViolationPunishRecordMapper.batchInsert(toInsert);
        }

        // 批量更新现有记录
        for (SmtViolationPunishRecord record : toUpdate) {
            smtViolationPunishRecordMapper.updateByPrimaryKeySelective(record);
        }
    }


    @Override
    public int updateByPrimaryKeySelective(SmtViolationPunishRecord record) {
        Assert.notNull(record, "record is null!");
        // 默认加修改时间和修改人
        return smtViolationPunishRecordMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public int updateByExampleSelective(SmtViolationPunishRecord record, SmtViolationPunishRecordExample example) {
        Assert.notNull(record, "record is null!");
        // 默认加修改时间和修改人
        return smtViolationPunishRecordMapper.updateByExampleSelective(record, example);
    }

    @Override
    public int deleteByPrimaryKey(List<Long> ids) {
        Assert.notNull(ids, "ids is null!");
        return smtViolationPunishRecordMapper.deleteByPrimaryKey(ids);
    }

    @Override
    public int deleteByAccount(String account) {
        Assert.notNull(account, "account is null!");
        return smtViolationPunishRecordMapper.deleteByAccount(account);
    }

    @Override
    public List<String> selectType(String type) {
        Assert.notNull(type, "type is null!");
        return smtViolationPunishRecordMapper.selectType(type);
    }

    @Override
    public List<String> selectAccountsByExample(SmtViolationPunishRecordExample example) {
        Assert.notNull(example, "example is null!");
        return smtViolationPunishRecordMapper.selectAccountsByExample(example);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public DataCleanResultDto cleanDuplicateRecords() {
        DataCleanResultDto result = new DataCleanResultDto();
        log.info("开始清理smt_violation_punish_record表中的重复数据");

        try {
            // 获取清理前的总记录数
            SmtViolationPunishRecordExample countExample = new SmtViolationPunishRecordExample();
            result.setTotalRecordsBefore(smtViolationPunishRecordMapper.countByExample(countExample));

            // 查询所有记录用于Java代码处理
            List<SmtViolationPunishRecord> allRecords = smtViolationPunishRecordMapper.selectAllRecordsForDuplicateCheck();

            if (CollectionUtils.isEmpty(allRecords)) {
                log.info("表中无数据，无需清理");
                result.setDeletedCount(0);
                result.setTotalRecordsAfter(0);
                result.setEndTimeAndCalculateDuration();
                result.generateMessage();
                return result;
            }

            // 使用Java代码识别重复数据
            List<Long> duplicateIds = findDuplicateRecordIds(allRecords);

            if (CollectionUtils.isEmpty(duplicateIds)) {
                log.info("未发现重复数据，无需清理");
                result.setDeletedCount(0);
                result.setTotalRecordsAfter(result.getTotalRecordsBefore());
                result.setEndTimeAndCalculateDuration();
                result.generateMessage();
                return result;
            }

            log.info("发现{}条重复记录需要清理", duplicateIds.size());

            // 分批删除，避免大事务问题，每批处理500条记录
            int batchSize = 500;
            int totalDeleted = 0;
            List<List<Long>> batches = Lists.partition(duplicateIds, batchSize);
            result.setBatchCount(batches.size());
            result.setBatchSize(batchSize);

            for (int i = 0; i < batches.size(); i++) {
                List<Long> batch = batches.get(i);
                int deletedCount = smtViolationPunishRecordMapper.batchDeleteDuplicateRecords(batch);
                totalDeleted += deletedCount;
                log.info("第{}批删除完成，删除{}条记录", i + 1, deletedCount);
            }

            result.setDeletedCount(totalDeleted);
            result.setTotalRecordsAfter(result.getTotalRecordsBefore() - totalDeleted);
            result.setEndTimeAndCalculateDuration();
            result.generateMessage();

            log.info("重复数据清理完成，共删除{}条记录", totalDeleted);
            return result;

        } catch (Exception e) {
            log.error("清理重复数据时发生异常", e);
            result.setSuccess(false);
            result.setErrorMessage(e.getMessage());
            result.setEndTimeAndCalculateDuration();
            result.generateMessage();
            throw new RuntimeException("清理重复数据失败：" + e.getMessage(), e);
        }
    }

    /**
     * 使用Java代码识别重复记录ID
     * 清理规则：以店铺账号(account)、产品ID(productId)、违规编号(punishId)、SKU编码(skuCode)四个字段组合作为唯一性判断标准
     * 对于相同组合的多条记录，仅保留爬取时间(crawlTime)最新的一条数据，爬取时间相同时保留ID更大的一条
     *
     * @param allRecords 所有记录列表（已按照account、product_id、punish_id、sku_code、crawl_time DESC、id ASC排序）
     * @return 需要删除的重复记录ID列表
     */
    private List<Long> findDuplicateRecordIds(List<SmtViolationPunishRecord> allRecords) {
        List<Long> duplicateIds = new ArrayList<>();

        // 使用LinkedHashMap保持插入顺序，key为组合键，value为该组合的记录列表
        Map<String, List<SmtViolationPunishRecord>> groupedRecords = new LinkedHashMap<>();

        // 按照account、productId、punishId、skuCode组合进行分组
        for (SmtViolationPunishRecord record : allRecords) {
            String groupKey = buildGroupKey(record.getAccount(), record.getProductId(), record.getPunishId(), record.getArticleNumber());
            groupedRecords.computeIfAbsent(groupKey, k -> new ArrayList<>()).add(record);
        }

        log.info("共找到{}个不同的组合键", groupedRecords.size());

        // 处理每个分组，找出需要删除的重复记录
        for (Map.Entry<String, List<SmtViolationPunishRecord>> entry : groupedRecords.entrySet()) {
            List<SmtViolationPunishRecord> records = entry.getValue();

            // 如果该组合只有一条记录，则无需处理
            if (records.size() <= 1) {
                continue;
            }

            log.debug("组合键 {} 有{}条记录需要处理", entry.getKey(), records.size());

            // 找到最新的爬取时间
            SmtViolationPunishRecord firstRecord = records.get(0);
            java.sql.Timestamp latestCrawlTime = firstRecord.getCrawlTime();

            // 收集具有最新爬取时间的记录
            List<SmtViolationPunishRecord> latestRecords = new ArrayList<>();
            for (SmtViolationPunishRecord record : records) {
                if (record.getCrawlTime() != null && record.getCrawlTime().equals(latestCrawlTime)) {
                    latestRecords.add(record);
                } else {
                    // 爬取时间较早的记录直接加入删除列表
                    duplicateIds.add(record.getId());
                }
            }

            // 如果有多条记录具有相同的最新爬取时间，保留id更大的一条
            if (latestRecords.size() > 1) {
                // 找到ID最大的记录
                SmtViolationPunishRecord maxIdRecord = latestRecords.stream()
                        .max((r1, r2) -> Long.compare(r1.getId(), r2.getId()))
                        .orElse(latestRecords.get(0));

                // 将其他记录加入删除列表
                for (SmtViolationPunishRecord record : latestRecords) {
                    if (!record.getId().equals(maxIdRecord.getId())) {
                        duplicateIds.add(record.getId());
                    }
                }
                log.debug("爬取时间相同的{}条记录中保留ID最大的记录(ID:{})", latestRecords.size(), maxIdRecord.getId());
            }
        }

        log.info("Java代码处理完成，共识别出{}条重复记录需要删除", duplicateIds.size());
        return duplicateIds;
    }

    /**
     * 构建分组键
     *
     * @param account       店铺账号
     * @param productId     产品ID
     * @param punishId      违规编号
     * @param articleNumber SKU编码
     * @return 组合键字符串
     */
    private String buildGroupKey(String account, String productId, String punishId, String articleNumber) {
        return String.format("%s|%s|%s|%s",
                StringUtils.isNotBlank(account) ? account : "",
                StringUtils.isNotBlank(productId) ? productId : "",
                StringUtils.isNotBlank(punishId) ? punishId : "",
                StringUtils.isNotBlank(articleNumber) ? articleNumber : "");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String batchSetBanStatus(SmtBatchSetBanStatusDTO dto) {
        List<Long> ids = dto.getIds();
        String infringementObj = dto.getInfringementObj();
        String infringementTypeName = dto.getInfringementTypeName();
        try {
            // 根据ids查询记录
            SmtViolationPunishRecordExample example = new SmtViolationPunishRecordExample();
            example.createCriteria().andIdIn(ids);
            List<SmtViolationPunishRecord> records = smtViolationPunishRecordMapper.selectByExample(example);

            if (CollectionUtils.isEmpty(records)) {
                return "未找到任何记录";
            }

            // 过滤条件：过滤掉已设置禁售的数据和SKU为空的数据
            List<SmtViolationPunishRecord> validRecords = records.stream()
                    .filter(record -> !Boolean.TRUE.equals(record.getBanFlag())) // 过滤掉已设置禁售的数据
                    .filter(record -> StringUtils.isNotBlank(record.getArticleNumber())) // 过滤掉SKU为空的数据
                    .collect(Collectors.toList());

            if (CollectionUtils.isEmpty(validRecords)) {
                return "没有符合条件的记录可以设置禁售（已过滤掉已设置禁售的数据和SKU为空的数据）";
            }

            // 获取当前操作用户
            String currentUser = StringUtils.isBlank(WebUtils.getUserName()) ? DataContextHolder.getUsername() : WebUtils.getUserName();

            // 批量更新记录
            Timestamp currentTime = new Timestamp(System.currentTimeMillis());
            int updatedCount = 0;

            for (SmtViolationPunishRecord record : validRecords) {
                SmtViolationPunishRecord updateRecord = new SmtViolationPunishRecord();
                updateRecord.setId(record.getId());
                updateRecord.setInfringementTypeName(infringementTypeName);
                updateRecord.setInfringementObj(infringementObj);
                updateRecord.setBanFlag(true);
                updateRecord.setBanTime(currentTime);
                updateRecord.setOperatedBy(currentUser);
                updateRecord.setUpdateDate(currentTime);

                int result = smtViolationPunishRecordMapper.updateByPrimaryKeySelective(updateRecord);
                if (result > 0) {
                    updatedCount++;
                }
            }

            String resultMessage = String.format("批量设置SKU禁售完成，共处理%d条记录，成功更新%d条记录", validRecords.size(), updatedCount);
            log.info(resultMessage);
            return resultMessage;

        } catch (Exception e) {
            log.error("批量设置SKU禁售时发生异常", e);
            throw new RuntimeException("批量设置SKU禁售失败: " + e.getMessage(), e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String processSkuBanByRules() {
        return processSkuBanByRules(null);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String processSkuBanByRules(List<Integer> ruleIds) {
        log.info("开始执行违规处罚SKU禁售规则自动处理任务，指定规则ID: {}", ruleIds);

        try {
            // 1. 查询启用状态的SKU禁售规则
            LambdaQueryWrapper<SmtViolationSkuInfringementRule> ruleWrapper = new LambdaQueryWrapper<>();
            ruleWrapper.eq(SmtViolationSkuInfringementRule::getStatus, 1); // 启用状态

            // 如果指定了规则ID列表，则添加ID过滤条件
            if (CollectionUtils.isNotEmpty(ruleIds)) {
                ruleWrapper.in(SmtViolationSkuInfringementRule::getId, ruleIds);
                log.info("指定处理规则ID列表: {}", ruleIds);
            }

            List<SmtViolationSkuInfringementRule> enabledRules = smtViolationSkuInfringementRuleService.list(ruleWrapper);

            if (CollectionUtils.isEmpty(enabledRules)) {
                log.info("未找到启用状态的SKU禁售规则，任务结束");
                return "未找到启用状态的SKU禁售规则";
            }

            log.info("找到{}条启用状态的SKU禁售规则", enabledRules.size());

            // 2. 查询未设置禁售且SKU不为空的违规处罚记录
            SmtViolationPunishRecordExample recordExample = new SmtViolationPunishRecordExample();
            SmtViolationPunishRecordExample.Criteria criteria = recordExample.createCriteria();
            criteria.andSkuCodeIsNotNull(); // SKU不为空

            // 未设置禁售的条件：banFlag为null或者banFlag为false
            SmtViolationPunishRecordExample.Criteria criteria2 = recordExample.or();
            criteria2.andBanFlagIsNull(); // banFlag为null
            criteria2.andBanFlagEqualTo(false); // banFlag为false

            List<SmtViolationPunishRecord> candidateRecords = smtViolationPunishRecordMapper.selectByExample(recordExample);

            if (CollectionUtils.isEmpty(candidateRecords)) {
                log.info("未找到符合条件的违规处罚记录，任务结束");
                return "未找到符合条件的违规处罚记录";
            }

            log.info("找到{}条候选违规处罚记录", candidateRecords.size());

            return processRecordsByRules(enabledRules, candidateRecords);

        } catch (Exception e) {
            log.error("执行违规处罚SKU禁售规则自动处理任务失败", e);
            throw new RuntimeException("执行违规处罚SKU禁售规则自动处理任务失败: " + e.getMessage());
        }
    }
    /**
     * 根据规则处理违规记录
     *
     * @param enabledRules 启用的规则列表
     * @param candidateRecords 候选记录列表
     * @return 处理结果信息
     */
    private String processRecordsByRules(List<SmtViolationSkuInfringementRule> enabledRules,
                                       List<SmtViolationPunishRecord> candidateRecords) {
        int totalProcessed = 0;
        int totalBanned = 0;
        Set<String> processedSkus = new HashSet<>();
        Timestamp currentTime = new Timestamp(System.currentTimeMillis());
        String currentUser = "admin"; // 操作人为admin

        // 3. 遍历规则，匹配记录
        for (SmtViolationSkuInfringementRule rule : enabledRules) {
            log.info("处理规则ID: {}, 违规类型: {}, 违规原因: {}", rule.getId(), rule.getSubType(), rule.getPunishCause());

            List<SmtViolationPunishRecord> matchedRecords = findMatchedRecords(rule, candidateRecords);

            if (CollectionUtils.isEmpty(matchedRecords)) {
                log.info("规则ID: {} 未匹配到任何记录", rule.getId());
                continue;
            }

            log.info("规则ID: {} 匹配到{}条记录", rule.getId(), matchedRecords.size());

            // 4. 处理匹配的记录
            for (SmtViolationPunishRecord matchedRecord : matchedRecords) {
                String skuCode = matchedRecord.getSkuCode();

                // 跳过已处理的SKU
                if (processedSkus.contains(skuCode)) {
                    continue;
                }

                // 5. 查找所有相同SKU的记录并标记禁售
                int bannedCount = banAllRecordsWithSameSku(skuCode, rule, currentTime, currentUser);

                if (bannedCount > 0) {
                    processedSkus.add(skuCode);
                    totalBanned += bannedCount;
                    log.info("SKU: {} 共标记禁售{}条记录", skuCode, bannedCount);
                }

                totalProcessed++;
            }
        }

        String resultMessage = String.format("违规处罚SKU禁售规则自动处理完成，共处理%d个SKU，标记禁售%d条记录",
                                            totalProcessed, totalBanned);
        log.info(resultMessage);
        return resultMessage;
    }
    /**
     * 根据规则查找匹配的记录
     *
     * @param rule 禁售规则
     * @param candidateRecords 候选记录列表
     * @return 匹配的记录列表
     */
    private List<SmtViolationPunishRecord> findMatchedRecords(SmtViolationSkuInfringementRule rule,
                                                            List<SmtViolationPunishRecord> candidateRecords) {
        List<SmtViolationPunishRecord> matchedRecords = new ArrayList<>();

        for (SmtViolationPunishRecord record : candidateRecords) {
            // 检查违规类型匹配
            if (StringUtils.isNotBlank(rule.getSubType()) &&
                !rule.getSubType().equals(record.getSubType())) {
                continue;
            }

            // 检查违规原因匹配
            if (StringUtils.isNotBlank(rule.getPunishCause()) &&
                !rule.getPunishCause().equals(record.getPunishCause())) {
                continue;
            }

            // 检查扣分范围匹配
            if (!isScoreInRange(record.getPointScore(), rule.getPointScoreStart(), rule.getPointScoreEnd())) {
                continue;
            }

            // 检查计次范围匹配
            if (!isCountInRange(record.getPointCount(), rule.getPointCountStart(), rule.getPointCountEnd())) {
                continue;
            }

            matchedRecords.add(record);
        }

        return matchedRecords;
    }

    /**
     * 检查扣分是否在范围内
     *
     * @param pointScore 实际扣分
     * @param startScore 起始扣分
     * @param endScore 结束扣分
     * @return 是否在范围内
     */
    private boolean isScoreInRange(Integer pointScore, Integer startScore, Integer endScore) {
        // 如果规则没有设置扣分范围，则不进行扣分匹配
        if (startScore == null && endScore == null) {
            return true;
        }

        // 如果记录没有扣分值，则不匹配
        if (pointScore == null) {
            return false;
        }

        // 检查起始值
        if (startScore != null && pointScore < startScore) {
            return false;
        }

        // 检查结束值
        if (endScore != null && pointScore > endScore) {
            return false;
        }

        return true;
    }
    /**
     * 检查计次是否在范围内
     *
     * @param pointCount 实际计次
     * @param startCount 起始计次
     * @param endCount 结束计次
     * @return 是否在范围内
     */
    private boolean isCountInRange(Integer pointCount, Integer startCount, Integer endCount) {
        // 如果规则没有设置计次范围，则不进行计次匹配
        if (startCount == null && endCount == null) {
            return true;
        }

        // 如果记录没有计次值，则不匹配
        if (pointCount == null) {
            return false;
        }

        // 检查起始值
        if (startCount != null && pointCount < startCount) {
            return false;
        }

        // 检查结束值
        if (endCount != null && pointCount > endCount) {
            return false;
        }

        return true;
    }

    /**
     * 标记所有相同SKU的记录为禁售
     *
     * @param skuCode SKU编码
     * @param rule 禁售规则
     * @param currentTime 当前时间
     * @param currentUser 当前用户
     * @return 标记禁售的记录数量
     */
    private int banAllRecordsWithSameSku(String skuCode, SmtViolationSkuInfringementRule rule,
                                       Timestamp currentTime, String currentUser) {
        // 查询所有相同SKU的记录
        SmtViolationPunishRecordExample example = new SmtViolationPunishRecordExample();
        example.createCriteria().andSkuCodeEqualTo(skuCode);

        List<SmtViolationPunishRecord> sameSkuRecords = smtViolationPunishRecordMapper.selectByExample(example);

        if (CollectionUtils.isEmpty(sameSkuRecords)) {
            return 0;
        }

        int bannedCount = 0;

        // 批量更新所有相同SKU的记录
        for (SmtViolationPunishRecord record : sameSkuRecords) {
            SmtViolationPunishRecord updateRecord = new SmtViolationPunishRecord();
            updateRecord.setId(record.getId());
            updateRecord.setInfringementTypeName(rule.getInfringementTypeName()); // 禁售类型
            updateRecord.setInfringementObj(rule.getInfringementObj()); // 禁售原因
            updateRecord.setBanFlag(true); // 设置禁售
            updateRecord.setBanTime(currentTime); // 设置禁售时间
            updateRecord.setOperatedBy(currentUser); // 操作人
            updateRecord.setUpdateDate(currentTime); // 更新时间

            int result = smtViolationPunishRecordMapper.updateByPrimaryKeySelective(updateRecord);
            if (result > 0) {
                bannedCount++;
            }
        }

        return bannedCount;
    }
}
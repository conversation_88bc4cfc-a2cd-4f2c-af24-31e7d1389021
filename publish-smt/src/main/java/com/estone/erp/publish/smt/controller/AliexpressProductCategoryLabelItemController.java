package com.estone.erp.publish.smt.controller;

import com.alibaba.fastjson.TypeReference;
import com.estone.erp.common.model.api.ApiRequestParam;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.model.api.CQuery;
import com.estone.erp.common.model.api.CQueryResult;
import com.estone.erp.common.util.Asserts;
import com.estone.erp.publish.common.ErrorCode;
import com.estone.erp.publish.smt.model.AliexpressProductCategoryLabelItem;
import com.estone.erp.publish.smt.model.AliexpressProductCategoryLabelItemCriteria;
import com.estone.erp.publish.smt.service.AliexpressProductCategoryLabelItemService;
import javax.annotation.Resource;
import org.apache.commons.lang.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR> aliexpress_product_category_label_item
 * 2023-09-11 16:09:08
 */
@RestController
@RequestMapping("aliexpressProductCategoryLabelItem")
public class AliexpressProductCategoryLabelItemController {
    @Resource
    private AliexpressProductCategoryLabelItemService aliexpressProductCategoryLabelItemService;

    @PostMapping
    public ApiResult<?> postAliexpressProductCategoryLabelItem(@RequestBody(required = true) ApiRequestParam<String> requestParam) {
        String method = requestParam.getMethod();
        if (StringUtils.isNotBlank(method)) {
            switch (method) {
                case "searchAliexpressProductCategoryLabelItem": // 查询列表
                    CQuery<AliexpressProductCategoryLabelItemCriteria> cquery = requestParam.getArgsValue(new TypeReference<CQuery<AliexpressProductCategoryLabelItemCriteria>>() {});
                    Asserts.isTrue(cquery != null, ErrorCode.PARAM_EMPTY_ERROR);
                    CQueryResult<AliexpressProductCategoryLabelItem> results = aliexpressProductCategoryLabelItemService.search(cquery);
                    return results;
                case "addAliexpressProductCategoryLabelItem": // 添加
                    AliexpressProductCategoryLabelItem aliexpressProductCategoryLabelItem = requestParam.getArgsValue(new TypeReference<AliexpressProductCategoryLabelItem>() {});
                    aliexpressProductCategoryLabelItemService.insert(aliexpressProductCategoryLabelItem);
                    return ApiResult.newSuccess(aliexpressProductCategoryLabelItem);
                }
        }
        return ApiResult.newSuccess();
    }

    @GetMapping(value = "/{id}")
    public ApiResult<?> getAliexpressProductCategoryLabelItem(@PathVariable(value = "id", required = true) Integer id) {
        AliexpressProductCategoryLabelItem aliexpressProductCategoryLabelItem = aliexpressProductCategoryLabelItemService.selectByPrimaryKey(id);
        return ApiResult.newSuccess(aliexpressProductCategoryLabelItem);
    }

    @PutMapping(value = "/{id}")
    public ApiResult<?> putAliexpressProductCategoryLabelItem(@PathVariable(value = "id", required = true) Integer id, @RequestBody ApiRequestParam<String> requestParam) {
        String method = requestParam.getMethod();
        if (StringUtils.isNotBlank(method)) {
            switch (method) {
                case "updateAliexpressProductCategoryLabelItem": // 单个修改
                    AliexpressProductCategoryLabelItem aliexpressProductCategoryLabelItem = requestParam.getArgsValue(new TypeReference<AliexpressProductCategoryLabelItem>() {});
                    aliexpressProductCategoryLabelItemService.updateByPrimaryKeySelective(aliexpressProductCategoryLabelItem);
                    return ApiResult.newSuccess(aliexpressProductCategoryLabelItem);
                }
        }
        return ApiResult.newSuccess();
    }
}
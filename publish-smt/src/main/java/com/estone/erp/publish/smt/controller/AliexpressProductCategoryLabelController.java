package com.estone.erp.publish.smt.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.estone.erp.common.model.api.ApiRequestParam;
import com.estone.erp.common.model.api.ApiR<PERSON>ult;
import com.estone.erp.common.model.api.CQuery;
import com.estone.erp.common.model.api.CQueryResult;
import com.estone.erp.common.mq.PublishQueues;
import com.estone.erp.common.mq.RabbitMqSender;
import com.estone.erp.common.util.Asserts;
import com.estone.erp.common.util.CommonUtils;
import com.estone.erp.common.util.WebUtils;
import com.estone.erp.publish.common.ErrorCode;
import com.estone.erp.publish.common.util.ExcelUtils;
import com.estone.erp.publish.common.util.POIUtils;
import com.estone.erp.publish.mq.PublishMqConfig;
import com.estone.erp.publish.smt.enums.LabelTypeEnum;
import com.estone.erp.publish.smt.enums.ProductCategoryLabelTypeEnum;
import com.estone.erp.publish.smt.model.*;
import com.estone.erp.publish.smt.model.dto.AliexpressProductCategoryLabelDto;
import com.estone.erp.publish.smt.mq.model.ProductCategoryLabel;
import com.estone.erp.publish.smt.service.AliexpressProductCategoryLabelItemService;
import com.estone.erp.publish.smt.service.AliexpressProductCategoryLabelService;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.estone.erp.publish.smt.service.AliexpressProductCategoryLabelSkuService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;

import java.sql.Timestamp;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR> aliexpress_product_category_label
 * 2023-09-11 16:08:19
 */
@RestController
@Slf4j
@RequestMapping("aliexpressProductCategoryLabel")
public class AliexpressProductCategoryLabelController {
    @Resource
    private AliexpressProductCategoryLabelService aliexpressProductCategoryLabelService;

    @Resource
    private AliexpressProductCategoryLabelItemService aliexpressProductCategoryLabelItemService;

    @Resource
    private AliexpressProductCategoryLabelSkuService aliexpressProductCategoryLabelSkuService;

    @Resource
    private RabbitMqSender rabbitMqSender;

    @PostMapping
    public ApiResult<?> postAliexpressProductCategoryLabel(@RequestBody(required = true) ApiRequestParam<String> requestParam) {
        String method = requestParam.getMethod();
        if (StringUtils.isNotBlank(method)) {
            switch (method) {
                case "searchAliexpressProductCategoryLabel": // 查询列表
                    CQuery<AliexpressProductCategoryLabelCriteria> cquery = requestParam.getArgsValue(new TypeReference<CQuery<AliexpressProductCategoryLabelCriteria>>() {});
                    Asserts.isTrue(cquery != null, ErrorCode.PARAM_EMPTY_ERROR);
                    CQueryResult<AliexpressProductCategoryLabel> results = aliexpressProductCategoryLabelService.search(cquery);
                    return results;
                case "addAliexpressProductCategoryLabel": // 添加
                    AliexpressProductCategoryLabel aliexpressProductCategoryLabel = requestParam.getArgsValue(new TypeReference<AliexpressProductCategoryLabel>() {});
                    aliexpressProductCategoryLabelService.insert(aliexpressProductCategoryLabel);
                    return ApiResult.newSuccess(aliexpressProductCategoryLabel);
                }
        }
        return ApiResult.newSuccess();
    }

    public void sendMQ(ProductCategoryLabel categoryLable){
        try{
            rabbitMqSender.send(PublishMqConfig.SMT_API_DIRECT_EXCHANGE, PublishQueues.SMT_PRODUCT_CATEGORY_LABEL_ROUTE_KEY, JSON
                    .toJSON(categoryLable));
        }catch (Exception e){
            log.error("发送队列失败 " + e.getMessage(), e);
        }
    }

    /**
     * 新增
     * @param labelDto
     * @return
     */
    @PostMapping(value = "/add")
    public ApiResult<?> excelImport(@RequestBody AliexpressProductCategoryLabelDto labelDto){
        if(labelDto == null || StringUtils.isBlank(labelDto.getType())){
            return ApiResult.newError("参数不能为空！");
        }

        if(!Arrays.asList(ProductCategoryLabelTypeEnum.category.getType(), ProductCategoryLabelTypeEnum.item.getType(), ProductCategoryLabelTypeEnum.sku.getType()).contains(labelDto.getType())){
            return ApiResult.newError("type错误！");
        }

        String type = labelDto.getType();
        if(StringUtils.equalsIgnoreCase(type, ProductCategoryLabelTypeEnum.category.getType())){
            AliexpressProductCategoryLabel categoryLabel = new AliexpressProductCategoryLabel();
            categoryLabel.setLabelType(labelDto.getLabelType());
            categoryLabel.setCategoryId(labelDto.getCategoryId());
            categoryLabel.setOneCategoryName(labelDto.getOneCategoryName());
            categoryLabel.setTwoCategoryName(labelDto.getTwoCategoryName());
            categoryLabel.setThreeCategoryName(labelDto.getThreeCategoryName());
            categoryLabel.setChildCategoryName(labelDto.getChildCategoryName());

            AliexpressProductCategoryLabelExample labelExample = new AliexpressProductCategoryLabelExample();
            AliexpressProductCategoryLabelExample.Criteria criteria = labelExample.createCriteria();
            criteria.andCategoryIdEqualTo(labelDto.getCategoryId());
            criteria.andLabelTypeEqualTo(labelDto.getLabelType());
            List<AliexpressProductCategoryLabel> aliexpressProductCategoryLabels = aliexpressProductCategoryLabelService.selectByExample(labelExample);
            if(CollectionUtils.isNotEmpty(aliexpressProductCategoryLabels)){
                return ApiResult.newError("数据已存在 不允许重复添加！");
            }

            categoryLabel.setCreateBy(WebUtils.getUserName());
            categoryLabel.setCreateDate(new Timestamp(System.currentTimeMillis()));
            aliexpressProductCategoryLabelService.insert(categoryLabel);

            //需要删除 不需要重新发送队列 新增的数据队列会修改
            if(Arrays.asList(LabelTypeEnum.no_other.getType(), LabelTypeEnum.other.getType()).contains(labelDto.getLabelType())){
                String deleteLatelType = StringUtils.equalsIgnoreCase(LabelTypeEnum.no_other.getType(), labelDto.getLabelType()) ? LabelTypeEnum.other.getType() : LabelTypeEnum.no_other.getType();
                Long categoryId = labelDto.getCategoryId();
                AliexpressProductCategoryLabelExample deleteExample = new AliexpressProductCategoryLabelExample();
                deleteExample.createCriteria().andLabelTypeEqualTo(deleteLatelType).andCategoryIdEqualTo(categoryId);
                List<AliexpressProductCategoryLabel> deleteList = aliexpressProductCategoryLabelService.selectByExample(deleteExample);
                if(CollectionUtils.isNotEmpty(deleteList)){
                    aliexpressProductCategoryLabelService.deleteByPrimaryKey(Arrays.asList(deleteList.get(0).getId()));
                }
            }
            //发送队列
            sendMQ(new ProductCategoryLabel(labelDto.getLabelType(), labelDto.getCategoryId().toString(), ProductCategoryLabelTypeEnum.category.getType(), true));

        }else if(StringUtils.equalsIgnoreCase(type, ProductCategoryLabelTypeEnum.item.getType())){
            AliexpressProductCategoryLabelItemExample labelItemExample = new AliexpressProductCategoryLabelItemExample();
            String productIdStr = labelDto.getProductIdStr();
            if(StringUtils.isBlank(productIdStr)){
                return ApiResult.newError("参数为空!");
            }
            List<Long> values = CommonUtils.splitLongList(productIdStr, ",");
            labelItemExample.createCriteria().andProductIdIn(values);
            List<AliexpressProductCategoryLabelItem> aliexpressProductCategoryLabelItems = aliexpressProductCategoryLabelItemService.selectByExample(labelItemExample);
            if(CollectionUtils.isNotEmpty(aliexpressProductCategoryLabelItems)){
                List<Long> collect = aliexpressProductCategoryLabelItems.stream().map(t -> t.getProductId()).collect(Collectors.toList());
                return ApiResult.newError(StringUtils.join(collect, ",") +  "数据已存在 不允许重复添加！");
            }
            for (Long value : values) {
                try {
                    AliexpressProductCategoryLabelItem aliexpressProductCategoryLabelItem = new AliexpressProductCategoryLabelItem();
                    aliexpressProductCategoryLabelItem.setProductId(value);
                    aliexpressProductCategoryLabelItem.setCreateBy(WebUtils.getUserName());
                    aliexpressProductCategoryLabelItem.setCreateDate(new Timestamp(System.currentTimeMillis()));
                    aliexpressProductCategoryLabelItemService.insert(aliexpressProductCategoryLabelItem);
                    //发送队列
                    sendMQ(new ProductCategoryLabel(LabelTypeEnum.online_cainiao.getType(), value.toString(), ProductCategoryLabelTypeEnum.item.getType(), true));
                } catch (Exception e) {
                    log.error(e.getMessage(), e);
                }
            }
        }else if(StringUtils.equalsIgnoreCase(type, ProductCategoryLabelTypeEnum.sku.getType())){
            String skuStr = labelDto.getSkuStr();
            if(StringUtils.isBlank(skuStr)){
                return ApiResult.newError("参数为空!");
            }
            AliexpressProductCategoryLabelSkuExample skuExample = new AliexpressProductCategoryLabelSkuExample();
            List<String> strings = CommonUtils.splitList(skuStr, ",");
            skuExample.createCriteria().andSkuIn(strings);
            List<AliexpressProductCategoryLabelSku> aliexpressProductCategoryLabelSkus = aliexpressProductCategoryLabelSkuService.selectByExample(skuExample);
            if(CollectionUtils.isNotEmpty(aliexpressProductCategoryLabelSkus)){
                List<String> collect = aliexpressProductCategoryLabelSkus.stream().map(t -> t.getSku()).collect(Collectors.toList());
                return ApiResult.newError(StringUtils.join(collect, ",") +  "数据已存在 不允许重复添加！");
            }

            for (String string : strings) {
                try {
                    AliexpressProductCategoryLabelSku labelSku = new AliexpressProductCategoryLabelSku();
                    labelSku.setSku(string);
                    labelSku.setCreateBy(WebUtils.getUserName());
                    labelSku.setCreateDate(new Timestamp(System.currentTimeMillis()));
                    aliexpressProductCategoryLabelSkuService.insert(labelSku);
                    //发送队列
                    sendMQ(new ProductCategoryLabel(LabelTypeEnum.online_cainiao.getType(), string, ProductCategoryLabelTypeEnum.sku.getType(), true));
                } catch (Exception e) {
                    log.error(e.getMessage(), e);
                }
            }
        }
        return ApiResult.newSuccess();
    }

    /**
     * 删除
     * @param labelDto
     * @return
     */
    @PostMapping(value = "/delete")
    public ApiResult<?> delete(@RequestBody AliexpressProductCategoryLabelDto labelDto){
        if(labelDto == null || StringUtils.isBlank(labelDto.getType()) || CollectionUtils.isEmpty(labelDto.getIdList())){
            return ApiResult.newError("参数不能为空！");
        }
        String type = labelDto.getType();
        if(!Arrays.asList(ProductCategoryLabelTypeEnum.category.getType(), ProductCategoryLabelTypeEnum.item.getType(), ProductCategoryLabelTypeEnum.sku.getType()).contains(labelDto.getType())){
            return ApiResult.newError("type错误！");
        }

        List<Integer> idList = labelDto.getIdList();
        if(StringUtils.equalsIgnoreCase(type, ProductCategoryLabelTypeEnum.category.getType())){
            for (Integer integer : idList) {
                AliexpressProductCategoryLabel categoryLabel = aliexpressProductCategoryLabelService.selectByPrimaryKey(integer);
                if(categoryLabel != null){
                    aliexpressProductCategoryLabelService.deleteByPrimaryKey(Arrays.asList(integer));
                    sendMQ(new ProductCategoryLabel(categoryLabel.getLabelType(), categoryLabel.getCategoryId().toString(), ProductCategoryLabelTypeEnum.category.getType(), false));
                }
            }
        }else if(StringUtils.equalsIgnoreCase(type, ProductCategoryLabelTypeEnum.item.getType())){
            for (Integer integer : idList) {
                AliexpressProductCategoryLabelItem aliexpressProductCategoryLabelItem = aliexpressProductCategoryLabelItemService.selectByPrimaryKey(integer);
                if(aliexpressProductCategoryLabelItem != null){
                    aliexpressProductCategoryLabelItemService.deleteByPrimaryKey(Arrays.asList(integer));
                    sendMQ(new ProductCategoryLabel(LabelTypeEnum.online_cainiao.getType(), aliexpressProductCategoryLabelItem.getProductId().toString(), ProductCategoryLabelTypeEnum.item.getType(), false));
                }
            }
        }else if(StringUtils.equalsIgnoreCase(type, ProductCategoryLabelTypeEnum.sku.getType())){
            for (Integer integer : idList) {
                AliexpressProductCategoryLabelSku labelSku = aliexpressProductCategoryLabelSkuService.selectByPrimaryKey(integer);
                if(labelSku != null){
                    aliexpressProductCategoryLabelSkuService.deleteByPrimaryKey(Arrays.asList(integer));
                    sendMQ(new ProductCategoryLabel(LabelTypeEnum.online_cainiao.getType(), labelSku.getSku(), ProductCategoryLabelTypeEnum.sku.getType(), false));
                }
            }
        }
        return ApiResult.newSuccess();
    }

    /**
     * excle 导入
     * @param multiPartFile
     * @param type
     * @param request
     * @param response
     * @return
     */
    @PostMapping(value = "/excel/import/{type}/{labelType}")
    public ApiResult<?> excelImport(@RequestParam(value = "file", required = false) MultipartFile multiPartFile,
                                         @PathVariable(value = "type", required = true) String type,
                                    @PathVariable(value = "labelType", required = true) String labelType,
                                         HttpServletRequest request, HttpServletResponse response){
        try{
            MultipartFile file = null;
            MultipartHttpServletRequest multiRequest = (MultipartHttpServletRequest) request;
            Map fileMap = multiRequest.getFileMap();
            if (fileMap.values().size() > 0) {
                file = (MultipartFile) fileMap.values().iterator().next();
            }
            else {
                throw new Exception("请先上传文件");
            }
            if(file == null || file.getSize() == 0) {
                return ApiResult.of(false, null, "上传失败 文件为空!");
            }

            final String[] categoryForm = {"类目id", "一级", "二级", "三级", "叶子类目名称"};
            final String[] itemForm = {"商品id"};
            final String[] skuForm = {"SKU"};

            String[] form = categoryForm;
            if(StringUtils.equalsIgnoreCase(type, ProductCategoryLabelTypeEnum.item.getType())){
                form = itemForm;
            }else if(StringUtils.equalsIgnoreCase(type, ProductCategoryLabelTypeEnum.sku.getType())){
                form = skuForm;
            }
            String userName = WebUtils.getUserName();
            POIUtils.readExcelSheet1(form, file, row -> {
                try{
                    if(row == null){
                        return null;
                    }
                    //类目
                    if(StringUtils.equalsIgnoreCase(type, ProductCategoryLabelTypeEnum.category.getType())){
                        String category_id = ExcelUtils.getCellValue(row.getCell(0)).trim();
                        String one_category_name = ExcelUtils.getCellValue(row.getCell(1)).trim();
                        String two_category_name = ExcelUtils.getCellValue(row.getCell(2)).trim();
                        String three_category_name = ExcelUtils.getCellValue(row.getCell(3)).trim();
                        String child_category_name = ExcelUtils.getCellValue(row.getCell(4)).trim();
                        if(StringUtils.isBlank(labelType) || StringUtils.isBlank(category_id)
                                || StringUtils.isBlank(one_category_name) || StringUtils.isBlank(two_category_name)
                                || StringUtils.isBlank(three_category_name) || StringUtils.isBlank(child_category_name)){
                            return null;
                        }
                        if(!Arrays.asList(LabelTypeEnum.other.getType(), LabelTypeEnum.no_other.getType(), LabelTypeEnum.online_cainiao.getType()).contains(labelType)){
                            return null;
                        }

                        AliexpressProductCategoryLabel categoryLabel = new AliexpressProductCategoryLabel();
                        categoryLabel.setLabelType(labelType);
                        categoryLabel.setCategoryId(Long.valueOf(category_id));
                        categoryLabel.setOneCategoryName(one_category_name);
                        categoryLabel.setTwoCategoryName(two_category_name);
                        categoryLabel.setThreeCategoryName(three_category_name);
                        categoryLabel.setChildCategoryName(child_category_name);

                        AliexpressProductCategoryLabelExample labelExample = new AliexpressProductCategoryLabelExample();
                        AliexpressProductCategoryLabelExample.Criteria criteria = labelExample.createCriteria();
                        criteria.andCategoryIdEqualTo(Long.valueOf(category_id));
                        criteria.andLabelTypeEqualTo(labelType);
                        List<AliexpressProductCategoryLabel> aliexpressProductCategoryLabels = aliexpressProductCategoryLabelService.selectByExample(labelExample);
                        if(CollectionUtils.isEmpty(aliexpressProductCategoryLabels)){
                            //新增
                            categoryLabel.setCreateBy(userName);
                            categoryLabel.setCreateDate(new Timestamp(System.currentTimeMillis()));
                            aliexpressProductCategoryLabelService.insert(categoryLabel);

                            //需要删除 不需要重新发送队列 新增的数据队列会修改
                            if(Arrays.asList(LabelTypeEnum.no_other.getType(), LabelTypeEnum.other.getType()).contains(labelType)){
                                String deleteLatelType = StringUtils.equalsIgnoreCase(LabelTypeEnum.no_other.getType(), labelType) ? LabelTypeEnum.other.getType() : LabelTypeEnum.no_other.getType();
                                AliexpressProductCategoryLabelExample deleteExample = new AliexpressProductCategoryLabelExample();
                                deleteExample.createCriteria().andLabelTypeEqualTo(deleteLatelType).andCategoryIdEqualTo(Long.valueOf(category_id));
                                List<AliexpressProductCategoryLabel> deleteList = aliexpressProductCategoryLabelService.selectByExample(deleteExample);
                                if(CollectionUtils.isNotEmpty(deleteList)){
                                    aliexpressProductCategoryLabelService.deleteByPrimaryKey(Arrays.asList(deleteList.get(0).getId()));
                                }
                            }

                            //发送队列
                            sendMQ(new ProductCategoryLabel(categoryLabel.getLabelType(), categoryLabel.getCategoryId().toString(), ProductCategoryLabelTypeEnum.category.getType(), true));
                        }else{
                            categoryLabel.setId(aliexpressProductCategoryLabels.get(0).getId());
                            categoryLabel.setLastUpdateBy(userName);
                            categoryLabel.setLastUpdateDate(new Timestamp(System.currentTimeMillis()));
                            aliexpressProductCategoryLabelService.updateByPrimaryKeySelective(categoryLabel);
                        }
                    }else if(StringUtils.equalsIgnoreCase(type, ProductCategoryLabelTypeEnum.item.getType())){
                        String itemId = ExcelUtils.getCellValue(row.getCell(0)).trim();
                        if(StringUtils.isBlank(itemId)){
                            return null;
                        }
                        AliexpressProductCategoryLabelItemExample labelItemExample = new AliexpressProductCategoryLabelItemExample();
                        labelItemExample.createCriteria().andProductIdEqualTo(Long.valueOf(itemId));
                        List<AliexpressProductCategoryLabelItem> aliexpressProductCategoryLabelItems = aliexpressProductCategoryLabelItemService.selectByExample(labelItemExample);
                        if(CollectionUtils.isEmpty(aliexpressProductCategoryLabelItems)){
                            AliexpressProductCategoryLabelItem aliexpressProductCategoryLabelItem = new AliexpressProductCategoryLabelItem();
                            aliexpressProductCategoryLabelItem.setProductId(Long.valueOf(itemId));
                            aliexpressProductCategoryLabelItem.setCreateBy(userName);
                            aliexpressProductCategoryLabelItem.setCreateDate(new Timestamp(System.currentTimeMillis()));
                            aliexpressProductCategoryLabelItemService.insert(aliexpressProductCategoryLabelItem);
                            //发送队列
                            sendMQ(new ProductCategoryLabel(LabelTypeEnum.online_cainiao.getType(), itemId, ProductCategoryLabelTypeEnum.item.getType(), true));
                        }
                    }else if(StringUtils.equalsIgnoreCase(type, ProductCategoryLabelTypeEnum.sku.getType())){
                        String sku = ExcelUtils.getCellValue(row.getCell(0)).trim();
                        if(StringUtils.isBlank(sku)){
                            return null;
                        }

                        AliexpressProductCategoryLabelSkuExample skuExample = new AliexpressProductCategoryLabelSkuExample();
                        skuExample.createCriteria().andSkuEqualTo(sku);
                        List<AliexpressProductCategoryLabelSku> aliexpressProductCategoryLabelSkus = aliexpressProductCategoryLabelSkuService.selectByExample(skuExample);
                        if(CollectionUtils.isEmpty(aliexpressProductCategoryLabelSkus)){
                            AliexpressProductCategoryLabelSku labelSku = new AliexpressProductCategoryLabelSku();
                            labelSku.setSku(sku);
                            labelSku.setCreateBy(userName);
                            labelSku.setCreateDate(new Timestamp(System.currentTimeMillis()));
                            aliexpressProductCategoryLabelSkuService.insert(labelSku);
                            //发送队列
                            sendMQ(new ProductCategoryLabel(LabelTypeEnum.online_cainiao.getType(), sku, ProductCategoryLabelTypeEnum.sku.getType(), true));
                        }
                    }
//                    return row;
                }catch (Exception e){
                    log.error(e.getMessage(), e);
                    return ApiResult.newError("导入失败-" + e.getMessage());
                }
                return null;
            }, false);

        }catch (Exception e){
            log.error(e.getMessage(), e);
            return ApiResult.newError("导入失败-" + e.getMessage());
        }
        return ApiResult.newSuccess();
    }


    @GetMapping(value = "/{id}")
    public ApiResult<?> getAliexpressProductCategoryLabel(@PathVariable(value = "id", required = true) Integer id) {
        AliexpressProductCategoryLabel aliexpressProductCategoryLabel = aliexpressProductCategoryLabelService.selectByPrimaryKey(id);
        return ApiResult.newSuccess(aliexpressProductCategoryLabel);
    }

    @PutMapping(value = "/{id}")
    public ApiResult<?> putAliexpressProductCategoryLabel(@PathVariable(value = "id", required = true) Integer id, @RequestBody ApiRequestParam<String> requestParam) {
        String method = requestParam.getMethod();
        if (StringUtils.isNotBlank(method)) {
            switch (method) {
                case "updateAliexpressProductCategoryLabel": // 单个修改
                    AliexpressProductCategoryLabel aliexpressProductCategoryLabel = requestParam.getArgsValue(new TypeReference<AliexpressProductCategoryLabel>() {});
                    aliexpressProductCategoryLabelService.updateByPrimaryKeySelective(aliexpressProductCategoryLabel);
                    return ApiResult.newSuccess(aliexpressProductCategoryLabel);
                }
        }
        return ApiResult.newSuccess();
    }
}
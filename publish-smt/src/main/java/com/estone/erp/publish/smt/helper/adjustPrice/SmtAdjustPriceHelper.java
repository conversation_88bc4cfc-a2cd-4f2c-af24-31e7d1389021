package com.estone.erp.publish.smt.helper.adjustPrice;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.estone.erp.common.exception.BusinessException;
import com.estone.erp.common.util.NumberUtils;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.common.json.ResponseJson;
import com.estone.erp.publish.elasticsearch.util.EsAccountUtils;
import com.estone.erp.publish.elasticsearch2.model.EsAliexpressProductListing;
import com.estone.erp.publish.feginService.modle.BatchPriceCalculatorResponse;
import com.estone.erp.publish.smt.model.*;
import com.estone.erp.publish.smt.service.AliexpressEsExtendService;
import com.estone.erp.publish.smt.util.AliexpressCalcPriceUtil;
import com.estone.erp.publish.system.account.modle.SalesmanAccountDetail;
import com.estone.erp.publish.tidb.publishtidb.model.SmtAdjustPriceItemPool;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;


@Slf4j
@Component
public class SmtAdjustPriceHelper {
    @Resource
    private AliexpressEsExtendService aliexpressEsExtendService;

    /**
     *  用毛利率计算价格
     * @param esSmtItems
     * @param matchedConfig
     * @param configGrossProfitRate
     * @return
     */
    public ResponseJson calcPrice(List<EsAliexpressProductListing> esSmtItems, AliexpressConfigInfo matchedConfig, String configGrossProfitRate) {
        List<String> articleNumberList = esSmtItems.stream().map(t -> t.getArticleNumber()).collect(Collectors.toList());
        String shippingMethod = matchedConfig.getShippingMethod();
        String countryCode = matchedConfig.getCountryCode();
        String currencyCode = esSmtItems.get(0).getCurrencyCode();
        return AliexpressCalcPriceUtil.publishCalc(articleNumberList, shippingMethod, Double.valueOf(configGrossProfitRate), countryCode, currencyCode);
    }

    /**
     * 计算区域价格 并设置改后区域价格
     * @param esSmtItems
     * @param matchedConfig
     * @param configGrossProfitRate
     */
    public List<SmtAdjustPriceItemPool> calcAreaPrice(List<SmtAdjustPriceItemPool> smtAdjustPriceItemPools, List<EsAliexpressProductListing> esSmtItems, AliexpressConfigInfo matchedConfig, String configGrossProfitRate) {
        EsAliexpressProductListing esProduct = esSmtItems.get(0);
        String aliexpressAccountNumber = esProduct.getAliexpressAccountNumber();
        Long productId = esProduct.getProductId();
        Long configFreightTemplateId = matchedConfig.getFreightTemplateId();

        String afterTemplateName = smtAdjustPriceItemPools.get(0).getAfterTemplateName();
        if (StringUtils.isBlank(afterTemplateName)) {
            log.info("产品id:{} 运费模板id:{} 匹配物流名称为空！", productId, configFreightTemplateId);
            return smtAdjustPriceItemPools;
        }

        Map<String, SalesmanAccountDetail> salesmanAccountMap = EsAccountUtils.getSalesmanAccountDetailMapByEs(Arrays.asList(aliexpressAccountNumber), SaleChannel.CHANNEL_SMT);
        String createBy = "";
        SalesmanAccountDetail salesmanAccountDetail = salesmanAccountMap.get(aliexpressAccountNumber);
        if(salesmanAccountDetail == null || salesmanAccountDetail.getSalesmanSet() == null || salesmanAccountDetail.getSalesmanSet().isEmpty()){
            throw new BusinessException("店铺关联的销售为空！");
        }
        createBy = new ArrayList<>(salesmanAccountDetail.getSalesmanSet()).get(0);

        //组装产品改前区域价格，和改后区域价格
        List<AliInternationalCaleConfig> aliInternationalCaleConfigs = AliexpressCalcPriceUtil.getPriorityCaleConfigs(afterTemplateName, createBy);
        //-----------------------------------------------------
        if (CollectionUtils.isEmpty(aliInternationalCaleConfigs)) {
            String message = "产品id:" + productId + " 配置名称 " + afterTemplateName + "算价物流配置不存在！";
            log.info(message);
            return smtAdjustPriceItemPools;
        }

        AliexpressEsExtend aliexpressEsExtend = aliexpressEsExtendService.selectByAccountandProductId(aliexpressAccountNumber, productId);

        //区域价格
        JSONObject statePriceConfigObj = JSON.parseObject(aliexpressEsExtend.getAeopNationalQuoteConfiguration());
        if(statePriceConfigObj == null){
            throw new BusinessException("产品区域价格为空！");
        }
        JSONArray priceConfigArray = statePriceConfigObj.getJSONArray("configuration_data");
        Set<String> shiptoCountrySet = new HashSet<>();
        for (int i = 0; i < priceConfigArray.size(); i++) {
            JSONObject resultSku = priceConfigArray.getJSONObject(i);
            String shiptoCountry = resultSku.getString("shiptoCountry");
            shiptoCountrySet.add(shiptoCountry);
        }
        //需要计算区域调价的国家
        List<String> updateCountryCodeList = new ArrayList<>(shiptoCountrySet);

        //计算价格
        ResponseJson areaCaleJson = AliexpressCalcPriceUtil.product28CalcForEs(esSmtItems, afterTemplateName, createBy, Double.valueOf(configGrossProfitRate), updateCountryCodeList);
        if (!areaCaleJson.isSuccess()) {
            throw new BusinessException("毛利计算出错：" + areaCaleJson.getMessage());
        }
        //es主键 对应的区域国家调价集合
        Map<String, List<BatchPriceCalculatorResponse>> map = (Map<String, List<BatchPriceCalculatorResponse>>)areaCaleJson.getBody().get("key");
        for (Map.Entry<String, List<BatchPriceCalculatorResponse>> stringListEntry : map.entrySet()) {
            List<BatchPriceCalculatorResponse> value = stringListEntry.getValue();
            for (BatchPriceCalculatorResponse calcRsp : value) {
                if(!calcRsp.getIsSuccess()){
                    throw new BusinessException("算价异常:" + calcRsp.getErrorMsg());
                }
            }
        }
        //配置的折扣价
        Double configDiscountRate = matchedConfig.getDiscountRate();
        Map<String, SmtAdjustPriceItemPool> adjustPriceItemPoolMap = smtAdjustPriceItemPools.stream().collect(Collectors.toMap(k -> k.getProductId() + "-" + k.getSkuCode(), v -> v, (k1, k2) -> k1));

        //国家对应的店铺折扣
        Map<String, Double> codeDiscountRateMap = new HashMap<>();
        for (AliInternationalCaleConfig caleConfig : aliInternationalCaleConfigs) {
            String countryCode = caleConfig.getCountryCode();
            Double discountRate = caleConfig.getDiscountRate();
            if(configDiscountRate != null){
                codeDiscountRateMap.put(countryCode, configDiscountRate);
            }else{
                codeDiscountRateMap.put(countryCode, discountRate);
            }
        }
        for (EsAliexpressProductListing productListing : esSmtItems) {
            String id = productListing.getId();
            List<BatchPriceCalculatorResponse> batchPriceCalculatorResponses = map.get(id);
            //先用毛利算出来价格X，折扣价=X/(1-折扣)
            //单个产品国家对于的价格
            Map<String, Double> afterCountryCodeMap = new HashMap<>();
            for (BatchPriceCalculatorResponse batchPriceCalculatorRespons : batchPriceCalculatorResponses) {
                String countryCode = batchPriceCalculatorRespons.getCountryCode();
                Double foreignPrice = batchPriceCalculatorRespons.getForeignPrice();
                //全部按照直接调价上传
                Double discountRate = codeDiscountRateMap.get(countryCode);
                if(discountRate == null){
                    discountRate = 0.0d;
                }
                foreignPrice = NumberUtils.round(foreignPrice /(1- discountRate) , 2);

                if(foreignPrice <= 0.0d){
                    throw new BusinessException(id + countryCode + "改后区域价格不能小于等于0 " + foreignPrice);
                }

                //有折扣 类型转换
                afterCountryCodeMap.put(countryCode, foreignPrice);
            }

            String productKey = productId + "-" + productListing.getSkuCode();
            SmtAdjustPriceItemPool smtAdjustPriceItemPool = adjustPriceItemPoolMap.get(productKey);
            smtAdjustPriceItemPool.setAfterAreaPrice(JSON.toJSONString(afterCountryCodeMap));
        }
        return smtAdjustPriceItemPools;
    }



}

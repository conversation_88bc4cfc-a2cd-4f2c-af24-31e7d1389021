package com.estone.erp.publish.smt.mq.excel.impl;

import com.estone.erp.publish.common.json.ResponseJson;
import com.estone.erp.publish.smt.enums.ExcelTypeEnum;
import com.estone.erp.publish.smt.mq.excel.ExcelAbstract;
import com.estone.erp.publish.smt.mq.excel.config.ExcelContext;
import com.estone.erp.publish.smt.mq.excel.utils.ExcelOperationUtils;
import lombok.extern.slf4j.Slf4j;


/**
 * <AUTHOR>
 * @description:
 * @date 2022/1/1110:54
 */
@Slf4j
public class ExcelTgStockImpl extends ExcelAbstract {

    @Override
    public ResponseJson execute(ExcelContext context) {
        return ExcelOperationUtils.excelOperation(context);
    }

    @Override
    public int type() {
        return ExcelTypeEnum.tgStock.getCode();
    }
}

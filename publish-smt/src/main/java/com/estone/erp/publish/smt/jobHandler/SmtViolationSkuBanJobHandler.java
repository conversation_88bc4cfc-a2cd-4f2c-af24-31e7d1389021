package com.estone.erp.publish.smt.jobHandler;

import com.estone.erp.common.jobHandler.AbstractJobHandler;
import com.estone.erp.publish.smt.service.SmtViolationPunishRecordService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * SMT违规处罚SKU禁售规则自动处理定时任务
 * 
 * 功能说明：
 * 1. 根据违规处罚列表的设置SKU禁售规则且启用状态的规则，匹配违规处罚列表数据
 * 2. 若数据设置禁售为是的数据，过滤不处理
 * 3. 若列表数据与规则匹配，将对应SKU标记禁售，记录设置禁售时间
 * 4. 列表SKU为空的数据，过滤不处理
 * 5. 若某SKU被标记禁售后，违规处罚列表所有相同的SKU均标记禁售
 * 6. 记录是否设置禁售、设置禁售时间，并将规则对应的禁售类型和禁售原因进行标记
 * 7. 无需考虑其他相同的SKU是否符合设置的SKU禁售规则
 * 
 * <AUTHOR>
 * @date 2025-07-22
 */
@Component
@Slf4j
public class SmtViolationSkuBanJobHandler extends AbstractJobHandler {

    @Resource
    private SmtViolationPunishRecordService smtViolationPunishRecordService;

    public SmtViolationSkuBanJobHandler() {
        super("SmtViolationSkuBanJobHandler");
    }

    @Override
    @XxlJob("SmtViolationSkuBanJobHandler")
    public ReturnT<String> run(String param) throws Exception {
        XxlJobLogger.log("-------SMT违规处罚SKU禁售规则自动处理任务开始--------");
        
        try {
            // 执行SKU禁售规则自动处理
            String result = smtViolationPunishRecordService.processSkuBanByRules();
            
            XxlJobLogger.log("任务执行结果: {}", result);
            XxlJobLogger.log("-------SMT违规处罚SKU禁售规则自动处理任务结束--------");
            
            return ReturnT.SUCCESS;
            
        } catch (Exception e) {
            log.error("SMT违规处罚SKU禁售规则自动处理任务执行失败", e);
            XxlJobLogger.log("任务执行失败: {}", e.getMessage());
            return new ReturnT<>(ReturnT.FAIL_CODE, "任务执行失败: " + e.getMessage());
        }
    }
}

package com.estone.erp.publish.smt.jobHandler;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.estone.erp.common.jobHandler.AbstractJobHandler;
import com.estone.erp.common.mq.PublishQueues;
import com.estone.erp.common.mq.RabbitMqSender;
import com.estone.erp.common.util.CommonUtils;
import com.estone.erp.common.util.PagingUtils;
import com.estone.erp.common.util.PublishRedisClusterUtils;
import com.estone.erp.common.util.StrUtil;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.mq.PublishMqConfig;
import com.estone.erp.publish.smt.bean.HalfTgItemRequest;
import com.estone.erp.publish.smt.enums.ListingConfigTypeEnum;
import com.estone.erp.publish.smt.enums.ProductStatusTypeEnum;
import com.estone.erp.publish.smt.enums.UpdateStockResultEnum;
import com.estone.erp.publish.smt.enums.UpdateStockTypeEnum;
import com.estone.erp.publish.smt.model.*;
import com.estone.erp.publish.smt.mq.bean.UpdateHalfStockMqBean;
import com.estone.erp.publish.smt.service.AliexpressListingConfigService;
import com.estone.erp.publish.smt.service.SmtStockUpdateFinalLogService;
import com.estone.erp.publish.smt.util.AliexpressLogUtils;
import com.estone.erp.publish.system.account.AccountUtils;
import com.estone.erp.publish.system.account.SaleAccountAndBusinessResponse;
import com.estone.erp.publish.tidb.publishtidb.model.AliexpressHalfTgItem;
import com.estone.erp.publish.tidb.publishtidb.model.AliexpressHalfTgItemExample;
import com.estone.erp.publish.tidb.publishtidb.service.AliexpressHalfTgItemService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 定时频率：没半个小时一次，并且下次执行的时候清除上次未执行完的数据，用redis时间控制
 *
 * 定时内容：
 *
 * 排除停产存档的SKU，
 *
 * 若产品系统库存：可用-待发小于等于2，则将半托管链接库存改为0.
 *
 * 若产品系统库存：可用-待发大于2小于等于100，则将半托管库存改为产品系统库存的三分之二，小数点向上取。 //取消
 *
 * 若产品系统库存：可用-待发大于100，且半托管链接库存小于100，则将半托管链接库存改为100.
 *
 * 变更 http://************:8080/browse/ES-7860
 *
 * 变更 http://************:8080/browse/ES-8291
 * <AUTHOR>
 * @description:
 * @date 2020/4/7 11:03
 */
@Component
@Slf4j
public class HalfTgUpdateStockZeroBySystemStockJobHandler extends AbstractJobHandler {
    @Resource
    private AliexpressHalfTgItemService aliexpressHalfTgItemService;
    @Resource
    private RabbitMqSender rabbitMqSender;
    @Resource
    private SmtStockUpdateFinalLogService smtStockUpdateFinalLogService;
    @Resource
    private AliexpressListingConfigService aliexpressListingConfigService;

    private static String redisKey = "HalfTgUpdateStockZeroBySystemStockJobHandler_execute_time";
    public String skulifecyclephase = "Archived,Stop";
    public String fields = "id, account, product_id, article_number, sku_code, sku_id, pop_choice_sku_warehouse_stock_list, system_stock, sku_stock, sku_status, usable_stock, smt_transfer_stock, system_usable_transfer_stock";
    public HalfTgUpdateStockZeroBySystemStockJobHandler() {
        super("HalfTgUpdateStockZeroBySystemStockJobHandler");
    }

    @Getter
    @Setter
    static class InnerParam{
        private List<String> accountList;
        private List<String> skuList;
        private Integer thresholdNumber = 2;
    }

    @Override
    @XxlJob("HalfTgUpdateStockZeroBySystemStockJobHandler")
    public ReturnT<String> run(String param) throws Exception {
        log.warn("*****************根据可用库存修改半托管库存*****************");
        String createLongTime = String.valueOf(System.currentTimeMillis()) ;
        PublishRedisClusterUtils.set(redisKey, createLongTime);

        // 解析param
        InnerParam innerParam = new InnerParam();
        if(StringUtils.isNotBlank(param)){
            innerParam = super.passParam(param, InnerParam.class);
            if(null == innerParam) {
                XxlJobLogger.log("请检查参数" + param);
                return ReturnT.FAIL;
            }
        }
        List<SaleAccountAndBusinessResponse> saleAccountList = AccountUtils
                .getSaleAccountListBySaleChannel(SaleChannel.CHANNEL_SMT);
        saleAccountList = saleAccountList.stream().filter(t->t.getColBool3() != null && t.getColBool3()).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(saleAccountList)){
            XxlJobLogger.log("没有需要执行的店铺");
            return ReturnT.SUCCESS;
        }
        List<String> accountList = innerParam.getAccountList();
        if(CollectionUtils.isNotEmpty(accountList)){
            saleAccountList = saleAccountList.stream().filter(t -> accountList.contains(t.getAccountNumber())).collect(Collectors.toList());
        }

        //过滤新规则的店铺
        if(CollectionUtils.isNotEmpty(saleAccountList)){
            AliexpressListingConfigExample configExample = new AliexpressListingConfigExample();
            configExample.createCriteria().andRuleTypeEqualTo(ListingConfigTypeEnum.pop_adjust_inventory.intCode());
            List<AliexpressListingConfig> aliexpressListingConfigs = aliexpressListingConfigService.selectByExample(configExample);
            Set<String> newRuleAccountSet = new HashSet<>();
            newRuleAccountSet = aliexpressListingConfigs.stream().map(t -> CommonUtils.splitList(StrUtil.strDeldComma(t.getStoreInformation()), ",")).flatMap(Collection::stream).collect(Collectors.toSet());
            Set<String> finalNewRuleAccountSet = newRuleAccountSet;
            if(CollectionUtils.isNotEmpty(finalNewRuleAccountSet)){
                for (String account : finalNewRuleAccountSet) {
                    XxlJobLogger.log("新规则存在不跑 " + account);
                }
                saleAccountList = saleAccountList.stream().filter(t -> !finalNewRuleAccountSet.contains(t.getAccountNumber())).collect(Collectors.toList());
            }
        }

        if(CollectionUtils.isEmpty(saleAccountList)){
            XxlJobLogger.log("没有需要执行的店铺");
            return ReturnT.SUCCESS;
        }
        List<String> skuList = innerParam.getSkuList();

        Map<String, SaleAccountAndBusinessResponse> accountMap = saleAccountList.stream().collect(Collectors.toMap(k -> k.getAccountNumber(), v -> v, (k1, k2) -> k1));

        //优先处理 系统库存小于等2的数据
        updateZero(saleAccountList, skuList, accountMap, createLongTime);
        updateTwo(saleAccountList, skuList, accountMap, createLongTime);
        updateThree(saleAccountList, skuList, accountMap, createLongTime);
//        updateFour(saleAccountList, skuList, accountMap, createLongTime);
        update(saleAccountList, skuList, accountMap, createLongTime);
        log.warn("*****************根据可用库存修改半托管库存 end*****************");
        return ReturnT.SUCCESS;
    }

    public void handleData(List<AliexpressHalfTgItem> dbList, Map<String, SaleAccountAndBusinessResponse> accountMap, String createLongTime, Integer updateStock){
        if(CollectionUtils.isEmpty(dbList)){
            return;
        }
        List<String> stopSkuStatusList = CommonUtils.splitList(skulifecyclephase, ",");
        //有可能没有状态 排除停产存档
        dbList = dbList.stream().filter(t -> StringUtils.isBlank(t.getSkuStatus()) || !stopSkuStatusList.contains(t.getSkuStatus())).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(dbList)){
            return;
        }

        List<SmtStockUpdateFinalLog> finalLogs = new ArrayList<>(); //记录日志

        Map<Long, List<AliexpressHalfTgItem>> productIdMap = dbList.stream().collect(Collectors.groupingBy(t -> t.getProductId()));
        for (Map.Entry<Long, List<AliexpressHalfTgItem>> longListEntry : productIdMap.entrySet()) {
            try {
                Long key = longListEntry.getKey();
                List<HalfTgItemRequest> requestsList = new ArrayList<>();
                List<AliexpressHalfTgItem> value = longListEntry.getValue();
                SaleAccountAndBusinessResponse saleAccountAndBusinessResponse = accountMap.get(value.get(0).getAccount());
                for (AliexpressHalfTgItem aliexpressHalfTgItem : value) {
                    String account = aliexpressHalfTgItem.getAccount();
                    Integer systemStock = aliexpressHalfTgItem.getSystemStock();

                    SmtStockUpdateFinalLog smtStockUpdateFinalLog = new SmtStockUpdateFinalLog();
                    smtStockUpdateFinalLog.setAccount(account);
                    smtStockUpdateFinalLog.setProductId(aliexpressHalfTgItem.getProductId());
                    smtStockUpdateFinalLog.setArticleNumber(aliexpressHalfTgItem.getArticleNumber());
                    smtStockUpdateFinalLog.setSkuId(aliexpressHalfTgItem.getSkuId());
                    smtStockUpdateFinalLog.setStockBefore(aliexpressHalfTgItem.getSkuStock());
                    smtStockUpdateFinalLog.setCreateDate(new Timestamp(System.currentTimeMillis()));
                    smtStockUpdateFinalLog.setSkuStatus(aliexpressHalfTgItem.getSkuStatus());
                    smtStockUpdateFinalLog.setUpdateBy(AliexpressLogUtils.halfTgUpdateStockZeroBySystemStockJobHandler);
                    smtStockUpdateFinalLog.setUpdateType(UpdateStockTypeEnum.halfTg.getCode());
                    if(systemStock == null){
                        smtStockUpdateFinalLog.setResultType(UpdateStockResultEnum.filter.getCode());
                        smtStockUpdateFinalLog.setFailInfo("可用库存为空");
                        finalLogs.add(smtStockUpdateFinalLog);
                        continue;
                    }
                    Integer sellableQuantity = null;
                    if(systemStock <= 2){
                        //若产品系统库存：可用-待发小于等于2，且半托管链接库存大于0，则将半托管链接库存改为0.
                        sellableQuantity = 0;
                    }else if(systemStock > 2){
                        if(updateStock != null){
                            sellableQuantity = updateStock;
                        }else{
                            sellableQuantity = systemStock;
                        }
                    }
                    if(sellableQuantity == null){
                        continue;
                    }
                    String popChoiceSkuWarehouseStockList = aliexpressHalfTgItem.getPopChoiceSkuWarehouseStockList();
                    JSONArray dbJsonArray = JSONObject.parseArray(popChoiceSkuWarehouseStockList);
                    for (int i = 0; i < dbJsonArray.size(); i++) {
                        JSONObject dbJsonArrayJSONObject = dbJsonArray.getJSONObject(i);
                        String warehouse_code = dbJsonArrayJSONObject.getString("warehouse_code");
                        String warehouse_name = dbJsonArrayJSONObject.getString("warehouse_name");

                        int pendingStock = Math.abs(aliexpressHalfTgItem.getSystemUsableTransferStock() - aliexpressHalfTgItem.getSmtTransferStock() - aliexpressHalfTgItem.getUsableStock());

                        //数据库的数量
                        Integer sellable_quantity = dbJsonArrayJSONObject.getInteger("sellable_quantity");
                        //数量一直不需要修改
                        if(sellable_quantity == sellableQuantity.intValue()){
                            smtStockUpdateFinalLog.setUsableStock(aliexpressHalfTgItem.getUsableStock());
                            smtStockUpdateFinalLog.setPendingStock(pendingStock);
                            smtStockUpdateFinalLog.setRedisStock(systemStock);
                            smtStockUpdateFinalLog.setStockBefore(sellable_quantity);
                            smtStockUpdateFinalLog.setStockAfter(sellableQuantity);
                            smtStockUpdateFinalLog.setResultType(UpdateStockResultEnum.filter.getCode());
                            smtStockUpdateFinalLog.setFailInfo("数量一致不需要修改");
                            finalLogs.add(smtStockUpdateFinalLog);
                            continue;
                        }
                        HalfTgItemRequest itemRequest = new HalfTgItemRequest();
                        itemRequest.setAccount(account);
                        itemRequest.setSkuId(aliexpressHalfTgItem.getSkuId());
                        itemRequest.setArticleNumber(aliexpressHalfTgItem.getArticleNumber());
                        itemRequest.setWarehouseName(warehouse_name);
                        itemRequest.setWarehouseCode(warehouse_code);
                        itemRequest.setSellableQuantity(sellableQuantity);
                        itemRequest.setProductId(key);
                        itemRequest.setUsableStock(aliexpressHalfTgItem.getUsableStock());
                        itemRequest.setPendingStock(pendingStock);
                        itemRequest.setSystemStock(systemStock);
                        requestsList.add(itemRequest);
                    }
                }
                if(CollectionUtils.isNotEmpty(requestsList)){
                    UpdateHalfStockMqBean halfStockMqBean = new UpdateHalfStockMqBean();
                    halfStockMqBean.setSaleAccountAndBusinessResponse(saleAccountAndBusinessResponse);
                    halfStockMqBean.setProductId(key);
                    halfStockMqBean.setRequestsList(requestsList);
                    halfStockMqBean.setCreateLongTime(createLongTime);

                    try {
                        rabbitMqSender.publishVHostRabbitTemplateSend(PublishMqConfig.SMT_API_DIRECT_EXCHANGE, PublishQueues.SMT_TIME_UPDATE_HALF_STOCK_ROUTE_KEY, JSON
                                .toJSON(halfStockMqBean));
                    } catch (Exception e) {
                        log.error("半托管修改库存发送队列异常 " + key + ":" + e.getMessage(), e);
                    }
//                    aliexpressHalfTgItemService.updateStocks(saleAccountAndBusinessResponse, key, requestsList, "HalfTgUpdateStockZeroBySystemStockJobHandler");
                }

                if(CollectionUtils.isNotEmpty(finalLogs)){
                    List<List<SmtStockUpdateFinalLog>> lists = PagingUtils.newPagingList(finalLogs, 1000);
                    for (List<SmtStockUpdateFinalLog> list : lists) {
                        try {
                            smtStockUpdateFinalLogService.batchInsert(list);
                        } catch (Exception e) {
                            log.error("批量新增异常:" + e.getMessage(),e);
                        }
                    }
                }

            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }
        }
    }

    //sku可用-待发 <=2 半托管库存大于0 则将半托管链接库存改为0
    private void updateZero(List<SaleAccountAndBusinessResponse> saleAccountList, List<String> skuList, Map<String, SaleAccountAndBusinessResponse> accountMap, String createLongTime){
        if(CollectionUtils.isEmpty(saleAccountList)){
            return;
        }
        for (SaleAccountAndBusinessResponse saleAccountAndBusinessResponse : saleAccountList) {
            String accountNumber = saleAccountAndBusinessResponse.getAccountNumber();
            //优先处理 系统库存小于等2的数据
            //查询本地数据，记录改前改后日志
            AliexpressHalfTgItemExample halfTgItemExample = new AliexpressHalfTgItemExample();
            AliexpressHalfTgItemExample.Criteria criteria = halfTgItemExample.createCriteria();
            criteria.andAccountEqualTo(accountNumber);
            criteria.andProductStatusEqualTo(ProductStatusTypeEnum.onSelling.getCode());
            criteria.andSystemStockLessThanOrEqualTo(2);
            criteria.andSkuStockGreaterThanTo(0);
            if(CollectionUtils.isNotEmpty(skuList)){
                criteria.andArticleNumberIn(skuList);
            }
            halfTgItemExample.setFields(fields);
            List<AliexpressHalfTgItem> dbList = aliexpressHalfTgItemService.selectByExample(halfTgItemExample);
            if(CollectionUtils.isEmpty(dbList)){
                continue;
            }
            handleData(dbList, accountMap, createLongTime, 0);
        }
    }

    //可用-待发 2-15 区间 链接库存不等于 可用-待发  则将库存改为可用-待发
    private void updateTwo(List<SaleAccountAndBusinessResponse> saleAccountList, List<String> skuList, Map<String, SaleAccountAndBusinessResponse> accountMap, String createLongTime){
        if(CollectionUtils.isEmpty(saleAccountList)){
            return;
        }
        for (SaleAccountAndBusinessResponse saleAccountAndBusinessResponse : saleAccountList) {
            String accountNumber = saleAccountAndBusinessResponse.getAccountNumber();
            //查询本地数据，记录改前改后日志
            AliexpressHalfTgItemExample halfTgItemExample = new AliexpressHalfTgItemExample();
            AliexpressHalfTgItemExample.Criteria criteria = halfTgItemExample.createCriteria();
            criteria.andAccountEqualTo(accountNumber);
            criteria.andProductStatusEqualTo(ProductStatusTypeEnum.onSelling.getCode());
            criteria.andSystemStockGreaterThan(2);
            criteria.andSystemStockLessThanOrEqualTo(15);
            criteria.andCustomSql("(system_stock <> sku_stock)");
            if(CollectionUtils.isNotEmpty(skuList)){
                criteria.andArticleNumberIn(skuList);
            }
            halfTgItemExample.setFields(fields);
            List<AliexpressHalfTgItem> dbList = aliexpressHalfTgItemService.selectByExample(halfTgItemExample);
            handleData(dbList, accountMap, createLongTime, null);
        }
    }

    //可用-待发 15-29 区间 链接库存小于15  则将库存改为 15
    private void updateThree(List<SaleAccountAndBusinessResponse> saleAccountList, List<String> skuList, Map<String, SaleAccountAndBusinessResponse> accountMap, String createLongTime){
        if(CollectionUtils.isEmpty(saleAccountList)){
            return;
        }
        for (SaleAccountAndBusinessResponse saleAccountAndBusinessResponse : saleAccountList) {
            String accountNumber = saleAccountAndBusinessResponse.getAccountNumber();

            //查询本地数据，记录改前改后日志
            AliexpressHalfTgItemExample halfTgItemExample = new AliexpressHalfTgItemExample();
            AliexpressHalfTgItemExample.Criteria criteria = halfTgItemExample.createCriteria();
            criteria.andAccountEqualTo(accountNumber);
            criteria.andProductStatusEqualTo(ProductStatusTypeEnum.onSelling.getCode());
            criteria.andSystemStockGreaterThan(15);
            criteria.andSystemStockLessThanOrEqualTo(29);
            criteria.andSkuStockLessThanTo(15);
            if(CollectionUtils.isNotEmpty(skuList)){
                criteria.andArticleNumberIn(skuList);
            }
            halfTgItemExample.setFields(fields);
            List<AliexpressHalfTgItem> dbList = aliexpressHalfTgItemService.selectByExample(halfTgItemExample);
            handleData(dbList, accountMap, createLongTime, 15);
        }
    }

    //可用-待发 50-100 区间 链接库存小于70  则将库存改为 70 变更为 50-100的改库存为50
    private void updateFour(List<SaleAccountAndBusinessResponse> saleAccountList, List<String> skuList, Map<String, SaleAccountAndBusinessResponse> accountMap, String createLongTime){
        if(CollectionUtils.isEmpty(saleAccountList)){
            return;
        }

        for (SaleAccountAndBusinessResponse saleAccountAndBusinessResponse : saleAccountList) {
            String accountNumber = saleAccountAndBusinessResponse.getAccountNumber();
            //查询本地数据，记录改前改后日志
            AliexpressHalfTgItemExample halfTgItemExample = new AliexpressHalfTgItemExample();
            AliexpressHalfTgItemExample.Criteria criteria = halfTgItemExample.createCriteria();
            criteria.andAccountEqualTo(accountNumber);
            criteria.andProductStatusEqualTo(ProductStatusTypeEnum.onSelling.getCode());
            criteria.andSystemStockGreaterThan(50);
            criteria.andSystemStockLessThanOrEqualTo(100);
            criteria.andSkuStockNotEqualTo(50);
            if(CollectionUtils.isNotEmpty(skuList)){
                criteria.andArticleNumberIn(skuList);
            }
            halfTgItemExample.setFields(fields);
            List<AliexpressHalfTgItem> dbList = aliexpressHalfTgItemService.selectByExample(halfTgItemExample);
            handleData(dbList, accountMap, createLongTime, 50);
        }
    }

    //可用-待发 >= 30 链接库存小于 1000 则将库存改为 9999
    private void update(List<SaleAccountAndBusinessResponse> saleAccountList, List<String> skuList, Map<String, SaleAccountAndBusinessResponse> accountMap, String createLongTime){
        if(CollectionUtils.isEmpty(saleAccountList)){
            return;
        }

        for (SaleAccountAndBusinessResponse saleAccountAndBusinessResponse : saleAccountList) {
            //只查询在线的产品
            String accountNumber = saleAccountAndBusinessResponse.getAccountNumber();

            //查询本地数据，记录改前改后日志
            AliexpressHalfTgItemExample halfTgItemExample = new AliexpressHalfTgItemExample();
            AliexpressHalfTgItemExample.Criteria criteria = halfTgItemExample.createCriteria();
            criteria.andAccountEqualTo(accountNumber);
            criteria.andProductStatusEqualTo(ProductStatusTypeEnum.onSelling.getCode());
            criteria.andSystemStockGreaterThanOrEqualTo(30);
            criteria.andSkuStockLessThanTo(1000);
            if(CollectionUtils.isNotEmpty(skuList)){
                criteria.andArticleNumberIn(skuList);
            }
            halfTgItemExample.setFields(fields);
            List<AliexpressHalfTgItem> dbList = aliexpressHalfTgItemService.selectByExample(halfTgItemExample);
            handleData(dbList, accountMap, createLongTime, 9999);
        }
    }
}

package com.estone.erp.publish.smt.mapper;

import com.estone.erp.publish.smt.model.HolidaySmtAreaUpdateLog;
import com.estone.erp.publish.smt.model.HolidaySmtAreaUpdateLogExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface HolidaySmtAreaUpdateLogMapper {
    int countByExample(HolidaySmtAreaUpdateLogExample example);

    int deleteByPrimaryKey(List<Long> ids);

    int insert(HolidaySmtAreaUpdateLog record);

    HolidaySmtAreaUpdateLog selectByPrimaryKey(Long id);

    List<HolidaySmtAreaUpdateLog> selectByExample(HolidaySmtAreaUpdateLogExample example);

    int updateByExampleSelective(@Param("record") HolidaySmtAreaUpdateLog record, @Param("example") HolidaySmtAreaUpdateLogExample example);

    int updateByPrimaryKeySelective(HolidaySmtAreaUpdateLog record);
}
package com.estone.erp.publish.smt.jobHandler;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.estone.erp.common.jobHandler.AbstractJobHandler;
import com.estone.erp.common.model.TidbPageMeta;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.common.util.CacheUtils;
import com.estone.erp.publish.common.util.PagingUtils;
import com.estone.erp.publish.elasticsearch.model.SaleAccount;
import com.estone.erp.publish.elasticsearch.model.beanrequest.EsSaleAccountRequest;
import com.estone.erp.publish.elasticsearch.service.SaleAccountService;
import com.estone.erp.publish.elasticsearch2.model.EsAliexpressProductListing;
import com.estone.erp.publish.elasticsearch2.model.beanrequest.EsAliexpressProductListingRequest;
import com.estone.erp.publish.elasticsearch2.service.EsAliexpressProductListingService;
import com.estone.erp.publish.smt.model.dto.LeaderAccountVo;
import com.estone.erp.publish.smt.model.dto.SpuAccountNumberVo;
import com.estone.erp.publish.smt.util.NewProductUtils;
import com.estone.erp.publish.system.hr.HrClient;
import com.estone.erp.publish.system.hr.model.HrNewUser;
import com.estone.erp.publish.tidb.publishtidb.model.AliexpressNewProductPublish;
import com.estone.erp.publish.tidb.publishtidb.model.AliexpressNewProductPublishLeader;
import com.estone.erp.publish.tidb.publishtidb.model.SmtNewProductPublishLeaderConfig;
import com.estone.erp.publish.tidb.publishtidb.service.AliexpressNewProductPublishLeaderService;
import com.estone.erp.publish.tidb.publishtidb.service.AliexpressNewProductPublishService;
import com.estone.erp.publish.tidb.publishtidb.service.SmtNewProductPublishLeaderConfigService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @BelongsProject: publish
 * @BelongsPackage: com.estone.erp.publish.smt.jobHandler
 * @Author: sj
 * @CreateTime: 2025-03-14  09:44
 * @Description: SMT刊登次数报表统计定时任务
 */

@Component
public class AliexpressNewProductPublishStatisticsJobHandler extends AbstractJobHandler {

    @Resource
    private SaleAccountService saleAccountService;

    @Resource
    private AliexpressNewProductPublishService aliexpressNewProductPublishService;

    @Resource
    private EsAliexpressProductListingService esAliexpressProductListingService;
    @Resource
    private AliexpressNewProductPublishLeaderService aliexpressNewProductPublishLeaderService;
    @Resource
    private HrClient hrClient;

    @Resource
    private SmtNewProductPublishLeaderConfigService smtNewProductPublishLeaderConfigService;

    private static final BigDecimal ONE_HUNDRED = new BigDecimal(100);

    public AliexpressNewProductPublishStatisticsJobHandler() {
        super("AliexpressNewProductPublishStatisticsJobHandler");
    }

    @Data
    public static class InnerParam {
        private List<String> spuList;
    }

    @XxlJob("AliexpressNewProductPublishStatisticsJobHandler")
    @Override
    public ReturnT<String> run(String param) throws Exception {
        InnerParam innerParam = passParam(param, InnerParam.class);
        if (innerParam == null) {
            innerParam = new InnerParam();
        }
        String threshold = CacheUtils.getSystemParamValue(SaleChannel.CHANNEL_SMT, "smt_new_product", "smt刊登次数达成率配置", 10);
        if (threshold == null) {
            XxlJobLogger.log("publish_rate_threshold is not set in system param");
            return ReturnT.FAIL;
        }
        int successNumber = Integer.parseInt(threshold);
        if (successNumber <= 0) {
            XxlJobLogger.log("publish_rate_threshold must be greater than 0");
            return ReturnT.FAIL;
        }
        XxlJobLogger.log("publish_rate_threshold is " + successNumber);
        XxlJobLogger.log("start get product listing statistics");

        doService(innerParam, successNumber);

        XxlJobLogger.log("end get product listing statistics");
        return ReturnT.SUCCESS;
    }

    private void doService(InnerParam innerParam, int successNumber) {
        LocalDateTime startTime30 = getBeforeDateTime(30);
        LocalDateTime startTime7 = getBeforeDateTime(7);


        // 获取所有的组长，以及获取所有组长的销售和店铺
        Map<String, LeaderAccountVo> leaderAccountVoMap = getLeaderAccountVoMap();

        //获取销售组长刊登次数配置
        Map<String, SmtNewProductPublishLeaderConfig> leaderConfigMap = getLeaderConfigMap();

        // 近7天的数据需要更新全部
        // 近30天的数据只需要更新30天的数据
        List<String> spuList = new ArrayList<>();
        if (ObjectUtils.isNotEmpty(innerParam)){
            spuList = innerParam.getSpuList();
        }
        LambdaQueryWrapper<AliexpressNewProductPublish> wrapper = new LambdaQueryWrapper<>();
        wrapper.ge(AliexpressNewProductPublish::getPushTime, startTime30);
        wrapper.in(CollectionUtils.isNotEmpty(spuList), AliexpressNewProductPublish::getSpu, spuList);

        List<TidbPageMeta<Long>> tidbPageMetaMap = aliexpressNewProductPublishService.getTidbPageMetaMap(wrapper);

        for (TidbPageMeta<Long> longTidbPageMeta : tidbPageMetaMap) {
            Long startKey = longTidbPageMeta.getStartKey();
            Long endKey = longTidbPageMeta.getEndKey();
            XxlJobLogger.log("开始处理 startKey: " + startKey + ", endKey: " + endKey);

            LambdaQueryWrapper<AliexpressNewProductPublish> publishLambdaQueryWrapper = new LambdaQueryWrapper<>();
            publishLambdaQueryWrapper.ge(AliexpressNewProductPublish::getId, startKey);
            publishLambdaQueryWrapper.le(AliexpressNewProductPublish::getId, endKey);
            publishLambdaQueryWrapper.ge(AliexpressNewProductPublish::getPushTime, startTime30);
            publishLambdaQueryWrapper.in(CollectionUtils.isNotEmpty(spuList), AliexpressNewProductPublish::getSpu, spuList);
            // 获取30天内的数据
            List<AliexpressNewProductPublish> aliexpressNewProductPublishes = aliexpressNewProductPublishService.list(publishLambdaQueryWrapper);
            if (CollectionUtils.isNotEmpty(aliexpressNewProductPublishes)) {
                doStatistics(aliexpressNewProductPublishes, successNumber, startTime7, leaderAccountVoMap, leaderConfigMap);
            }
            XxlJobLogger.log("结束处理 startKey: " + startKey + ", endKey: " + endKey);
        }
    }

    private Map<String, SmtNewProductPublishLeaderConfig> getLeaderConfigMap() {
        List<SmtNewProductPublishLeaderConfig> leaderConfigs = smtNewProductPublishLeaderConfigService.list();
        XxlJobLogger.log("组长配置：" + JSON.toJSONString(leaderConfigs));
        return leaderConfigs.stream().collect(Collectors.toMap(SmtNewProductPublishLeaderConfig::getSaleLeader, a -> a, (a, b) -> a));
    }

    private void doStatistics(List<AliexpressNewProductPublish> aliexpressNewProductPublishList, Integer successNumber, LocalDateTime startTime7,
                              Map<String, LeaderAccountVo> leaderAccountVoMap, Map<String, SmtNewProductPublishLeaderConfig> leaderConfigMap) {
        // 7天内的
        List<AliexpressNewProductPublish> smtNewProductPublishes7 = aliexpressNewProductPublishList.stream().filter(publish -> publish.getPushTime().isAfter(startTime7) || publish.getPushTime().isEqual(startTime7)).collect(Collectors.toList());
        // 7天后到30天
        List<AliexpressNewProductPublish> smtNewProductPublishes7And30 = aliexpressNewProductPublishList.stream().filter(publish -> publish.getPushTime().isBefore(startTime7)).collect(Collectors.toList());

        doStatistics7(smtNewProductPublishes7, successNumber, leaderAccountVoMap, leaderConfigMap);
        doStatistics30(smtNewProductPublishes7And30);
    }



    /**
     * 更新30天内的数据
     *
     * @param smtNewProductPublishes7And30 30天内数据
     */
    private void doStatistics30(List<AliexpressNewProductPublish> smtNewProductPublishes7And30) {

        if (CollectionUtils.isEmpty(smtNewProductPublishes7And30)) {
            return;
        }
        List<String> spuList = smtNewProductPublishes7And30.stream().map(AliexpressNewProductPublish::getSpu).collect(Collectors.toList());
        List<Long> mainIdList = smtNewProductPublishes7And30.stream().map(AliexpressNewProductPublish::getId).collect(Collectors.toList());

        List<SpuAccountNumberVo> spuAccountNumberVoList = getSpuAccountNumberVoList(spuList);
        LocalDateTime now = LocalDateTime.now();
        if (CollectionUtils.isEmpty(spuAccountNumberVoList)) {
            // 为空的时候，需要把所有数据都设置为0
            updateBy30Empty(smtNewProductPublishes7And30, now);
            return;
        }
        Map<String, List<SpuAccountNumberVo>> collect = spuAccountNumberVoList.stream().collect(Collectors.groupingBy(SpuAccountNumberVo::getSpu, Collectors.toList()));

        List<AliexpressNewProductPublish> emplyList = new ArrayList<>();
        List<AliexpressNewProductPublish> updateList = new ArrayList<>();
        for (AliexpressNewProductPublish aliexpressNewProductPublish : smtNewProductPublishes7And30) {
            String spu = aliexpressNewProductPublish.getSpu();
            List<SpuAccountNumberVo> spuAccountNumberVos = collect.get(spu);
            if (CollectionUtils.isEmpty(spuAccountNumberVos)) {
                emplyList.add(aliexpressNewProductPublish);
                continue;
            }
            AliexpressNewProductPublish update = new AliexpressNewProductPublish();
            update.setTwoListingNumber(spuAccountNumberVos.size());
            update.setUpdatedTime(now);
            update.setId(aliexpressNewProductPublish.getId());
            updateList.add(update);

        }
        if (CollectionUtils.isNotEmpty(updateList)) {
            aliexpressNewProductPublishService.updateBatchById(updateList, 300);
        }
        if (CollectionUtils.isNotEmpty(emplyList)) {
            updateBy30Empty(emplyList, now);
        }
    }

    private void updateBy30Empty(List<AliexpressNewProductPublish> smtNewProductPublishes7And30, LocalDateTime now) {
        Set<Long> ids = smtNewProductPublishes7And30.stream().map(AliexpressNewProductPublish::getId).collect(Collectors.toSet());
        if (CollectionUtils.isNotEmpty(ids)) {
            List<List<Long>> lists = PagingUtils.newPagingList(new ArrayList<>(ids), 300);
            for (List<Long> list : lists) {
                LambdaQueryWrapper<AliexpressNewProductPublish> updateWhere = new LambdaQueryWrapper<>();
                updateWhere.in(AliexpressNewProductPublish::getId, list);
                AliexpressNewProductPublish aliexpressNewProductPublish = new AliexpressNewProductPublish();
                aliexpressNewProductPublish.setUpdatedTime(now);
                aliexpressNewProductPublish.setTwoListingNumber(0);
                aliexpressNewProductPublishService.update(aliexpressNewProductPublish, updateWhere);
            }
        }
    }

    private void doStatistics7(List<AliexpressNewProductPublish> smtNewProductPublishes7, Integer successNumber,
                               Map<String, LeaderAccountVo> leaderAccountVoMap, Map<String, SmtNewProductPublishLeaderConfig> leaderConfigMap) {
        if (CollectionUtils.isEmpty(smtNewProductPublishes7)) {
            return;
        }
        // 近7天的数据，里面的30天和7天的连接数是一样的
        List<String> spuList = smtNewProductPublishes7.stream().map(AliexpressNewProductPublish::getSpu).collect(Collectors.toList());
        List<Long> mainIdList = smtNewProductPublishes7.stream().map(AliexpressNewProductPublish::getId).collect(Collectors.toList());
        LocalDateTime now = LocalDateTime.now();
        List<SpuAccountNumberVo> spuAccountNumberVoList = getSpuAccountNumberVoList(spuList);
        Map<Long, List<AliexpressNewProductPublishLeader>> publishLeaderByMainId = aliexpressNewProductPublishLeaderService.getPublishLeaderByMainId(mainIdList);
        if (CollectionUtils.isEmpty(spuAccountNumberVoList)) {
            // 为空的时候，需要把所有数据都设置为0
            updateBy7Empty(smtNewProductPublishes7, publishLeaderByMainId, now);
            return;
        }

        Map<String, List<SpuAccountNumberVo>> collect = spuAccountNumberVoList.stream().collect(Collectors.groupingBy(SpuAccountNumberVo::getSpu, Collectors.toList()));
        List<AliexpressNewProductPublish> emplyList = new ArrayList<>();
        List<AliexpressNewProductPublish> updateList = new ArrayList<>();
        List<AliexpressNewProductPublishLeader> updateLeaderList = new ArrayList<>();

        for (AliexpressNewProductPublish aliexpressNewProductPublish : smtNewProductPublishes7) {
            String spu = aliexpressNewProductPublish.getSpu();
            List<SpuAccountNumberVo> spuAccountNumberVos = collect.get(spu);
            if (CollectionUtils.isEmpty(spuAccountNumberVos)) {
                emplyList.add(aliexpressNewProductPublish);
                continue;
            }
            AliexpressNewProductPublish update = new AliexpressNewProductPublish();
            update.setTwoListingNumber(spuAccountNumberVos.size());
            update.setOneListingNumber(spuAccountNumberVos.size());
            update.setOneListingNumberPercent(getPercent(spuAccountNumberVos.size(), successNumber));
            update.setUpdatedTime(now);
            update.setId(aliexpressNewProductPublish.getId());
            updateList.add(update);


            List<AliexpressNewProductPublishLeader> aliexpressNewProductPublishLeaders = publishLeaderByMainId.getOrDefault(aliexpressNewProductPublish.getId(), List.of());

            // 更新组长数据
            SmtNewProductPublishLeaderConfig defaultConfig = getDefaultLeaderConfig();
            for (AliexpressNewProductPublishLeader aliexpressNewProductPublishLeader : aliexpressNewProductPublishLeaders) {
                String saleLeader = aliexpressNewProductPublishLeader.getSaleLeader();
                LeaderAccountVo leaderAccountVo = leaderAccountVoMap.get(saleLeader);
                SmtNewProductPublishLeaderConfig smtNewProductPublishLeaderConfig = leaderConfigMap.getOrDefault(saleLeader, defaultConfig);
                if (leaderAccountVo == null) {
                    continue;
                }
                Set<String> accountNumberSet = leaderAccountVo.getAccountNumberSet();
                List<SpuAccountNumberVo> authAccountSpu = spuAccountNumberVos.stream().filter(a -> accountNumberSet.contains(a.getAccountNumber())).collect(Collectors.toList());
                AliexpressNewProductPublishLeader updateLeader = new AliexpressNewProductPublishLeader();
                updateLeader.setUpdatedTime(now);
                updateLeader.setId(aliexpressNewProductPublishLeader.getId());
                updateLeader.setPercent(getPercent(authAccountSpu.size(), smtNewProductPublishLeaderConfig.getPublishCount()));
                updateLeaderList.add(updateLeader);
            }
        }

        if (CollectionUtils.isNotEmpty(updateList)) {
            aliexpressNewProductPublishService.updateBatchById(updateList, 300);
        }

        if (CollectionUtils.isNotEmpty(updateLeaderList)) {
            aliexpressNewProductPublishLeaderService.updateBatchById(updateLeaderList, 300);
        }
        if (CollectionUtils.isNotEmpty(emplyList)) {
            updateBy7Empty(emplyList, publishLeaderByMainId, now);
        }

    }

    private SmtNewProductPublishLeaderConfig getDefaultLeaderConfig() {
        SmtNewProductPublishLeaderConfig smtNewProductPublishLeaderConfig = new SmtNewProductPublishLeaderConfig();
        smtNewProductPublishLeaderConfig.setPublishCount(0);
        return smtNewProductPublishLeaderConfig;
    }


    /**
     * 更新在线列表数据不存在的
     * @param smtNewProductPublishes7 7天内数据
     * @param publishLeaderByMainId 发布组长数据
     * @param now  更新时间
     */
    private void updateBy7Empty(List<AliexpressNewProductPublish> smtNewProductPublishes7, Map<Long, List<AliexpressNewProductPublishLeader>> publishLeaderByMainId, LocalDateTime now) {
        if (CollectionUtils.isEmpty(smtNewProductPublishes7)) {
            return;
        }
        Set<Long> ids = smtNewProductPublishes7.stream().map(AliexpressNewProductPublish::getId).collect(Collectors.toSet());
        if (CollectionUtils.isNotEmpty(ids)) {
            List<List<Long>> lists = PagingUtils.newPagingList(new ArrayList<>(ids), 300);
            for (List<Long> list : lists) {
                LambdaQueryWrapper<AliexpressNewProductPublish> updateWhere = new LambdaQueryWrapper<>();
                updateWhere.in(AliexpressNewProductPublish::getId, list);
                AliexpressNewProductPublish aliexpressNewProductPublish = new AliexpressNewProductPublish();
                aliexpressNewProductPublish.setUpdatedTime(now);
                aliexpressNewProductPublish.setOneListingNumberPercent(0D);
                aliexpressNewProductPublish.setOneListingNumber(0);
                aliexpressNewProductPublish.setTwoListingNumber(0);
                aliexpressNewProductPublishService.update(aliexpressNewProductPublish, updateWhere);
            }
        }
        // 只更新包含的

        List<Long> leaderIds = publishLeaderByMainId.values().stream().flatMap(Collection::stream)
                .filter(a -> ids.contains(a.getPublishId()))
                .map(AliexpressNewProductPublishLeader::getId)
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(leaderIds)) {
            List<List<Long>> lists = PagingUtils.newPagingList(leaderIds, 300);
            for (List<Long> list : lists) {
                LambdaQueryWrapper<AliexpressNewProductPublishLeader> updateWhere = new LambdaQueryWrapper<>();
                updateWhere.in(AliexpressNewProductPublishLeader::getId, list);
                AliexpressNewProductPublishLeader aliexpressNewProductPublishLeader = new AliexpressNewProductPublishLeader();
                aliexpressNewProductPublishLeader.setUpdatedTime(now);
                aliexpressNewProductPublishLeader.setPercent(0D);
                aliexpressNewProductPublishLeaderService.update(aliexpressNewProductPublishLeader, updateWhere);
            }
        }

    }

    /**
     * 获取在线列表的数据，按照item维度
     *
     * @param spuList spu
     * @return spu维度的在线列表数据
     */
    public List<SpuAccountNumberVo> getSpuAccountNumberVoList(List<String> spuList) {
        List<List<String>> lists = PagingUtils.newPagingList(spuList, 1000);
        List<SpuAccountNumberVo> spuAccountNumberVoList = new ArrayList<>();
        Set<Long> itemSet = new HashSet<>();
        for (List<String> list : lists) {
            EsAliexpressProductListingRequest request =  new EsAliexpressProductListingRequest();
            request.setSpuList(list);
            request.setProductStatusType("onSelling");
            request.setQueryFields(new String[]{"id", "productId", "spu","aliexpressAccountNumber"});

            List<EsAliexpressProductListing> productListing = esAliexpressProductListingService.getEsAliexpressProductListing(request);

            for (EsAliexpressProductListing aliexpressProductListing : productListing) {
                Long productId = aliexpressProductListing.getProductId();
                if (itemSet.contains(productId)) {
                    continue;
                }
                SpuAccountNumberVo spuAccountNumberVo = new SpuAccountNumberVo();
                spuAccountNumberVo.setSpu(aliexpressProductListing.getSpu());
                spuAccountNumberVo.setAccountNumber(aliexpressProductListing.getAliexpressAccountNumber());
                spuAccountNumberVo.setProductId(productId);
                itemSet.add(productId);
                spuAccountNumberVoList.add(spuAccountNumberVo);
            }
        }
        return spuAccountNumberVoList;
    }

    private Map<String, LeaderAccountVo> getLeaderAccountVoMap() {
        List<LeaderAccountVo> leaderAccountVoList = getLeaderAccountVoList();
        XxlJobLogger.log("组长信息：" + JSON.toJSONString(leaderAccountVoList));
        return leaderAccountVoList.stream().collect(Collectors.toMap(LeaderAccountVo::getLeaderSale, a -> a, (a, b) -> a));
    }

    private List<LeaderAccountVo> getLeaderAccountVoList() {

        Map<String, String> argsMap = new HashMap<>(2);
        argsMap.put("args", SaleChannel.CHANNEL_SMT);
        ApiResult<List<HrNewUser>> lastLeaderMembersByPlatformResult = hrClient.getLastLeaderMembersByPlatform(JSON.toJSONString(argsMap));
        if(!lastLeaderMembersByPlatformResult.isSuccess()){
            throw new RuntimeException("smt新品推荐 获取HR 组长结构异常" + lastLeaderMembersByPlatformResult.getErrorMsg());
        }

        // 获取平台所有组长信息 需要排除特殊人员
        List<HrNewUser> hrNewUserListUser = lastLeaderMembersByPlatformResult.getResult();
        if(CollectionUtils.isEmpty(hrNewUserListUser)){
            throw new RuntimeException("smt新品推荐 hr 返回 无组长");
        }

        List<LeaderAccountVo> accountVos = new ArrayList<>();
        // 获取所有的销售
        for (HrNewUser user : hrNewUserListUser) {
            if(NewProductUtils.excludeLeaderList.contains(user.getEmployeeNo())){
                continue;
            }
            LeaderAccountVo leaderAccountVo = new LeaderAccountVo();
            leaderAccountVo.setLeaderSale(user.getEmployeeNo());
            List<String> saleIds = new ArrayList<>();
            saleIds.add(user.getEmployeeId().toString());
            List<HrNewUser> employeeList = user.getSubEmployees();

            // 过滤掉已经是组长的
            List<String> collect = employeeList.stream().map(HrNewUser::getEmployeeNo).collect(Collectors.toList());
            leaderAccountVo.setEmployeeSale(collect);
            List<String> saleList = employeeList.stream().map(a -> a.getEmployeeId().toString()).collect(Collectors.toList());
            saleIds.addAll(saleList);

            String[] withFields = {"accountNumber", "colBool2", "accountStatus"};
            EsSaleAccountRequest request = new EsSaleAccountRequest();
            request.setSaleChannel(SaleChannel.CHANNEL_SMT);
            request.setSaleIds(saleIds);
            request.setOverseasBusiness(false);
            List<SaleAccount> saleAccounts = saleAccountService.getSaleAccountsEs(request, withFields);
            Set<String> accountSet = saleAccounts.stream().map(SaleAccount::getAccountNumber).collect(Collectors.toSet());
            leaderAccountVo.setAccountNumberSet(accountSet);
            accountVos.add(leaderAccountVo);
        }
        return accountVos;
    }

    private LocalDateTime getBeforeDateTime(int beforeDays) {
        LocalDateTime now = LocalDateTime.now();
        return now.minusDays(beforeDays)
                .toLocalDate()
                .atStartOfDay();
    }

    private static double getPercent(Integer authAccountSpu, Integer success) {
        if (authAccountSpu == null || authAccountSpu == 0 || success == null || success == 0) {
            return 0D;
        }

        return BigDecimal.valueOf(authAccountSpu).divide(new BigDecimal(success), 4, RoundingMode.HALF_UP).multiply(ONE_HUNDRED).doubleValue();
    }

    private static double getPercent(Long authAccountSpu, Integer success) {
        if (authAccountSpu == null || authAccountSpu == 0 || success == null || success == 0) {
            return 0D;
        }

        return BigDecimal.valueOf(authAccountSpu).divide(new BigDecimal(success), 4, RoundingMode.HALF_UP).multiply(ONE_HUNDRED).doubleValue();
    }
}

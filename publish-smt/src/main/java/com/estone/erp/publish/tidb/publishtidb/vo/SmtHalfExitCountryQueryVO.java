package com.estone.erp.publish.tidb.publishtidb.vo;

import lombok.Data;
import java.time.LocalDateTime;
import java.util.List;

/**
 * SmtHalfExitCountry查询条件VO
 */
@Data
public class SmtHalfExitCountryQueryVO {

    /**
     * 主键ID 勾选导出使用
     */
    private List<Long> idList;

    /**
     * 店铺，支持多选
     */
    private List<String> shopList;

    /**
     * 商品ID，多个用英文逗号分隔
     */
    private String productIds;

    /**
     * 退出国家，支持多选
     */
    private List<String> exitCountries;

    /**
     * 审核状态，单选：0-审核中，1-审核成功，2-审核失败
     */
    private Integer reviewStatus;

    /**
     * 审核通过时间范围
     */
    private String reviewStartTime;
    private String reviewEndTime;

    /**
     * 提交状态，单选：0-待提交，1-提交成功，2-提交失败，3-提交中
     */
    private Integer submitStatus;

    /**
     * 添加人，支持多选
     */
    private List<String> creatorList;

    /**
     * 添加时间范围
     */
    private String createStartTime;
    private String createEndTime;

    /**
     * 退出时间范围
     */
    private String exitStartTime;
    private String exitEndTime;

    /**
     * 提交时间范围
     */
    private String submitStartTime;
    private String submitEndTime;

    /**
     * 提交成功时间范围
     */
    private String submitSuccessStartTime;
    private String submitSuccessEndTime;

    /**
     * 同步时间范围
     */
    private String syncStartTime;
    private String syncEndTime;


    private int pageNum = 1;
    private int pageSize = 50;


}
package com.estone.erp.publish.smt.service.impl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.exception.ExcelDataConvertException;
import com.alibaba.fastjson.JSON;
import com.alibaba.nacos.common.utils.CollectionUtils;
import com.estone.erp.common.model.api.CQuery;
import com.estone.erp.common.model.api.CQueryResult;
import com.estone.erp.common.util.CommonUtils;
import com.estone.erp.common.util.WebUtils;
import com.estone.erp.publish.smt.mapper.AliexpressSpuCategoryRelationMapper;
import com.estone.erp.publish.smt.model.*;
import com.estone.erp.publish.smt.model.dto.SmtSpuCategoryRelationDTO;
import com.estone.erp.publish.smt.service.AliexpressCategoryService;
import com.estone.erp.publish.smt.service.AliexpressSpuCategoryRelationService;
import com.estone.erp.publish.system.product.ProductUtils;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> aliexpress_spu_category_relation
 * 2023-06-14 16:35:35
 */
@Service("aliexpressSpuCategoryRelationService")
@Slf4j
public class AliexpressSpuCategoryRelationServiceImpl implements AliexpressSpuCategoryRelationService {
    @Resource
    private AliexpressSpuCategoryRelationMapper aliexpressSpuCategoryRelationMapper;

    @Resource
    private AliexpressCategoryService aliexpressCategoryService;

    @Override
    public int countByExample(AliexpressSpuCategoryRelationExample example) {
        Assert.notNull(example, "example is null!");
        return aliexpressSpuCategoryRelationMapper.countByExample(example);
    }

    @Override
    public CQueryResult<AliexpressSpuCategoryRelation> search(CQuery<AliexpressSpuCategoryRelationCriteria> cquery) {
        Assert.notNull(cquery, "cquery is null!");
        AliexpressSpuCategoryRelationCriteria query = cquery.getSearch();
        AliexpressSpuCategoryRelationExample example = query.getExample();
        long total = 0;
        int totalPages = 0;
        // 是否分页
        if (cquery.isPageReqired()) {
            total = aliexpressSpuCategoryRelationMapper.countByExample(example);
            totalPages = (int) Math.ceil((double) total / cquery.getLimit());
            example.setLimit(cquery.getLimit());
            example.setOffset(cquery.getOffset());
        }
        example.setOrderByClause("create_date DESC");
        List<AliexpressSpuCategoryRelation> aliexpressSpuCategoryRelations = aliexpressSpuCategoryRelationMapper.selectByExample(example);

        // 设置分类
        handleCategory(aliexpressSpuCategoryRelations);

        // 组装结果
        CQueryResult<AliexpressSpuCategoryRelation> result = new CQueryResult<>();
        result.setTotal(total);
        result.setTotalPages(totalPages);
        result.setRows(aliexpressSpuCategoryRelations);
        return result;
    }

    private void handleCategory(List<AliexpressSpuCategoryRelation> aliexpressSpuCategoryRelations) {
        if (CollectionUtils.isEmpty(aliexpressSpuCategoryRelations)) {
            return;
        }

        // 获取分类
        List<String> categoryIdStrList = aliexpressSpuCategoryRelations.stream().map(AliexpressSpuCategoryRelation::getPlatformCategory).collect(Collectors.toList());
        Set<Integer> categoryIdSet = new HashSet<>();
        for (String categoryIdStr : categoryIdStrList) {
            List<String> categoryIdList = CommonUtils.splitList(categoryIdStr, ",");
            for (String categoryId : categoryIdList) {
                categoryIdSet.add(Integer.valueOf(categoryId));
            }
        }
        AliexpressCategoryExample example = new AliexpressCategoryExample();
        example.createCriteria().andCategoryIdIn(new ArrayList<>(categoryIdSet));
        List<AliexpressCategory> aliexpressCategories = aliexpressCategoryService.searchCategoryTree(example);
        Map<Integer, AliexpressCategory> categoryMap = new HashMap<>();
        aliexpressCategories.forEach((t) -> categoryMap.put(t.getCategoryId(), t));

        for (AliexpressSpuCategoryRelation aliexpressSpuCategoryRelation : aliexpressSpuCategoryRelations) {
            List<AliexpressCategory> aliexpressCategoryList = new ArrayList<>();
            List<String> categoryIdList = CommonUtils.splitList(aliexpressSpuCategoryRelation.getPlatformCategory(), ",");
            for (String categoryId : categoryIdList) {
                AliexpressCategory aliexpressCategory = categoryMap.get(Integer.valueOf(categoryId));
                aliexpressCategoryList.add(aliexpressCategory);
            }
            aliexpressSpuCategoryRelation.setAliexpressCategoryList(aliexpressCategoryList);
        }
    }

    @Override
    public AliexpressSpuCategoryRelation selectByPrimaryKey(Integer id) {
        Assert.notNull(id, "id is null!");
        return aliexpressSpuCategoryRelationMapper.selectByPrimaryKey(id);
    }

    @Override
    public AliexpressSpuCategoryRelation selectBySpu(String spu, Boolean status) {
        AliexpressSpuCategoryRelationExample example = new AliexpressSpuCategoryRelationExample();
        AliexpressSpuCategoryRelationExample.Criteria criteria = example.createCriteria();
        criteria.andSpuEqualTo(spu);
        if (null != status) {
            criteria.andStatusEqualTo(status);
        }
        List<AliexpressSpuCategoryRelation> aliexpressSpuCategoryRelations = aliexpressSpuCategoryRelationMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(aliexpressSpuCategoryRelations)) {
            return null;
        }
        return aliexpressSpuCategoryRelations.get(0);
    }

    @Override
    public List<AliexpressSpuCategoryRelation> selectByExample(AliexpressSpuCategoryRelationExample example) {
        Assert.notNull(example, "example is null!");
        return aliexpressSpuCategoryRelationMapper.selectByExample(example);
    }

    @Override
    public int insert(AliexpressSpuCategoryRelation record) {
        Assert.notNull(record, "record is null!");
        // 默认加时间和人
        return aliexpressSpuCategoryRelationMapper.insert(record);
    }

    @Override
    public int batchInsert(List<AliexpressSpuCategoryRelation> records) {
        Assert.notNull(records, "records is null!");
        // 默认加时间和人
        return aliexpressSpuCategoryRelationMapper.batchInsert(records);
    }

    @Override
    public int updateByPrimaryKeySelective(AliexpressSpuCategoryRelation record) {
        Assert.notNull(record, "record is null!");
        // 默认加修改时间和修改人
        return aliexpressSpuCategoryRelationMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public int batchUpdateByPrimaryKeySelective(List<AliexpressSpuCategoryRelation> records) {
        return aliexpressSpuCategoryRelationMapper.batchUpdateByPrimaryKeySelective(records);
    }

    @Override
    public int updateByExampleSelective(AliexpressSpuCategoryRelation record, AliexpressSpuCategoryRelationExample example) {
        Assert.notNull(record, "record is null!");
        // 默认加修改时间和修改人
        return aliexpressSpuCategoryRelationMapper.updateByExampleSelective(record, example);
    }

    @Override
    public int deleteByPrimaryKey(List<Integer> ids) {
        Assert.notNull(ids, "ids is null!");
        return aliexpressSpuCategoryRelationMapper.deleteByPrimaryKey(ids);
    }

    @Override
    public void addSpuCategoryRelation(AliexpressSpuCategoryRelation aliexpressSpuCategoryRelation) {
        if (null == aliexpressSpuCategoryRelation) {
            throw new IllegalArgumentException("参数异常");
        }

        String spu = aliexpressSpuCategoryRelation.getSpu();

        // 校验spu是否已存在
        AliexpressSpuCategoryRelation relation = this.selectBySpu(spu, null);
        if (null != relation) {
            throw new RuntimeException("SPU已存在");
        }

        // 校验spu在产品系统是否存在
        List<String> existSpuList = ProductUtils.getExistSpuBySpu(Lists.newArrayList(spu));
        if (CollectionUtils.isEmpty(existSpuList)) {
            throw new RuntimeException("SPU在产品系统不存在");
        }

        String platformCategory = aliexpressSpuCategoryRelation.getPlatformCategory();
        aliexpressSpuCategoryRelation.setPlatformCategory("," + platformCategory + ",");
        aliexpressSpuCategoryRelation.setStatus(true);
        aliexpressSpuCategoryRelation.setCreateBy(WebUtils.getUserName());
        aliexpressSpuCategoryRelation.setCreateDate(new Timestamp(System.currentTimeMillis()));
        aliexpressSpuCategoryRelation.setUpdateBy(WebUtils.getUserName());
        aliexpressSpuCategoryRelation.setUpdateDate(new Timestamp(System.currentTimeMillis()));
        this.insert(aliexpressSpuCategoryRelation);
    }

    @Override
    public void batchEditRelation(List<Integer> idList, String platformCategory) {
        platformCategory = "," + platformCategory + ",";
        String userName = WebUtils.getUserName();
        Timestamp updateTime = new Timestamp(System.currentTimeMillis());
        aliexpressSpuCategoryRelationMapper.batchUpdateRelation(idList, platformCategory, userName, updateTime);
    }

    @Override
    public void batchDisableEnable(List<Integer> idList, Boolean status) {
        String userName = WebUtils.getUserName();
        Timestamp updateTime = new Timestamp(System.currentTimeMillis());
        aliexpressSpuCategoryRelationMapper.batchUpdateStatus(idList, status, userName, updateTime);
    }

    @Override
    public void uploadSpuCategoryRelation(MultipartFile file) {
        if (null == file) {
            throw new IllegalArgumentException("文件为空");
        }

        List<String> errorSpuList = new ArrayList<>();

        // excel转换对象
        List<SmtSpuCategoryRelationDTO> smtSpuCategoryRelationDTOList = readByFile(file, errorSpuList);
        if (CollectionUtils.isEmpty(smtSpuCategoryRelationDTOList)) {
            throw new RuntimeException("导入文件数据格式有误");
        }

        // 移除spu相同的数据
        Set<String> sameSpuSet = new HashSet<>();
        Map<String, List<SmtSpuCategoryRelationDTO>> spuToRelationMap =
                smtSpuCategoryRelationDTOList.stream().collect(Collectors.groupingBy(SmtSpuCategoryRelationDTO::getSpu));
        for (String spu : spuToRelationMap.keySet()) {
            List<SmtSpuCategoryRelationDTO> relationDTOList = spuToRelationMap.get(spu);
            if (relationDTOList.size() > 1) {
                sameSpuSet.add(spu);
            }
        }
        errorSpuList.addAll(sameSpuSet);
        smtSpuCategoryRelationDTOList.removeIf(item -> sameSpuSet.contains(item.getSpu()));
        if (CollectionUtils.isEmpty(smtSpuCategoryRelationDTOList)) {
            throw new RuntimeException("一个SPU只能导入一条数据");
        }

        // 校验spu是否存在于产品系统
        List<String> spuList = smtSpuCategoryRelationDTOList.stream().map(SmtSpuCategoryRelationDTO::getSpu).collect(Collectors.toList());
        List<String> existSpuList = ProductUtils.getExistSpuBySpu(spuList);

        // 转换为数据库对象
        List<AliexpressSpuCategoryRelation> updateRelations = new ArrayList<>();
        List<AliexpressSpuCategoryRelation> insertRelations = new ArrayList<>();
        for (SmtSpuCategoryRelationDTO relationDTO : smtSpuCategoryRelationDTOList) {
            try {
                AliexpressSpuCategoryRelation aliexpressSpuCategoryRelation = transformSpuCategoryRelation(relationDTO, existSpuList);
                if (null != aliexpressSpuCategoryRelation.getId()) {
                    updateRelations.add(aliexpressSpuCategoryRelation);
                } else {
                    insertRelations.add(aliexpressSpuCategoryRelation);
                }
            } catch (Exception e) {
                errorSpuList.add(relationDTO.getSpu());
            }
        }

        // 保存
        if (CollectionUtils.isNotEmpty(updateRelations)) {
            this.batchUpdateByPrimaryKeySelective(updateRelations);
        }
        if (CollectionUtils.isNotEmpty(insertRelations)) {
            this.batchInsert(insertRelations);
        }

        if (CollectionUtils.isNotEmpty(errorSpuList)) {
            throw new RuntimeException(String.format("SPU%s保存失败，请检查数据格式", JSON.toJSONString(errorSpuList)));
        }
    }

    private AliexpressSpuCategoryRelation transformSpuCategoryRelation(SmtSpuCategoryRelationDTO relationDTO, List<String> existSpuList) {
        String spu = relationDTO.getSpu();
        String platformCategoryName = relationDTO.getPlatformCategoryName();
        if (StringUtils.isBlank(spu) || StringUtils.isBlank(platformCategoryName)) {
            throw new RuntimeException("spu或分类不能为空");
        }

        // 校验spu是否存在产品系统
        if (!existSpuList.contains(spu)) {
            throw new RuntimeException("SPU在产品系统不存在");
        }

        // 获取分类id
        List<Integer> categoryIdList = new ArrayList<>();
        List<String> platformCategoryNameList = CommonUtils.splitList(platformCategoryName, ",");
        for (String categoryName : platformCategoryNameList) {
            AliexpressCategoryExample categoryExample = new AliexpressCategoryExample();
            categoryExample.setFields("id, category_id");
            categoryExample.createCriteria().andFullCnNameEqualTo(categoryName);
            List<AliexpressCategory> aliexpressCategories = aliexpressCategoryService.selectByExample(categoryExample);
            if (CollectionUtils.isEmpty(aliexpressCategories) || null == aliexpressCategories.get(0).getCategoryId()) {
                throw new RuntimeException(String.format("SPU[%s]，分类[%s]获取不到分类id", spu, categoryName));
            }
            categoryIdList.add(aliexpressCategories.get(0).getCategoryId());
        }

        AliexpressSpuCategoryRelation aliexpressSpuCategoryRelation = new AliexpressSpuCategoryRelation();

        // 如果SPU存在则覆盖
        AliexpressSpuCategoryRelation relation = this.selectBySpu(spu, null);
        if (null != relation) {
            aliexpressSpuCategoryRelation.setId(relation.getId());
        }
        aliexpressSpuCategoryRelation.setSpu(spu);
        aliexpressSpuCategoryRelation.setPlatformCategory("," + StringUtils.join(categoryIdList, ",") + ",");
        aliexpressSpuCategoryRelation.setStatus(true);
        aliexpressSpuCategoryRelation.setCreateBy(WebUtils.getUserName());
        aliexpressSpuCategoryRelation.setCreateDate(new Timestamp(System.currentTimeMillis()));
        aliexpressSpuCategoryRelation.setUpdateBy(WebUtils.getUserName());
        aliexpressSpuCategoryRelation.setUpdateDate(new Timestamp(System.currentTimeMillis()));

        return aliexpressSpuCategoryRelation;
    }

    private List<SmtSpuCategoryRelationDTO> readByFile(MultipartFile file, List<String> errorSpuList) {
        List<SmtSpuCategoryRelationDTO> smtSpuCategoryRelationDTOList = new ArrayList<>();
        try {
            EasyExcel.read(file.getInputStream(), SmtSpuCategoryRelationDTO.class,
                    new AnalysisEventListener<SmtSpuCategoryRelationDTO>() {
                        @Override
                        public void onException(Exception exception, AnalysisContext context) {
                            // 如果某行导入失败，过滤
                            if (exception instanceof ExcelDataConvertException) {
                                ExcelDataConvertException excelDataConvertException = (ExcelDataConvertException)exception;
                                log.error("第{}行，第{}列解析异常，数据为:{}", excelDataConvertException.getRowIndex(),
                                        excelDataConvertException.getColumnIndex(), JSON.toJSONString(excelDataConvertException.getCellData()));
                            }
                        }

                        @Override
                        public void invoke(SmtSpuCategoryRelationDTO smtSpuCategoryRelationDTO, AnalysisContext analysisContext) {
                            String spu = smtSpuCategoryRelationDTO.getSpu();
                            String platformCategoryName = smtSpuCategoryRelationDTO.getPlatformCategoryName();
                            if (StringUtils.isBlank(spu) || StringUtils.isBlank(platformCategoryName)) {
                                if (StringUtils.isNotBlank(spu)) {
                                    errorSpuList.add(spu);
                                }
                                return;
                            }
                            platformCategoryName = platformCategoryName.replaceAll("，", ",");
                            smtSpuCategoryRelationDTO.setPlatformCategoryName(platformCategoryName);
                            smtSpuCategoryRelationDTOList.add(smtSpuCategoryRelationDTO);
                        }

                        @Override
                        public void doAfterAllAnalysed(AnalysisContext analysisContext) {}
                    }).sheet().doRead();
        } catch (Exception e) {
            throw new RuntimeException("导入文件解析失败：" + e.getMessage());
        }
        return smtSpuCategoryRelationDTOList;
    }
}
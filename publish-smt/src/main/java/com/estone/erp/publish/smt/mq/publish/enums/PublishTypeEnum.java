package com.estone.erp.publish.smt.mq.publish.enums;

/**
 * <AUTHOR>
 * @description: 刊登类型
 * @date 2020/11/214:53
 */
public enum PublishTypeEnum {

    temp(10, "模板"),

    temp_batch(11, "模板批量"),

    temp_timing(12, "模板定时"),

    productSource(20, "产品库"),

    productSource_auto(21, "产品库自动刊登"),

    spu_auto(30, "spu自动刊登"),

    spu_timing(31, "spu定时刊登"),

    pop_to_sku(32, "pop指定sku刊登"),

    pop_cofig(33, "pop配置刊登"),

    product_moving(40, "产品搬家"),
    ;


    private int code;

    private String name;

    private PublishTypeEnum(int code, String name) {
        this.name = name;
        this.code = code;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public static PublishTypeEnum build(int code) {
        PublishTypeEnum[] values = values();
        for (PublishTypeEnum type : values) {
            if (type.code == code) {
                return type;
            }
        }
        return null;
    }

    public static String getNameByCode(int code) {
        PublishTypeEnum[] values = values();
        for (PublishTypeEnum type : values) {
            if (type.code == code) {
                return type.getName();
            }
        }
        return null;
    }

    public int intCode() {
        return Integer.valueOf(this.code).intValue();
    }

    public Integer integerCode(){return Integer.valueOf(this.code);}

}

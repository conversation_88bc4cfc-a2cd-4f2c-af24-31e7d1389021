package com.estone.erp.publish.smt.model;

import com.estone.erp.common.util.SpringUtils;
import com.estone.erp.publish.smt.service.AliexpressCategoryService;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR> ali_system_category_mapping
 * 2023-02-14 17:50:17
 */
@Data
public class AliSystemCategoryMappingCriteria extends AliSystemCategoryMapping {
    private static final long serialVersionUID = 1L;

    private List<String> systemCategoryCodeList;
    private List<Integer> categoryIdList;
    private Boolean notRelation; // 无映射关系分类
    private List<String> platformCnNameLikeList; //平台中文类目like搜索
    private Boolean returnNull;

    public AliSystemCategoryMappingExample getExample() {
        AliSystemCategoryMappingExample example = new AliSystemCategoryMappingExample();
        AliSystemCategoryMappingExample.Criteria criteria = example.createCriteria();
        if (this.getEnable() != null) {
            criteria.andEnableEqualTo(this.getEnable());
        }
        if (StringUtils.isNotBlank(this.getSystemCategoryFullCode())) {
            criteria.andSystemCategoryFullCodeEqualTo(this.getSystemCategoryFullCode());
        }
        if (StringUtils.isNotBlank(this.getSystemCategoryCode())) {
            criteria.andSystemCategoryCodeEqualTo(this.getSystemCategoryCode());
        }
        //产品系统分类查询
        if(CollectionUtils.isNotEmpty(this.getSystemCategoryCodeList())){
            criteria.andSystemCategoryCodeIn(this.getSystemCategoryCodeList());
        }
        if (StringUtils.isNotBlank(this.getSystemCategoryParentCode())) {
            criteria.andSystemCategoryParentCodeEqualTo(this.getSystemCategoryParentCode());
        }
        if (StringUtils.isNotBlank(this.getSystemCategoryFullName())) {
            criteria.andSystemCategoryFullNameEqualTo(this.getSystemCategoryFullName());
        }
        if (StringUtils.isNotBlank(this.getPlatformCategoryCodes())) {
            criteria.andPlatformCategoryCodesEqualTo(this.getPlatformCategoryCodes());
        }
        if (StringUtils.isNotBlank(this.getPlatformCategoryNames())) {
            criteria.andPlatformCategoryNamesEqualTo(this.getPlatformCategoryNames());
        }
        if (StringUtils.isNotBlank(this.getCreateBy())) {
            criteria.andCreateByEqualTo(this.getCreateBy());
        }
        if (this.getCreateDate() != null) {
            criteria.andCreateDateEqualTo(this.getCreateDate());
        }
        if (StringUtils.isNotBlank(this.getUpdateBy())) {
            criteria.andUpdateByEqualTo(this.getUpdateBy());
        }
        if (this.getUpdateDate() != null) {
            criteria.andUpdateDateEqualTo(this.getUpdateDate());
        }

        //无映射关系分类
        if(this.getNotRelation() != null && this.getNotRelation()){
            criteria.andPlatformCategoryCodesIsNullorEmpty();
        }

        //先去平台类目模糊搜索
        if(CollectionUtils.isNotEmpty(this.getPlatformCnNameLikeList())){
            AliexpressCategoryService aliexpressCategoryService = SpringUtils.getBean(AliexpressCategoryService.class);
            AliexpressCategoryExample categoryExample = new AliexpressCategoryExample();
            categoryExample.createCriteria()
                    .andLeafCategoryEqualTo(true)
                    .andIsShowEqualTo(true)
                    .andFullCnNameListLike(this.getPlatformCnNameLikeList());
            List<AliexpressCategory> aliexpressCategories = aliexpressCategoryService.searchCategory(categoryExample);
            //搜索不到
            if(CollectionUtils.isEmpty(aliexpressCategories)){
                this.setReturnNull(true);
            }else{
                List<Integer> categoryIdList = aliexpressCategories.stream().map(t -> t.getCategoryId()).collect(Collectors.toList());
                criteria.andPlatformCategoryCodeListLike(categoryIdList);
            }
        }
        return example;
    }
}
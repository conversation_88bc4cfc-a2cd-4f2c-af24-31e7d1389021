package com.estone.erp.publish.smt.jobHandler;

import com.estone.erp.common.jobHandler.AbstractJobHandler;
import com.estone.erp.common.util.SpringUtils;
import com.estone.erp.publish.smt.model.AliexpressNewRemind;
import com.estone.erp.publish.smt.model.AliexpressNewRemindExample;
import com.estone.erp.publish.smt.service.AliexpressNewRemindService;
import com.estone.erp.publish.system.product.ProductClient;
import com.estone.erp.publish.system.product.esProduct.service.SingleItemEsService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 每天更新产品标签
 * <AUTHOR>
 * @date 2021/12/3 15:14
 */
@Component
@Slf4j
public class NewProductRemindUpdateTagJobHandler extends AbstractJobHandler {

    public NewProductRemindUpdateTagJobHandler() {
        super("NewProductRemindUpdateTagJobHandler");
    }

    private AliexpressNewRemindService aliexpressNewRemindService = SpringUtils.getBean(AliexpressNewRemindService.class);

    private ProductClient productClient = SpringUtils.getBean(ProductClient.class);

    private SingleItemEsService singleItemEsService = SpringUtils.getBean(SingleItemEsService.class);

    @Override
    @XxlJob("NewProductRemindUpdateTagJobHandler")
    public ReturnT<String> run(String param) throws Exception {
        XxlJobLogger.log("更新产品标签START");

        int limit = 200;
        int offset = 0;

        while (true){
            AliexpressNewRemindExample newRemindExample = new AliexpressNewRemindExample();
            newRemindExample.setOrderByClause("id DESC");
            newRemindExample.setLimit(limit);
            newRemindExample.setOffset(offset);
            AliexpressNewRemindExample.Criteria criteria = newRemindExample.createCriteria();
            criteria.andIsSuccessTempEqualTo(false);
            List<AliexpressNewRemind> aliexpressNewReminds = aliexpressNewRemindService.selectByExample(newRemindExample);
            if(CollectionUtils.isEmpty(aliexpressNewReminds)){
                break;
            }

            for (AliexpressNewRemind aliexpressNewRemind : aliexpressNewReminds) {
                try {
                    String spu = aliexpressNewRemind.getSpu();
                    //设置spu下面所有子sku的标签去重
                    List<String> allTagCodeListBySpu = singleItemEsService.getAllTagCodeListBySpu(spu);
                    if(CollectionUtils.isNotEmpty(allTagCodeListBySpu)){
                        aliexpressNewRemind.setTagCodes("," + StringUtils.join(allTagCodeListBySpu, ",") + ",");
                        aliexpressNewRemindService.updateByPrimaryKeySelective(aliexpressNewRemind);
                    }
                } catch (Exception e) {
                    log.error(e.getMessage(), e);
                }
            }
            offset += limit;
        }
        XxlJobLogger.log("更新产品标签END");
        return ReturnT.SUCCESS;
    }
}

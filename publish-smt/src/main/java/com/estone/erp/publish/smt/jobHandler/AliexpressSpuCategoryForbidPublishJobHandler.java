package com.estone.erp.publish.smt.jobHandler;

import com.alibaba.fastjson.JSON;
import com.estone.erp.common.jobHandler.AbstractJobHandler;
import com.estone.erp.common.mq.PublishQueues;
import com.estone.erp.common.mq.RabbitMqSender;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.common.util.CacheUtils;
import com.estone.erp.publish.mq.PublishMqConfig;
import com.estone.erp.publish.smt.bean.AccountProductIdBean;
import com.estone.erp.publish.smt.enums.OperateLogEnum;
import com.estone.erp.publish.smt.model.AliexpressOperateLog;
import com.estone.erp.publish.smt.model.SmtViolationPunishRecordExample;
import com.estone.erp.publish.smt.service.AliexpressOperateLogService;
import com.estone.erp.publish.smt.service.AliexpressSpuCategoryForbidPublishService;
import com.estone.erp.publish.smt.service.SmtViolationPunishRecordService;
import com.google.common.collect.Lists;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 *
 * 违规处罚数据写入SPU-禁止刊登分类列表定时任务
 * @Author：gt
 * @Date：2024/9/10 14:47
 */
@Component
@Slf4j
public class AliexpressSpuCategoryForbidPublishJobHandler extends AbstractJobHandler {
    public AliexpressSpuCategoryForbidPublishJobHandler() {
        super("AliexpressSpuCategoryForbidPublishJobHandler");
    }

    @Resource
    private AliexpressSpuCategoryForbidPublishService aliexpressSpuCategoryForbidPublishService;
    @Resource
    private SmtViolationPunishRecordService smtViolationPunishRecordService;
    @Resource
    private RabbitMqSender rabbitMqSender;
    @Resource
    private AliexpressOperateLogService aliexpressOperateLogService;

    @Data
    public static class InnerParam {
        private List<String> stores;//店铺数组
        private List<String> subType=new ArrayList<>(Arrays.asList("禁限售违规"));
        private List<String> subRulesType=new ArrayList<>(Arrays.asList("平台规则"));
        private List<String> assetTypes=new ArrayList<>(Arrays.asList("商品违规"));
    }

    public static void main(String[] args) {

    }

    @XxlJob("AliexpressSpuCategoryForbidPublishJobHandler")
    @Override
    public ReturnT<String> run(String param) throws Exception {
        XxlJobLogger.log("############开始执行违规处罚数据写入SPU-禁止刊登分类列表定时任务");
        try {
            InnerParam innerParam = null;
            if (StringUtils.isNotBlank(param)) {
                try {
                    innerParam = JSON.parseObject(param, InnerParam.class);
                } catch (Exception e) {
                    log.error("-------解析param出错：--------", e);
                    return ReturnT.FAIL;
                }
            }
            if (innerParam == null) {
                innerParam = new InnerParam();
            }
            String promptKeyword =  CacheUtils.getSystemParamValue(SaleChannel.CHANNEL_SMT, "smt_param", "PROMPT_KEYWORD", 600);
            String[] promptKeywordArr=promptKeyword.split(",");
            List<String> promptKeywordList=Lists.newArrayList(promptKeywordArr);
            SmtViolationPunishRecordExample example=new SmtViolationPunishRecordExample();
            SmtViolationPunishRecordExample.Criteria criteria=example.createCriteria();
            criteria.andSubTypeIn(innerParam.getSubType());
            criteria.andSubRulesTypeIn(innerParam.getSubRulesType());
            criteria.andAssetTypesIn(innerParam.getAssetTypes());
            criteria.andPromptLikeIn(promptKeywordList);
            if(CollectionUtils.isNotEmpty(innerParam.getStores())){
                criteria.andAccountIn(innerParam.getStores());
            }
            //先按这样的查询条件查出所有的店铺,发送MQ消息多个消费者进行消费排队处理
            List<String> accounts=smtViolationPunishRecordService.selectAccountsByExample(example);
            if(CollectionUtils.isEmpty(accounts)){
                XxlJobLogger.log("没有符合的违规处罚数据");
                return ReturnT.SUCCESS;
            }
            for(String account:accounts){
                AccountProductIdBean accountBean=new AccountProductIdBean();
                accountBean.setAccountNumber(account);
                accountBean.setSubType(innerParam.getSubType());
                accountBean.setSubRulesType(innerParam.getSubRulesType());
                accountBean.setAssetTypes(innerParam.getAssetTypes());
                rabbitMqSender.publishSmtVHostRabbitTemplateSend(PublishMqConfig.SMT_API_DIRECT_EXCHANGE, PublishQueues.SMT_SPU_CATEGORY_FORBID_PUBLISH_KEY,JSON.toJSON(accountBean));
            }
        } catch (Exception e) {
            AliexpressOperateLog aliexpressOperateLog = new AliexpressOperateLog();
            aliexpressOperateLog.setType(OperateLogEnum.SYNC_SPU_CATEGORY_FORBID_PUBLISH.getCode());
            aliexpressOperateLog.setMessage(e.getMessage());
            aliexpressOperateLog.setCreateBy("admin");
            aliexpressOperateLog.setCreateDate(new Timestamp(System.currentTimeMillis()));
            aliexpressOperateLogService.insert(aliexpressOperateLog);
            XxlJobLogger.log("执行报错==={}",e.getMessage());
        }
        return ReturnT.SUCCESS;
    }
}

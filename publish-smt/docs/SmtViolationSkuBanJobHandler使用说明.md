# SMT违规处罚SKU禁售规则自动处理定时任务

## 功能概述

该定时任务用于自动处理违规处罚列表中的SKU禁售逻辑，根据预设的禁售规则自动标记违规SKU为禁售状态。

## 主要功能

1. **规则匹配处理**：根据违规处罚列表的设置SKU禁售规则且启用状态的规则，匹配违规处罚列表数据
2. **过滤机制**：
   - 若数据设置禁售为是的数据，过滤不处理
   - 若列表SKU为空的数据，过滤不处理
3. **禁售标记**：若列表数据与规则匹配，将对应SKU标记禁售，记录设置禁售时间
4. **批量处理**：若某SKU被标记禁售后，违规处罚列表所有相同的SKU均标记禁售
5. **信息记录**：记录是否设置禁售、设置禁售时间，并将规则对应的禁售类型和禁售原因进行标记

## 涉及数据表

### 1. SmtViolationSkuInfringementRule（违规处罚SKU禁售规则表）
- **用途**：存储SKU禁售规则配置
- **关键字段**：
  - `status`：规则状态（1-启用，0-禁用）
  - `subType`：违规类型
  - `punishCause`：违规原因
  - `pointScoreStart/pointScoreEnd`：扣分范围
  - `pointCountStart/pointCountEnd`：计次范围
  - `infringementTypeName`：禁售类型
  - `infringementObj`：禁售原因

### 2. SmtViolationPunishRecord（违规处罚记录表）
- **用途**：存储违规处罚记录数据
- **关键字段**：
  - `skuCode`：SKU编码
  - `banFlag`：是否设置禁售
  - `banTime`：设置禁售时间
  - `infringementTypeName`：禁售类型
  - `infringementObj`：禁售原因
  - `operatedBy`：操作人（固定为"admin"）

## 匹配规则逻辑

任务会按照以下条件进行规则匹配：

1. **违规类型匹配**：记录的`subType`与规则的`subType`相等
2. **违规原因匹配**：记录的`punishCause`与规则的`punishCause`相等
3. **扣分范围匹配**：记录的`pointScore`在规则的`pointScoreStart`和`pointScoreEnd`范围内
4. **计次范围匹配**：记录的`pointCount`在规则的`pointCountStart`和`pointCountEnd`范围内

> 注意：如果规则中某个条件为空（null），则该条件不参与匹配判断

## 处理流程

```mermaid
flowchart TD
    A[开始执行任务] --> B[查询启用状态的SKU禁售规则]
    B --> C{是否有启用规则?}
    C -->|否| D[任务结束：未找到启用规则]
    C -->|是| E[查询未设置禁售且SKU不为空的违规记录]
    E --> F{是否有候选记录?}
    F -->|否| G[任务结束：未找到候选记录]
    F -->|是| H[遍历规则进行匹配]
    H --> I[根据规则条件匹配记录]
    I --> J{是否有匹配记录?}
    J -->|否| K[处理下一个规则]
    J -->|是| L[遍历匹配的记录]
    L --> M{SKU是否已处理?}
    M -->|是| N[跳过该记录]
    M -->|否| O[查找所有相同SKU的记录]
    O --> P[批量标记禁售]
    P --> Q[记录处理结果]
    Q --> R{是否还有记录?}
    R -->|是| L
    R -->|否| S{是否还有规则?}
    S -->|是| K
    S -->|否| T[任务完成：输出处理结果]
    N --> R
    K --> S
```

## 定时任务配置

### 任务名称
`SmtViolationSkuBanJobHandler`

### 执行参数
无需传入参数，任务会自动处理所有符合条件的数据。

### 建议执行频率
- **开发环境**：手动执行测试
- **生产环境**：建议每小时执行一次，或根据业务需求调整

## 日志输出

任务执行过程中会输出详细的日志信息：

1. **规则处理日志**：显示每个规则的处理情况
2. **匹配结果日志**：显示每个规则匹配到的记录数量
3. **SKU处理日志**：显示每个SKU的禁售标记情况
4. **最终结果日志**：显示总体处理结果统计

## 异常处理

1. **数据库连接异常**：任务会捕获异常并返回失败状态
2. **数据不一致异常**：记录详细错误信息，便于排查
3. **规则配置异常**：跳过有问题的规则，继续处理其他规则

## 监控建议

1. **执行结果监控**：关注任务返回状态和处理结果
2. **处理数量监控**：监控每次处理的SKU数量和记录数量
3. **执行时间监控**：关注任务执行耗时，优化性能
4. **异常监控**：及时发现和处理任务执行异常

## 注意事项

1. **数据一致性**：任务使用事务处理，确保数据一致性
2. **性能考虑**：大量数据处理时可能耗时较长，建议在业务低峰期执行
3. **规则配置**：确保禁售规则配置正确，避免误操作
4. **操作记录**：所有禁售操作都会记录操作人为"admin"，便于追溯

## 相关文件

- **定时任务处理器**：`SmtViolationSkuBanJobHandler.java`
- **业务服务接口**：`SmtViolationPunishRecordService.java`
- **业务服务实现**：`SmtViolationPunishRecordServiceImpl.java`
- **单元测试**：`SmtViolationSkuBanJobHandlerTest.java`

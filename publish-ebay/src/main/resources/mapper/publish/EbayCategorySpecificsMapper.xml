<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.estone.erp.publish.ebay.mapper.EbayCategorySpecificsMapper">
  <resultMap id="BaseResultMap" type="com.estone.erp.publish.ebay.model.EbayCategorySpecifics">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="site" jdbcType="VARCHAR" property="site" />
    <result column="category_id" jdbcType="VARCHAR" property="categoryId" />
    <result column="recommendation_pairs_js_string" jdbcType="VARCHAR" property="recommendationPairsJsString" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, site, category_id, recommendation_pairs_js_string
  </sql>
  <select id="selectByExample" parameterType="com.estone.erp.publish.ebay.model.EbayCategorySpecificsExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from ebay_category_specifics
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from ebay_category_specifics
    where id = #{id,jdbcType=BIGINT}
  </select>
  <select id="selectByUnique" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from ebay_category_specifics
    where site = #{site,jdbcType=VARCHAR} and category_id = #{categoryId,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from ebay_category_specifics
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.estone.erp.publish.ebay.model.EbayCategorySpecificsExample">
    delete from ebay_category_specifics
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.estone.erp.publish.ebay.model.EbayCategorySpecifics">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into ebay_category_specifics (site, category_id, recommendation_pairs_js_string
      )
    values (#{site,jdbcType=VARCHAR}, #{categoryId,jdbcType=VARCHAR}, #{recommendationPairsJsString,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.estone.erp.publish.ebay.model.EbayCategorySpecifics">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into ebay_category_specifics
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="site != null">
        site,
      </if>
      <if test="categoryId != null">
        category_id,
      </if>
      <if test="recommendationPairsJsString != null">
        recommendation_pairs_js_string,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="site != null">
        #{site,jdbcType=VARCHAR},
      </if>
      <if test="categoryId != null">
        #{categoryId,jdbcType=VARCHAR},
      </if>
      <if test="recommendationPairsJsString != null">
        #{recommendationPairsJsString,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.estone.erp.publish.ebay.model.EbayCategorySpecificsExample" resultType="java.lang.Integer">
    select count(*) from ebay_category_specifics
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update ebay_category_specifics
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.site != null">
        site = #{record.site,jdbcType=VARCHAR},
      </if>
      <if test="record.categoryId != null">
        category_id = #{record.categoryId,jdbcType=VARCHAR},
      </if>
      <if test="record.recommendationPairsJsString != null">
        recommendation_pairs_js_string = #{record.recommendationPairsJsString,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update ebay_category_specifics
    set id = #{record.id,jdbcType=BIGINT},
      site = #{record.site,jdbcType=VARCHAR},
      category_id = #{record.categoryId,jdbcType=VARCHAR},
      recommendation_pairs_js_string = #{record.recommendationPairsJsString,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.estone.erp.publish.ebay.model.EbayCategorySpecifics">
    update ebay_category_specifics
    <set>
      <if test="site != null">
        site = #{site,jdbcType=VARCHAR},
      </if>
      <if test="categoryId != null">
        category_id = #{categoryId,jdbcType=VARCHAR},
      </if>
      <if test="recommendationPairsJsString != null">
        recommendation_pairs_js_string = #{recommendationPairsJsString,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.estone.erp.publish.ebay.model.EbayCategorySpecifics">
    update ebay_category_specifics
    set site = #{site,jdbcType=VARCHAR},
      category_id = #{categoryId,jdbcType=VARCHAR},
      recommendation_pairs_js_string = #{recommendationPairsJsString,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>
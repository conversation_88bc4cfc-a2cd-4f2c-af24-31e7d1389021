package com.estone.erp.publish.ebay.bean;

import com.estone.erp.publish.system.product.ProductInfo;
import com.estone.erp.publish.system.product.bean.ComposeSku;
import com.estone.erp.publish.system.product.bean.SuiteSku;
import lombok.Data;

import java.util.List;

/**
 * @Auther yucm
 * @Date 2023/6/25
 */
@Data
public class EbayPublishCalcRequest {

    private List<String> articleNumbers;

    private String site;

    private Double profitMargin;

    /**
     * 数据来源
     */
    private Integer dataSource;

    /**
     * 管理单品
     */
    private List<ProductInfo> productInfos;

    /**
     * 组合
     */
    private ComposeSku composeProduct;

    /**
     * 套装
     */
    private SuiteSku suiteSku;

    /**
     * 是套装
     */
    private Boolean existSaleSuit;
}
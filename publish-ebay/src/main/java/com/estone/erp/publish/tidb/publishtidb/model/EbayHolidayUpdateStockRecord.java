package com.estone.erp.publish.tidb.publishtidb.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * ebay春节调库存记录
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-02
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class EbayHolidayUpdateStockRecord implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 店铺
     */
    @TableField("account_number")
    private String accountNumber;

    /**
     * 站点
     */
    @TableField("site")
    private String site;

    /**
     * itemId
     */
    @TableField("item_id")
    private String itemId;

    /**
     * sku
     */
    @TableField("sku")
    private String sku;

    /**
     * 单品状态
     */
    @TableField("sku_status")
    private String skuStatus;

    /**
     * 是否虚拟仓
     */
    @TableField("is_virtual_overseas_warehouse")
    private Boolean isVirtualOverseasWarehouse;

    /**
     * 改前库存
     */
    @TableField("before_stock")
    private Integer beforeStock;

    /**
     * 改后库存
     */
    @TableField("after_stock")
    private Integer afterStock;

    /**
     * 修改库存时间
     */
    @TableField("update_stock_time")
    private LocalDateTime updateStockTime;

    /**
     * 结果状态 0 待处理 1 成功 2 失败
     */
    @TableField("status")
    private Integer status;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

    /**
     * 恢复状态 -3 修复前后库存一样 -2 在线列表不存在 -1 重复数据 0 待处理 1 成功 2 失败
     */
    @TableField("recover_status")
    private Integer recoverStatus;

    /**
     * 恢复结果备注
     */
    @TableField("recover_remark")
    private String recoverRemark;

    /**
     * 恢复库存时间
     */
    @TableField("recover_stock_time")
    private LocalDateTime recoverStockTime;

    /**
     * 创建时间
     */
    @TableField("created_time")
    private LocalDateTime createdTime;

    /**
     * 更新时间
     */
    @TableField("updated_time")
    private LocalDateTime updatedTime;


}

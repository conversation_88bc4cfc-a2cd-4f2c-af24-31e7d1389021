package com.estone.erp.publish.ebay.service;

import com.estone.erp.common.model.api.CQuery;
import com.estone.erp.common.model.api.CQueryResult;
import com.estone.erp.publish.ebay.model.EbayCategoryCompatibility;
import com.estone.erp.publish.ebay.model.EbayCategoryCompatibilityCriteria;
import com.estone.erp.publish.ebay.model.EbayCategoryCompatibilityExample;

import java.util.List;

/**
 * <AUTHOR> ebay_category_compatibility
 * 2023-09-21 15:41:14
 */
public interface EbayCategoryCompatibilityService {
    int countByExample(EbayCategoryCompatibilityExample example);

    CQueryResult<EbayCategoryCompatibility> search(CQuery<EbayCategoryCompatibilityCriteria> cquery);

    List<EbayCategoryCompatibility> selectByExample(EbayCategoryCompatibilityExample example);

    EbayCategoryCompatibility selectByPrimaryKey(Long id);

    int insert(EbayCategoryCompatibility record);

    int updateByPrimaryKeySelective(EbayCategoryCompatibility record);

    int updateByExampleSelective(EbayCategoryCompatibility record, EbayCategoryCompatibilityExample example);

    int deleteByPrimaryKey(List<Long> ids);

    EbayCategoryCompatibility selectBySiteAndCategoryId(String site, String categoryId);

    EbayCategoryCompatibility selectBySiteAndCategoryId(String site, String categoryId, String accountNumber);
}
package com.estone.erp.publish.ebay.model;

import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR> ebaycategory
 * 2019-09-06 09:53:23
 */
public class EbayCategoryCriteria extends EbayCategory {
    private static final long serialVersionUID = 1L;

    public EbayCategoryExample getExample() {
        EbayCategoryExample example = new EbayCategoryExample();
        EbayCategoryExample.Criteria criteria = example.createCriteria();
        if (StringUtils.isNotBlank(this.getSite())) {
            criteria.andSiteEqualTo(this.getSite());
        }
        if (StringUtils.isNotBlank(this.getCategoryid())) {
            criteria.andCategoryidEqualTo(this.getCategoryid());
        }
        if (StringUtils.isNotBlank(this.getCategoryname())) {
            criteria.andCategorynameEqualTo(this.getCategoryname());
        }
        if (StringUtils.isNotBlank(this.getParentcategoryid())) {
            criteria.andParentcategoryidEqualTo(this.getParentcategoryid());
        }
        if (StringUtils.isNotBlank(this.getParentcategoryname())) {
            criteria.andParentcategorynameEqualTo(this.getParentcategoryname());
        }
        if (this.getCategorylevel() != null) {
            criteria.andCategorylevelEqualTo(this.getCategorylevel());
        }
        if (this.getLeafcategory() != null) {
            criteria.andLeafcategoryEqualTo(this.getLeafcategory());
        }
        if (StringUtils.isNotBlank(this.getFullpathname())) {
            criteria.andFullpathnameEqualTo(this.getFullpathname());
        }
        if (this.getVariationsEnabled() != null) {
            criteria.andVariationsEnabledEqualTo(this.getVariationsEnabled());
        }
        return example;
    }
}
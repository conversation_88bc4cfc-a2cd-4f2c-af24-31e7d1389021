package com.estone.erp.publish.ebay.service.impl;

import com.estone.erp.common.model.api.CQuery;
import com.estone.erp.common.model.api.CQueryResult;
import com.estone.erp.common.util.WebUtils;
import com.estone.erp.publish.ebay.mapper.EbayShippingServiceTemplateMapper;
import com.estone.erp.publish.ebay.model.EbayShippingServiceTemplate;
import com.estone.erp.publish.ebay.model.EbayShippingServiceTemplateCriteria;
import com.estone.erp.publish.ebay.model.EbayShippingServiceTemplateExample;
import com.estone.erp.publish.ebay.service.EbayShippingServiceTemplateService;

import java.sql.Timestamp;
import java.util.List;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

/**
 * <AUTHOR> ebay_shipping_service_template
 * 2019-09-11 10:52:07
 */
@Service("ebayShippingServiceTemplateService")
@Slf4j
public class EbayShippingServiceTemplateServiceImpl implements EbayShippingServiceTemplateService {
    @Resource
    private EbayShippingServiceTemplateMapper ebayShippingServiceTemplateMapper;

    @Override
    public int countByExample(EbayShippingServiceTemplateExample example) {
        Assert.notNull(example, "example is null!");
        return ebayShippingServiceTemplateMapper.countByExample(example);
    }

    @Override
    public CQueryResult<EbayShippingServiceTemplate> search(CQuery<EbayShippingServiceTemplateCriteria> cquery) {
        Assert.notNull(cquery, "cquery is null!");
        EbayShippingServiceTemplateCriteria query = cquery.getSearch();
        EbayShippingServiceTemplateExample example = query.getExample();
        long total = 0;
        int totalPages = 0;
        // 是否分页
        if (cquery.isPageReqired()) {
            total = ebayShippingServiceTemplateMapper.countByExample(example);
            totalPages = (int) Math.ceil((double) total / cquery.getLimit());
            example.setLimit(cquery.getLimit());
            example.setOffset(cquery.getOffset());
        }
        List<EbayShippingServiceTemplate> ebayShippingServiceTemplates = ebayShippingServiceTemplateMapper.selectByExample(example);
        // 组装结果
        CQueryResult<EbayShippingServiceTemplate> result = new CQueryResult<>();
        result.setTotal(total);
        result.setTotalPages(totalPages);
        result.setRows(ebayShippingServiceTemplates);
        return result;
    }

    @Override
    public EbayShippingServiceTemplate selectByPrimaryKey(Long id) {
        Assert.notNull(id, "id is null!");
        return ebayShippingServiceTemplateMapper.selectByPrimaryKey(id);
    }

    @Override
    public List<EbayShippingServiceTemplate> selectByExample(EbayShippingServiceTemplateExample example) {
        Assert.notNull(example, "example is null!");
        return ebayShippingServiceTemplateMapper.selectByExample(example);
    }

    @Override
    public int insert(EbayShippingServiceTemplate record) {
        Assert.notNull(record, "record is null!");
        record.setCreateDate(new Timestamp(System.currentTimeMillis()));
        record.setCreatedBy(WebUtils.getUserName());
        return ebayShippingServiceTemplateMapper.insert(record);
    }

    @Override
    public int insertSelective(EbayShippingServiceTemplate record) {
        Assert.notNull(record, "record is null!");
        return ebayShippingServiceTemplateMapper.insertSelective(record);
    }

    @Override
    public int updateByPrimaryKeySelective(EbayShippingServiceTemplate record) {
        Assert.notNull(record, "record is null!");
        return ebayShippingServiceTemplateMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public int updateByExampleSelective(EbayShippingServiceTemplate record, EbayShippingServiceTemplateExample example) {
        Assert.notNull(record, "record is null!");
        return ebayShippingServiceTemplateMapper.updateByExampleSelective(record, example);
    }

    @Override
    public int deleteByPrimaryKey(Long id) {
        Assert.notNull(id, "id is null!");
        return ebayShippingServiceTemplateMapper.deleteByPrimaryKey(id);
    }

    @Override
    public int deleteByExample(EbayShippingServiceTemplateExample example) {
        Assert.notNull(example, "example is null!");
        return ebayShippingServiceTemplateMapper.deleteByExample(example);
    }
}
package com.estone.erp.publish.ebay.service.impl;

import com.alibaba.fastjson.JSON;
import com.ebay.soap.eBLBaseComponents.ItemType;
import com.ebay.soap.eBLBaseComponents.VideoDetailsType;
import com.estone.erp.common.model.TidbPageMeta;
import com.estone.erp.common.model.api.CQuery;
import com.estone.erp.common.model.api.CQueryResult;
import com.estone.erp.common.util.TidbPageMetaUtil;
import com.estone.erp.common.util.WebUtils;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.common.component.StatusCode;
import com.estone.erp.publish.common.enums.FeedTaskResultStatusEnum;
import com.estone.erp.publish.common.executors.EbayExecutors;
import com.estone.erp.publish.common.json.ResponseJson;
import com.estone.erp.publish.ebay.call.EbayReviseItemCall;
import com.estone.erp.publish.ebay.enums.FeedTaskEnum;
import com.estone.erp.publish.ebay.mapper.EbayItemMapper;
import com.estone.erp.publish.ebay.model.EbayItem;
import com.estone.erp.publish.ebay.model.EbayItemCriteria;
import com.estone.erp.publish.ebay.model.EbayItemExample;
import com.estone.erp.publish.ebay.service.EbayItemEsService;
import com.estone.erp.publish.ebay.service.EbayItemService;
import com.estone.erp.publish.ebay.util.EbayFeedTaskUtils;
import com.estone.erp.publish.ebay.util.EbayItemUtils;
import com.estone.erp.publish.ebay.util.EbayTemplateTransformUtils;
import com.estone.erp.publish.elasticsearch4.model.EsEbayItem;
import com.estone.erp.publish.elasticsearch4.model.beanrequest.EsEbayItemRequest;
import com.estone.erp.publish.system.account.AccountUtils;
import com.estone.erp.publish.system.account.SaleAccountAndBusinessResponse;
import com.estone.erp.publish.system.feedTask.feedTaskEnum.TaskStatusEnum;
import com.estone.erp.publish.system.feedTask.model.FeedTask;
import com.estone.erp.publish.system.feedTask.service.FeedTaskService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> ebay_item
 * 2022-11-16 12:08:59
 */
@Service("ebayItemService")
@Slf4j
public class EbayItemServiceImpl implements EbayItemService {
    @Resource
    private EbayItemMapper ebayItemMapper;
    @Resource
    private EbayItemEsService ebayItemEsService;
    @Resource
    private FeedTaskService feedTaskService;

    @Override
    public int countByExample(EbayItemExample example) {
        Assert.notNull(example, "example is null!");
        return ebayItemMapper.countByExample(example);
    }

    @Override
    public CQueryResult<EbayItem> search(CQuery<EbayItemCriteria> cquery) {
        Assert.notNull(cquery, "cquery is null!");
        EbayItemCriteria query = cquery.getSearch();
        EbayItemExample example = query.getExample();
        long total = 0;
        int totalPages = 0;
        // 是否分页
        if (cquery.isPageReqired()) {
            total = ebayItemMapper.countByExample(example);
            totalPages = (int) Math.ceil((double) total / cquery.getLimit());
            example.setLimit(cquery.getLimit());
            example.setOffset(cquery.getOffset());
        }
        List<EbayItem> ebayItems = ebayItemMapper.selectByExample(example);
        // 组装结果
        CQueryResult<EbayItem> result = new CQueryResult<>();
        result.setTotal(total);
        result.setTotalPages(totalPages);
        result.setRows(ebayItems);
        return result;
    }

    @Override
    public EbayItem selectByPrimaryKey(Long id) {
        Assert.notNull(id, "id is null!");
        return ebayItemMapper.selectByPrimaryKey(id);
    }

    @Override
    public EbayItem selectByItemId(String itemId) {
        Assert.notNull(itemId, "itemId is null!");
        EbayItem ebayItem = ebayItemMapper.selectByItemId(itemId);
        return ebayItem;
    }

    @Override
    public List<EbayItem> selectByExample(EbayItemExample example) {
        Assert.notNull(example, "example is null!");
        return ebayItemMapper.selectByExample(example);
    }

    @Override
    public int insert(EbayItem record) {
        Assert.notNull(record, "record is null!");
        // 默认加时间和人
        record.setLastUpdateDate(new Timestamp(System.currentTimeMillis()));
        return ebayItemMapper.insert(record);
    }

    @Override
    public int save(EbayItem record) {
        if(null == record || StringUtils.isBlank(record.getItemId()) || StringUtils.isBlank(record.getEbayItem())) {
            return 0;
        }

        EbayItemExample example = new EbayItemExample();
        // 不查大对象字段
        example.setColumns("id, account_number, item_id, create_date, sync_date, last_update_date, last_update_by");
        example.createCriteria().andItemIdEqualTo(record.getItemId());
        List<EbayItem> ebayItems = this.selectByExample(example);
        // 不存在则插入 存在则更新
        if(CollectionUtils.isEmpty(ebayItems)) {
            record.setCreateDate(new Timestamp(System.currentTimeMillis()));
            record.setSyncDate(new Timestamp(System.currentTimeMillis()));
            return this.insert(record);
        } else {
            record.setSyncDate(new Timestamp(System.currentTimeMillis()));
            record.setId(ebayItems.get(0).getId());
            return this.updateByPrimaryKeySelective(record);
        }
    }

    @Override
    public int updateByPrimaryKeySelective(EbayItem record) {
        Assert.notNull(record, "record is null!");
        // 默认加修改时间和修改人
        record.setLastUpdateDate(new Timestamp(System.currentTimeMillis()));
        return ebayItemMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public void updateEbayItem(EbayItem record) {
        String userName = WebUtils.getUserName();
        FeedTask feedTask = EbayFeedTaskUtils.initEbayFeedTask(FeedTaskEnum.UPDATE_EBAY_ITEM, record.getAccountNumber(), userName);
        feedTask.setTaskStatus(TaskStatusEnum.EXECUTING.getStatusCode());
        feedTask.setAssociationId(record.getItemId());
        feedTask.setResultStatus(null);
        feedTaskService.insert(feedTask);

        EbayExecutors.executeUpdate(() -> {
            try {
                // 修改备注 修改本地记录 根据itemId修改 item下的子sku都需要修改
                EsEbayItemRequest updateRequest = new EsEbayItemRequest();
                updateRequest.setItemId(record.getItemId());
                Map<String, Object> updateMap = new HashMap<>();
                updateMap.put("remarks", record.getRemarks());
                ebayItemEsService.updateByQuery(updateRequest, updateMap);

                // 转平台对象结构
                ItemType itemType = JSON.parseObject(record.getEbayItem(), ItemType.class);
                String sku = itemType.getSKU();
                SaleAccountAndBusinessResponse ebayAccount = AccountUtils.getSaleAccountByAccountNumber(SaleChannel.CHANNEL_EBAY, record.getAccountNumber());
                String videoLink = record.getVideoLink();
                if (StringUtils.isNotBlank(videoLink)) {
                    // 处理mp4的短视频，其他的不需要处理
                    if (videoLink.endsWith(".mp4")) {
                        // 如果是mp4 就证明是需要上传的视频
                        String videoId = EbayTemplateTransformUtils.buildVideo(ebayAccount, sku, videoLink);
                        if (StringUtils.isNotBlank(videoId)) {
                            VideoDetailsType videoDetailsType = new VideoDetailsType();
                            videoDetailsType.setVideoID(new String[] {videoId});
                            itemType.setVideoDetails(videoDetailsType);
                        }
                    }
                } else {
                    VideoDetailsType videoDetailsType = new VideoDetailsType();
                    videoDetailsType.setVideoID(new String[]{});
                    itemType.setVideoDetails(videoDetailsType);
                }

                // 上传图片 非ebay图片 需要先上传阿里云 再上传ebay
                EbayItemUtils.uploadImage(itemType, ebayAccount);

                // 上传前 部分字段不可以原样传给平台 需要处理为空
                EbayItemUtils.handleReviseItemType(itemType, ebayAccount);

                // 请求平台修改数据
                EbayReviseItemCall call = new EbayReviseItemCall(ebayAccount);
                ResponseJson rsp = call.reviseItem(itemType);
//                ResponseJson rsp = new ResponseJson();
                if(StatusCode.SUCCESS.equals(rsp.getStatus())) {
                        // 成功后同步平台数据 本地数据修改字段太多不好处理难保持和在线列表数据统一
                        ebayItemEsService.syncByItemId(ebayAccount, record.getItemId());
                        // 记录日志
                    feedTaskService.updateFeedTaskToFinish(feedTask, TaskStatusEnum.FINISH.getStatusCode(), FeedTaskResultStatusEnum.SUCCESS.getResultStatus(), null);
                } else {
                    String message = rsp.getMessage();
                    if(StringUtils.isBlank(message)) {
                        message = "error message is null" + JSON.toJSONString(rsp);
                    }
                    feedTaskService.updateFeedTaskToFinish(feedTask, TaskStatusEnum.FINISH.getStatusCode(), FeedTaskResultStatusEnum.FAIL.getResultStatus(), message);
                }
            }catch (Exception e) {
                log.error("refreshNotSynchItemIdNumber failed ", e);
                feedTaskService.updateFeedTaskToFinish(feedTask, TaskStatusEnum.FINISH.getStatusCode(), FeedTaskResultStatusEnum.FAIL.getResultStatus(), e.getMessage());
            }
        });
    }

    @Override
    public int updateByExampleSelective(EbayItem record, EbayItemExample example) {
        Assert.notNull(record, "record is null!");
        // 默认加修改时间和修改人
        record.setLastUpdateDate(new Timestamp(System.currentTimeMillis()));
        return ebayItemMapper.updateByExampleSelective(record, example);
    }

    @Override
    public int deleteByPrimaryKey(List<Long> ids) {
        Assert.notNull(ids, "ids is null!");
        return ebayItemMapper.deleteByPrimaryKey(ids);
    }

    @Override
    public void keepDataUnifiedByEbayItemSummary(EbayItem ebayItem) {
        if(ebayItem == null || StringUtils.isBlank(ebayItem.getItemId())) {
            return;
        }
        List<EsEbayItem> esEbayItems = ebayItemEsService.getEsEbayItemsByItemId(ebayItem.getItemId(), null);
        EbayItemUtils.keepDataUnifiedByEbayItemSummary(ebayItem, esEbayItems);
    }

    @Override
    public List<TidbPageMeta<Long>> getTidbPageMetaMap(EbayItemExample example){
        List<Map<Object, Object>> tidbPageMetaMap = ebayItemMapper.getTidbPageMetaMap(example);
        return TidbPageMetaUtil.getPageMetaList(tidbPageMetaMap);
    }
}
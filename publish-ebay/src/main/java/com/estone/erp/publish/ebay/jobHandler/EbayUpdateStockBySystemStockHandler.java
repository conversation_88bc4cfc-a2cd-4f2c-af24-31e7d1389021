package com.estone.erp.publish.ebay.jobHandler;

import com.alibaba.fastjson.JSON;
import com.estone.erp.common.elasticsearch.util.PageInfo;
import com.estone.erp.common.jobHandler.AbstractJobHandler;
import com.estone.erp.publish.common.executors.EbayExecutors;
import com.estone.erp.publish.ebay.enums.EbayFeedTaskMsgEnum;
import com.estone.erp.publish.ebay.service.EbayItemEsService;
import com.estone.erp.publish.ebay.util.EbayCommonUtils;
import com.estone.erp.publish.elasticsearch4.model.EsEbayItem;
import com.estone.erp.publish.elasticsearch4.model.beanrequest.EsEbayItemRequest;
import com.estone.erp.publish.system.product.util.SkuStockUtils;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.time.StopWatch;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Iterator;
import java.util.List;
import java.util.concurrent.Semaphore;

/**
 * 支持配置店铺，若系统剩余可用库存（可用库存 - 待发）小于10（参数），则将链接库存改为0
 * 改为  可用-待发 小于10（参数），则将链接库存改为0
 * @Auther yucm
 * @Date 2023/2/1
 */
@Component
@Slf4j
public class EbayUpdateStockBySystemStockHandler extends AbstractJobHandler {

    @Resource
    private EbayItemEsService ebayItemEsService;

    @Getter
    @Setter
    static class InnerParam{
        private List<String> accountNumberList;
        private Integer updateStock = 0;
        // 账号线程池数量
        private int threadNum = 5;

        /**
         * skuList
         */
        private List<String> skuList;
    }

    public EbayUpdateStockBySystemStockHandler() {
        super(EbayUpdateStockBySystemStockHandler.class.getName());
    }

    @Override
    @XxlJob("EbayUpdateStockBySystemStockHandler")
    public ReturnT<String> run(String param) throws Exception {
        InnerParam innerParam = null;
        try {
            innerParam = JSON.parseObject(param, InnerParam.class);
        }catch (Exception e){
            XxlJobLogger.log("解析错误{}；",e);
        }
        if(innerParam == null) {
            innerParam = new InnerParam();
        }
        List<String> accountNumberList = innerParam.getAccountNumberList();
        List<String> skuList = innerParam.getSkuList();
        Integer updateStock = innerParam.getUpdateStock();
        if(null == updateStock) {
            XxlJobLogger.log("updateStock 不可以为null");
            return ReturnT.FAIL;
        }

        int threadNum = innerParam.getThreadNum();
        final Semaphore sp = new Semaphore(threadNum);

        Integer toRemainingAvailableStock = EbayCommonUtils.getSystemVirtualOverseasToRemainingAvailableStock();
        if(null == toRemainingAvailableStock ) {
            XxlJobLogger.log("虚拟海外仓剩余可用库存范围值配置参数");
            return ReturnT.FAIL;
        }

        if(CollectionUtils.isEmpty(accountNumberList)) {// 系统参数 获取虚拟海外仓账号
            accountNumberList = EbayCommonUtils.getSystemVirtualOverseasWarehouseAccounts();
        }
        if(CollectionUtils.isEmpty(accountNumberList)) {
            XxlJobLogger.log("请任务参数配置账号或系统参数配置账号");
            return ReturnT.FAIL;
        }

        Collections.shuffle(accountNumberList);
        for (String accountNumber : accountNumberList) {
            EbayExecutors.executeTaskUpdateItemStock(() -> {
                try {
                    StopWatch stopWatch = StopWatch.createStarted();
                    log.info("店铺{} 海外仓库存调零开始执行", accountNumber);
                    executeBySkuAndAccounts(accountNumber, updateStock, skuList, toRemainingAvailableStock);
                    log.info("店铺{} 海外仓库存调零结束执行，耗时{}", accountNumber, stopWatch);
                }catch (Exception e) {
                    log.error("店铺{} 海外仓库存调零结束执行失败：{}", accountNumber, e.getMessage(), e);
                } finally {
                    // 执行完之后，释放许可，许可数加1
                    sp.release();
                }
            });
        }

        return ReturnT.SUCCESS;
    }

    private void executeBySkuAndAccounts(String accountNumber, Integer updateStock, List<String> skuList, Integer toRemainingAvailableStock) {
        EsEbayItemRequest request = new EsEbayItemRequest();
        request.setFields(new String[]{"id", "accountNumber", "itemId", "sellerSku", "articleNumber", "skuStatus", "systemStock"});
        request.setOrderBy("id");
        request.setSequence("ASC");
        request.setPageSize(1000);
        request.setPageIndex(0);
        request.setAccountNumber(accountNumber);
        if (CollectionUtils.isNotEmpty(skuList)) {
            request.setArticleNumbers(skuList);
        }
        request.setNotQuantityAvailable(updateStock);
        request.setIsOnline(true);
        String maxId = null;
        while (true) {
            request.setGtId(maxId);
            PageInfo<EsEbayItem> page = ebayItemEsService.pageInfo(request);
            if (page == null || CollectionUtils.isEmpty(page.getContents())) {
                break;
            }

            List<EsEbayItem> esEbayItems = page.getContents();
            maxId = esEbayItems.get(esEbayItems.size() - 1).getId();

            buildUpdateQuantityRequests(esEbayItems, updateStock, toRemainingAvailableStock);
            ebayItemEsService.batchUpdatePriceAndQuantity(esEbayItems, EbayFeedTaskMsgEnum.SYSTEM_REMAINING_AVAILABLE_UPDATE_STOCK, "系统自动");
        }
    }

    private void buildUpdateQuantityRequests(List<EsEbayItem> esEbayItems, Integer updateStock, Integer toRemainingAvailableStock) {
        Iterator<EsEbayItem> it = esEbayItems.iterator();
        while (it.hasNext()) {
            EsEbayItem item = it.next();
            if (null == item.getSystemStock() || item.getSystemStock() >= toRemainingAvailableStock) {
                Integer skuStock = SkuStockUtils.getSkuStockToEbay(item.getArticleNumber());
                if (null == skuStock || skuStock >= toRemainingAvailableStock) {
                    it.remove();
                    continue;
                }
            }
            item.setQuantityAvailable(updateStock);
        }
    }
}

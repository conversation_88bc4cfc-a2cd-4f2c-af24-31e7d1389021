package com.estone.erp.publish.ebay.componet;

import com.estone.erp.publish.common.Constant;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.common.context.DashboardStatistContext;
import com.estone.erp.publish.common.model.dto.ShopMonthTotalDataDO;
import com.estone.erp.publish.component.AbstractDashboardStatisticsDataHandler;
import com.estone.erp.publish.ebay.service.EbayAccountConfigService;
import com.estone.erp.publish.elasticsearch2.model.EsSalesStatisticsData;
import com.estone.erp.publish.elasticsearch4.service.EsEbayItemService;
import com.estone.erp.publish.system.account.ChannelEnum.SaleChannelEnum;
import com.estone.erp.publish.system.param.model.SystemParam;
import com.estone.erp.publish.system.param.service.SystemParamService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 *
 * <AUTHOR>
 * @date 2023-01-11 14:30
 */
@Slf4j
@Component
public class EbayDashboardStatisticsHandler extends AbstractDashboardStatisticsDataHandler {

    @Resource
    private EsEbayItemService esEbayItemService;
    @Resource
    private SystemParamService systemParamService;
    @Resource
    private EbayAccountConfigService ebayAccountConfigService;

    /**
     * 统计平台
     *
     * @return String
     */
    @Override
    public String getPlatform() {
        return SaleChannelEnum.EBAY.getChannelName();
    }

    /**
     * 补充特殊链接数据
     *
     * @param context
     * @param data    统计数据
     */
    @Override
    protected void assemblyListingData(DashboardStatistContext context, EsSalesStatisticsData data) {
        List<String> accountNumberList = context.getAccountNumberList();
        Integer stockThreshold = context.getStockThreshold();
        Double grossThreshold = context.getGrossThreshold();
        // listing链接指数 侵权禁售链接数,不及格毛利链接数,包含侵权词链接
        int onlineListingNum = esEbayItemService.getOnlineListingNum(accountNumberList);
        int forbiddenListingNum = esEbayItemService.getForbiddenListingNum(accountNumberList);
        int subGrossProfitListingNum = esEbayItemService.getSubGrossProfitListingNum(accountNumberList, grossThreshold);
        int stopStatusListingNum = esEbayItemService.getStopStatusListingNum(accountNumberList);
        int notEnoughStockListingNum = esEbayItemService.getNotEnoughStockListingNum(accountNumberList, stockThreshold);

        data.setOnlineListingNum(onlineListingNum);
        data.setForbiddenListingNum(forbiddenListingNum);
        data.setSubGrossProfitListingNum(subGrossProfitListingNum);
        data.setStopStatusListingNum(stopStatusListingNum);
        data.setNotEnoughStockListingNum(notEnoughStockListingNum);
    }

    /**
     * 设置特殊链接的阈值
     *
     * @param context context
     */
    @Override
    public void setListingThreshold(DashboardStatistContext context) {
        try {
            SystemParam grossParam = systemParamService.queryParamValue(SaleChannel.CHANNEL_EBAY, Constant.STATISTICS, Constant.GROSS_THRESHOLD);
            SystemParam stockParam = systemParamService.queryParamValue(SaleChannel.CHANNEL_EBAY, Constant.STATISTICS, Constant.STOCK_THRESHOLD);
            if (grossParam != null) {
                context.setGrossThreshold(Double.valueOf(grossParam.getParamValue()));
            }
            if (stockParam != null) {
                context.setStockThreshold(Integer.valueOf(stockParam.getParamValue()));
            }
        } catch (Exception e) {
            log.error("设置Ebay 特殊链接阈值异常", e);
        }
    }

    /**
     * 设置店铺目标总值
     *
     * @param context context
     * @param data
     */
    @Override
    protected void setAccountConfigTargetData(DashboardStatistContext context, EsSalesStatisticsData data) {
        List<String> accountNumberList = context.getAccountNumberList();
        // 店铺目标配置
        ShopMonthTotalDataDO shopMonthTotalDataDO = ebayAccountConfigService.sumTarget(accountNumberList);
        if(shopMonthTotalDataDO != null) {
            data.setMonthSaleTarget(shopMonthTotalDataDO.getSalesTotal());
            data.setMonthAddListingTarget(shopMonthTotalDataDO.getAddListingTotal());
        }
    }

    /**
     * 获取指定时间段内的店铺新增链接数
     *
     * @return 新增链接数
     */
    @Override
    protected Long getRangeTimeAddListingTotal(String accountNumber, String starTime, String endTime) {
        return (long)esEbayItemService.getRangeTimeAddListingTotal(accountNumber, starTime, endTime);
    }
}

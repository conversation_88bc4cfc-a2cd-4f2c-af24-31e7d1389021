package com.estone.erp.publish.ebay.mapper.custom;

import com.estone.erp.publish.ebay.bean.EbayItemUpdateImage;
import com.estone.erp.publish.ebay.model.EbayItemSummary;
import com.estone.erp.publish.ebay.model.EbayItemSummaryExample;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

public interface CustomEbayItemSummaryMapper {

    int searchEbayItemsCount(EbayItemSummaryExample example);

    List<EbayItemSummary> searchEbayItems(EbayItemSummaryExample example);

    void batchUpdateEbayItemSummaryPriceAndQuantity(@Param("itemList") List<EbayItemSummary> entityList);

    void batchUpdateEbayItemSummaryQuantityByItemId(@Param("itemList") List<EbayItemSummary> entityList);

    void batchUpdateEbayItemSummary(@Param("itemList") List<EbayItemSummary> updateEbayItemList);

    void updateRemarksByItemId(@Param("remarks")String remarks, @Param("itemId")String itemId);

    void batchUpdateRemarksById(@Param("remarks")String remarks, @Param("ids")List<Long> ids);

    List<String> selectAllListingItemId();

    List<EbayItemSummary> selectCustomInfoByIds(@Param("list")List<Long> list);

    List<EbayItemSummary> selectListing(@Param("accountNumber") String account, @Param("endDate") Date endDate);

    List<EbayItemSummary> selectCategoryMapping(String accountNumber);

    List<EbayItemUpdateImage> selectItemSummaryForUpdateImg(@Param("ids")List<Long> ids);

    List<String> selectItemIdByTitle(@Param("title")String title, @Param("articleNumber") String articleNumber);

    int listByAccountAndSku(@Param("account")String account, @Param("skuList")List<String> skuList);

    List<EbayItemSummary> selectItemCountry(@Param("itemIds")List<String> itemIds);
}
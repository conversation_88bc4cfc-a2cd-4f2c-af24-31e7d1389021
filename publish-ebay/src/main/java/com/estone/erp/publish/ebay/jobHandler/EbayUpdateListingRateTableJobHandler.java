package com.estone.erp.publish.ebay.jobHandler;

import com.alibaba.fastjson.JSON;
import com.estone.erp.common.elasticsearch.util.PageInfo;
import com.estone.erp.common.jobHandler.AbstractJobHandler;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.common.executors.EbayExecutors;
import com.estone.erp.publish.ebay.bean.EbayRateTables;
import com.estone.erp.publish.ebay.call.sellAccount.v1.EbayGetRateTableCall;
import com.estone.erp.publish.ebay.model.EbayAccountRateTable;
import com.estone.erp.publish.ebay.service.EbayAccountRateTableService;
import com.estone.erp.publish.ebay.service.EbayItemEsService;
import com.estone.erp.publish.elasticsearch4.model.EsEbayItem;
import com.estone.erp.publish.elasticsearch4.model.beanrequest.EsEbayItemRequest;
import com.estone.erp.publish.system.account.AccountUtils;
import com.estone.erp.publish.system.account.SaleAccountAndBusinessResponse;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;

/**
 * 修改在线listing 费率表
 *
 * <AUTHOR>
 * @date 2024-02-20 10:17
 */
@Component
public class EbayUpdateListingRateTableJobHandler extends AbstractJobHandler {

    @Autowired
    private EbayItemEsService ebayItemEsService;
    @Resource
    private EbayAccountRateTableService ebayAccountRateTableService;


    public EbayUpdateListingRateTableJobHandler() {
        super(EbayUpdateListingRateTableJobHandler.class.getName());
    }

    @Data
    private static class InnerParam {
        private List<String> accountNumbers;
        private List<String> itemIds;
        private String tableName;
        private String starCreatedTime;
        private String endCreatedTime;
        private String site;
    }

    @Override
    @XxlJob("EbayUpdateListingRateTableJobHandler")
    public ReturnT<String> run(String param) throws Exception {
        InnerParam innerParam = passParam(param, InnerParam.class);
        if (innerParam == null) {
            XxlJobLogger.log("执行完毕，参数不能为空");
            return ReturnT.SUCCESS;
        }

        List<String> accountNumbers = innerParam.getAccountNumbers();
        if (CollectionUtils.isEmpty(accountNumbers)) {
            XxlJobLogger.log("待执行店铺为空");
            return ReturnT.SUCCESS;
        }

        for (String accountNumber : accountNumbers) {
            XxlJobLogger.log("店铺:{},执行修改链接费率表", accountNumber);
            updateAccountListingRateTable(accountNumber, innerParam);
        }
        XxlJobLogger.log("执行完毕");
        return ReturnT.SUCCESS;
    }

    private void updateAccountListingRateTable(String accountNumber, InnerParam innerParam) {
        SaleAccountAndBusinessResponse ebayAccount = AccountUtils.getSaleAccountByAccountNumber(SaleChannel.CHANNEL_EBAY, accountNumber);
        if (ebayAccount == null) {
            XxlJobLogger.log("订单账户为空", accountNumber);
            return;
        }
        // 获取对应的费率表Id
        EbayGetRateTableCall rateTableCall = new EbayGetRateTableCall(ebayAccount);
        List<EbayRateTables> siteRateTables = rateTableCall.getSiteRateTables(innerParam.getSite());
        if (CollectionUtils.isEmpty(siteRateTables)) {
            XxlJobLogger.log("获取店铺:{},对应站点:{},费率表为空", accountNumber, innerParam.getSite());
            return;
        }
        Optional<EbayRateTables> siteRateTable = siteRateTables.stream()
                .filter(rateTable -> innerParam.getTableName().equals(rateTable.getName())).findFirst();
        if (siteRateTable.isEmpty()) {
            XxlJobLogger.log("获取店铺:{},对应站点:{},无对应名称:{},费率表:{}", accountNumber, innerParam.getSite(), innerParam.getTableName(), JSON.toJSONString(siteRateTables));
            return;
        }

        EbayRateTables ebayRateTables = siteRateTable.get();
        // 获取费率表
        EbayAccountRateTable accountRateTable = ebayAccountRateTableService.selectByAccountRateTableId(accountNumber, ebayRateTables.getRateTableId());
        if (null == accountRateTable) {
            XxlJobLogger.log("查询不到对应费率表");
            return;
        }

        EsEbayItemRequest request = new EsEbayItemRequest();
        request.setFields(new String[]{"id", "accountNumber", "itemId", "shippingDetails"});
        if (CollectionUtils.isNotEmpty(innerParam.getItemIds())) {
            request.setItemIds(innerParam.getItemIds());
        }
        if (StringUtils.isNotBlank(innerParam.getStarCreatedTime())) {
            request.setFromStartDate(innerParam.getStarCreatedTime());
        }
        if (StringUtils.isNotBlank(innerParam.getEndCreatedTime())) {
            request.setToStartDate(innerParam.getEndCreatedTime());
        }
        request.setOrderBy("id");
        request.setSequence("ASC");
        String maxId = null;
        int total = 0;
        while (true) {
            request.setPageSize(1000);
            request.setPageIndex(0);
            request.setIsOnline(true);
            request.setAccountNumber(accountNumber);
            request.setGtId(maxId);
            PageInfo<EsEbayItem> page = ebayItemEsService.pageInfo(request);
            if (page == null || CollectionUtils.isEmpty(page.getContents())) {
                XxlJobLogger.log(accountNumber + "end");
                break;
            }
            List<EsEbayItem> esEbayItems = page.getContents();
            maxId = esEbayItems.get(esEbayItems.size() - 1).getId();
            CountDownLatch countDownLatch = new CountDownLatch(esEbayItems.size());
            for (EsEbayItem esEbayItem : esEbayItems) {
                EbayExecutors.executeUpdate(() -> {
                    try {
                        ebayItemEsService.updateItemRateTable(esEbayItem, accountRateTable, "admin");
                    } catch (Exception e) {
                        XxlJobLogger.log("修改在线listing修改费率表异常-----itemId：" + esEbayItem.getItemId());
                        logger.error("修改在线listing修改费率表异常-----itemId：" + esEbayItem.getItemId(), e);
                    }finally {
                        countDownLatch.countDown();
                    }
                });
            }
            total += esEbayItems.size();
            XxlJobLogger.log("accountNumber:{}, total:{}", accountNumber, total);
            try {
                countDownLatch.await(12, TimeUnit.HOURS);
            } catch (InterruptedException e) {
                logger.error("修改处理时间 countDownLatch.await超时 出错", e);
            }
        }
    }
}

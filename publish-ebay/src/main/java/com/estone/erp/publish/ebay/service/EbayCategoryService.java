package com.estone.erp.publish.ebay.service;

import com.estone.erp.common.model.api.CQuery;
import com.estone.erp.common.model.api.CQueryResult;
import com.estone.erp.publish.ebay.bean.EbayCategoryVO;
import com.estone.erp.publish.ebay.model.EbayCategory;
import com.estone.erp.publish.ebay.model.EbayCategoryCriteria;
import com.estone.erp.publish.ebay.model.EbayCategoryExample;
import com.estone.erp.publish.system.account.SaleAccountAndBusinessResponse;

import java.util.List;

/**
 * <AUTHOR> ebaycategory
 * 2019-09-06 09:53:23
 */
public interface EbayCategoryService {
    int countByExample(EbayCategoryExample example);

    CQueryResult<EbayCategory> search(CQuery<EbayCategoryCriteria> cquery);

    List<EbayCategory> selectByExample(EbayCategoryExample example);

    EbayCategory selectByPrimaryKey(Long id);

    int insert(EbayCategory record);

    int insertSelective(EbayCategory record);

    int updateByPrimaryKeySelective(EbayCategory record);

    int updateByPrimaryKey(EbayCategory record);

    int updateByExampleSelective(EbayCategory record, EbayCategoryExample example);

    int deleteByPrimaryKey(Long id);

    int deleteByExample(EbayCategoryExample example);

    EbayCategory selectByExampleOnly(EbayCategoryExample example);

    EbayCategory selectBySiteAndCategoryId(String site, String categoryId);

    List<EbayCategory> getSuggestedCategoriesByKeywords(String keywords, String accountNumber, String site) throws Exception;

    void syncEbayCategoryTree(String site, SaleAccountAndBusinessResponse ebayAccount) throws Exception;

    List<String> getCategoryIdBySite(String site);

    List<EbayCategory> selectCategoryBySite(String site);

    void deleteExpireBySite(String site, Integer days);

    /**
     * 获取对应站点类目树
     * @param site 站点
     * @return 类目树
     */
    List<EbayCategoryVO> getCategorySiteTree(String site);
}
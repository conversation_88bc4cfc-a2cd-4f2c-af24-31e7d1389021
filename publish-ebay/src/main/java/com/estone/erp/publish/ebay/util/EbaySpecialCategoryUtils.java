package com.estone.erp.publish.ebay.util;

import com.estone.erp.common.constant.RedisConstant;
import com.estone.erp.common.util.CommonUtils;
import com.estone.erp.common.util.PublishRedisClusterUtils;
import com.estone.erp.common.util.SpringUtils;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.common.component.StatusCode;
import com.estone.erp.publish.common.json.ResponseJson;
import com.estone.erp.publish.ebay.call.EbayGetCategoryFeaturesCall;
import com.estone.erp.publish.ebay.model.EbayAccountConfig;
import com.estone.erp.publish.ebay.model.EbayCategory;
import com.estone.erp.publish.ebay.service.EbayAccountConfigService;
import com.estone.erp.publish.ebay.service.EbayCategoryService;
import com.estone.erp.publish.system.account.AccountUtils;
import com.estone.erp.publish.system.account.SaleAccountAndBusinessResponse;
import com.estone.erp.publish.system.param.model.SystemParam;
import com.estone.erp.publish.system.param.service.SystemParamService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang.StringUtils;

import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @description:
 * @date 2020/5/2911:58
 */
@Slf4j
public class EbaySpecialCategoryUtils {

    private final static String cacheId = "ebay_category_special_id";

    private final static String paymentId = "ebay_category_payment_id";

    private final static String auPaymentId = "ebay_category.au_payment_id";

    private final static String DISABLED_ID = RedisConstant.EBAY_PREFIX_SYSTEM + "ebay_category_disabled_id";

    private final static String cacheAccount = "ebay_category_exclude_account";

    public static Boolean check(String primaryCategoryId, String account){
        if(StringUtils.isNotBlank(account)){
            EbayAccountConfigService ebayAccountConfigService = SpringUtils.getBean(EbayAccountConfigService.class);
            EbayAccountConfig ebayAccountConfig = ebayAccountConfigService.selectByAccountNumber(account);

            // 如果账号允许刊登敏感分类 直接return false
            if(ebayAccountConfig != null && BooleanUtils.isTrue(ebayAccountConfig.getIsAllowSpecialCategory())) {
                return false;
            }
        }

        String specialCategory = getSpecialCategory();
        if(StringUtils.isBlank(specialCategory) || StringUtils.isBlank(primaryCategoryId)){
            return false;
        }

        List<String> strings = CommonUtils.splitList(specialCategory, ",");
        return strings.contains(primaryCategoryId);
    }

    public static Boolean checkPaymentCategory(String site,String primaryCategoryId) {
        if(StringUtils.isBlank(site) || StringUtils.isBlank(primaryCategoryId)){
            return false;
        }

        // 只校验美国站点
        if (!site.equals("US")) {
            return false;
        }

        // 获取分类信息，如果缓存中没有，则从数据库中获取
        String cacheProduct = "";
        SystemParamService systemParamService = SpringUtils.getBean(SystemParamService.class);
        try{
            //缓存获取
            cacheProduct = PublishRedisClusterUtils.get(paymentId);
            if(StringUtils.isBlank(cacheProduct)){
                SystemParam systemParam = systemParamService.querySystemParamByCodeKey("ebay_category.payment_id");
                String paramValue = systemParam.getParamValue();
                if(StringUtils.isNotBlank(paramValue)){
                    cacheProduct = paramValue;
                    PublishRedisClusterUtils.set(paymentId, paramValue, 5, TimeUnit.MINUTES);
                }
                cacheProduct = paramValue;
            }
        }catch (Exception e){
            log.error(e.getMessage(), e);
        }

        // 校验分类是否包含
        List<String> strings = CommonUtils.splitList(cacheProduct, ",");
        boolean contains = strings.contains(primaryCategoryId);
        log.info("checkPaymentCategory方法-校验分类是否包含-状态为:{}", contains);

        return contains;
    }


    public static Boolean checkAuPaymentCategory(String site,String primaryCategoryId) {
        if(StringUtils.isBlank(site) || StringUtils.isBlank(primaryCategoryId)){
            return false;
        }

        // 只校验Australia站点
        if (!site.equals("Australia")) {
            return false;
        }

        // 获取分类信息，如果缓存中没有，则从数据库中获取
        String cacheProduct = "";
        SystemParamService systemParamService = SpringUtils.getBean(SystemParamService.class);
        try{
            //缓存获取
            cacheProduct = PublishRedisClusterUtils.get(auPaymentId);
            if(StringUtils.isBlank(cacheProduct)){
                SystemParam systemParam = systemParamService.querySystemParamByCodeKey(auPaymentId);
                String paramValue = systemParam.getParamValue();
                if(StringUtils.isNotBlank(paramValue)){
                    cacheProduct = paramValue;
                    PublishRedisClusterUtils.set(auPaymentId, paramValue, 5, TimeUnit.MINUTES);
                }
                cacheProduct = paramValue;
            }
        }catch (Exception e){
            log.error(e.getMessage(), e);
        }

        // 校验分类是否包含
        List<String> strings = CommonUtils.splitList(cacheProduct, ",");
        boolean contains = strings.contains(primaryCategoryId);
        log.info("checkAuPaymentCategory方法-校验分类是否包含-状态为:{}", contains);

        return contains;
    }

    public static String getSpecialCategory(){

        String cacheProduct = "";

        SystemParamService systemParamService = SpringUtils.getBean(SystemParamService.class);

        try{
            //缓存获取
            cacheProduct = PublishRedisClusterUtils.get(cacheId);

            if(StringUtils.isBlank(cacheProduct)){
                    SystemParam systemParam = systemParamService.querySystemParamByCodeKey("ebay_category.special_id");

                String paramValue = systemParam.getParamValue();

                if(StringUtils.isNotBlank(paramValue)){
                    //缓存1天
                    PublishRedisClusterUtils.set(cacheId, paramValue, 5, TimeUnit.MINUTES);
                }
                cacheProduct = paramValue;
            }

        }catch (Exception e){
            log.error(e.getMessage(), e);
        }
        return cacheProduct;
    }

    /**
     * 校验是否支持多属性
     * @param site
     * @param categoryId
     * @param account
     * @return
     */
    public static Boolean checkSupportVariation(String site, String categoryId, SaleAccountAndBusinessResponse account) {
        Boolean isSupportVariation = null;

        EbayCategoryService ebayCategoryService = SpringUtils.getBean(EbayCategoryService.class);

        //查询分类是否支持多属性,先本地查询,在同步
        EbayCategory ebayCategory = ebayCategoryService.selectBySiteAndCategoryId(site, categoryId);
        if(ebayCategory !=null && ebayCategory.getVariationsEnabled() != null) {
            isSupportVariation = ebayCategory.getVariationsEnabled();
        }else {
            // 同步平台分类特征
            EbayGetCategoryFeaturesCall featuresCall = new EbayGetCategoryFeaturesCall(account);
            ResponseJson categoryFeaturesRsp = featuresCall.getCategoryFeatures(categoryId, site);

            //同步成功，保存在本地
            if(StringUtils.equalsIgnoreCase(StatusCode.SUCCESS, categoryFeaturesRsp.getStatus()) && categoryFeaturesRsp.getBody() != null) {
                String variationsEnabled =  categoryFeaturesRsp.getBody().get("variationsEnabled").toString();
                String ISBNEnabled =  categoryFeaturesRsp.getBody().get("ISBNEnabled").toString();
                String itemCompatibilityEnabledCode =  categoryFeaturesRsp.getBody().get("itemCompatibilityEnabledCode").toString();

                // 修改本地
                EbayCategory newEbayCategory = new EbayCategory();
                newEbayCategory.setId(ebayCategory.getId());
                newEbayCategory.setVariationsEnabled(variationsEnabled.equals("1"));
                newEbayCategory.setISBNEnabled(ISBNEnabled.equals("1"));
                newEbayCategory.setItemCompatibilityEnabledCode(itemCompatibilityEnabledCode);
                ebayCategoryService.updateByPrimaryKeySelective(newEbayCategory);

                isSupportVariation = variationsEnabled.equals("1") ? true : false;
            }
        }

        return isSupportVariation;
    }

    /**
     * 校验附加兼容性
     * @param site
     * @param categoryId
     * @param accountNumber
     * @return
     */
    public static String getItemCompatibilityEnabledCode(String site, String categoryId, String accountNumber) {
        String itemCompatibilityEnabledCode = null;

        EbayCategoryService ebayCategoryService = SpringUtils.getBean(EbayCategoryService.class);

        //查询分类是否支持多属性,先本地查询,在同步
        EbayCategory ebayCategory = ebayCategoryService.selectBySiteAndCategoryId(site, categoryId);
        if(ebayCategory !=null && ebayCategory.getVariationsEnabled() != null) {
            itemCompatibilityEnabledCode = ebayCategory.getItemCompatibilityEnabledCode();
        }else {
            // 同步平台分类特征
            SaleAccountAndBusinessResponse saleAccount = AccountUtils.getSaleAccountByAccountNumber(SaleChannel.CHANNEL_EBAY, accountNumber);
            EbayGetCategoryFeaturesCall featuresCall = new EbayGetCategoryFeaturesCall(saleAccount);
            ResponseJson categoryFeaturesRsp = featuresCall.getCategoryFeatures(categoryId, site);

            //同步成功，保存在本地
            if(StringUtils.equalsIgnoreCase(StatusCode.SUCCESS, categoryFeaturesRsp.getStatus()) && categoryFeaturesRsp.getBody() != null) {
                String variationsEnabled =  categoryFeaturesRsp.getBody().get("variationsEnabled").toString();
                String ISBNEnabled =  categoryFeaturesRsp.getBody().get("ISBNEnabled").toString();
                itemCompatibilityEnabledCode =  categoryFeaturesRsp.getBody().get("itemCompatibilityEnabledCode").toString();

                // 修改本地
                EbayCategory newEbayCategory = new EbayCategory();
                newEbayCategory.setId(ebayCategory.getId());
                newEbayCategory.setVariationsEnabled(variationsEnabled.equals("1"));
                newEbayCategory.setISBNEnabled(ISBNEnabled.equals("1"));
                newEbayCategory.setItemCompatibilityEnabledCode(itemCompatibilityEnabledCode);
                ebayCategoryService.updateByPrimaryKeySelective(newEbayCategory);
            }
        }

        return itemCompatibilityEnabledCode;
    }


    public static Boolean checkDisabledCategory(String site,String primaryCategoryId) {
        if (StringUtils.isBlank(site) || StringUtils.isBlank(primaryCategoryId)) {
            return false;
        }

        // 只校验英国站点
        if (!site.equalsIgnoreCase("UK")) {
            return false;
        }

        // 获取分类信息，如果缓存中没有，则从数据库中获取
        String cacheProduct = "";
        SystemParamService systemParamService = SpringUtils.getBean(SystemParamService.class);
        try {
            //缓存获取
            cacheProduct = PublishRedisClusterUtils.get(DISABLED_ID);
            if (StringUtils.isBlank(cacheProduct)) {
                SystemParam systemParam = systemParamService.querySystemParamByCodeKey("ebay_uk_category.disabled_id");
                String paramValue = systemParam.getParamValue();
                if (StringUtils.isNotBlank(paramValue)) {
                    cacheProduct = paramValue;
                    PublishRedisClusterUtils.set(DISABLED_ID, paramValue, 5, TimeUnit.MINUTES);
                }
                cacheProduct = paramValue;
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }

        // 校验分类是否包含
        List<String> strings = CommonUtils.splitList(cacheProduct, ",");

        return strings.contains(primaryCategoryId);
    }

}

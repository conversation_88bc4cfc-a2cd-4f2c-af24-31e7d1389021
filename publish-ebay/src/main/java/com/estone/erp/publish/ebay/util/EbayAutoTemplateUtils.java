package com.estone.erp.publish.ebay.util;

import com.estone.erp.publish.ebay.enums.TemplateStatusEnum;
import com.estone.erp.publish.ebay.enums.TemplateTypeEnum;
import com.estone.erp.publish.ebay.model.EbayAutoTemplate;
import com.estone.erp.publish.ebay.model.EbayTemplate;
import com.estone.erp.publish.platform.enums.ApplyStatusEnum;
import org.apache.commons.lang3.StringUtils;

/**
 * @Auther yucm
 * @Date 2020/12/30
 */
public class EbayAutoTemplateUtils {

    public static EbayAutoTemplate toEbayAutoTemplate(EbayTemplate ebayTemplate) {

        EbayAutoTemplate ebayAutoTemplate = new EbayAutoTemplate();

        ebayAutoTemplate.setAccountNumber(ebayTemplate.getAccountNumber());
        ebayAutoTemplate.setCountry(ebayTemplate.getCountry());
        ebayAutoTemplate.setSite(ebayTemplate.getSite());
        ebayAutoTemplate.setTitle(ebayTemplate.getTitle());
        ebayAutoTemplate.setSubTitle(ebayTemplate.getSubTitle());
        ebayAutoTemplate.setDescription(ebayTemplate.getDescription());
        ebayAutoTemplate.setArticleNumber(ebayTemplate.getArticleNumber());
        ebayAutoTemplate.setConditionId(ebayTemplate.getConditionId());
        ebayAutoTemplate.setLocation(ebayTemplate.getLocation());
        ebayAutoTemplate.setPrimaryCategoryId(ebayTemplate.getPrimaryCategoryId());
        ebayAutoTemplate.setPrimaryCategoryName(ebayTemplate.getPrimaryCategoryName());
        ebayAutoTemplate.setStoreCategoryId(ebayTemplate.getStoreCategoryId());
        ebayAutoTemplate.setListingType(ebayTemplate.getListingType());
        ebayAutoTemplate.setListingDuration(ebayTemplate.getListingDuration());
        ebayAutoTemplate.setCurrency(ebayTemplate.getCurrency());
        ebayAutoTemplate.setBuyItNowPrice(ebayTemplate.getBuyItNowPrice());
        ebayAutoTemplate.setStartPrice(ebayTemplate.getStartPrice());
        ebayAutoTemplate.setQuantity(ebayTemplate.getQuantity());
        ebayAutoTemplate.setPaymentMethod(ebayTemplate.getPaymentMethod());
        ebayAutoTemplate.setPaypalEmailAddress(ebayTemplate.getPaypalEmailAddress());
        ebayAutoTemplate.setPrimaryImageUrl(ebayTemplate.getPrimaryImageUrl());
        ebayAutoTemplate.setFirstImageUrl(ebayTemplate.getFirstImageUrl());
        ebayAutoTemplate.setSecondImageUrl(ebayTemplate.getSecondImageUrl());
        ebayAutoTemplate.setThirdImageUrl(ebayTemplate.getThirdImageUrl());
        ebayAutoTemplate.setFourthImageUrl(ebayTemplate.getFourthImageUrl());
        ebayAutoTemplate.setFifthImageUrl(ebayTemplate.getFifthImageUrl());
        ebayAutoTemplate.setSixthImageUrl(ebayTemplate.getSixthImageUrl());
        ebayAutoTemplate.setSeventhImageUrl(ebayTemplate.getSeventhImageUrl());
        ebayAutoTemplate.setEighthImageUrl(ebayTemplate.getEighthImageUrl());
        ebayAutoTemplate.setNinthImageUrl(ebayTemplate.getNinthImageUrl());
        ebayAutoTemplate.setTenthImageUrl(ebayTemplate.getTenthImageUrl());
        ebayAutoTemplate.setEleventhImageUrl(ebayTemplate.getEleventhImageUrl());
        ebayAutoTemplate.setTwelfthImageUrl(ebayTemplate.getTwelfthImageUrl());
        ebayAutoTemplate.setVariationProperties(ebayTemplate.getVariationProperties());
        ebayAutoTemplate.setCustomProperties(ebayTemplate.getCustomProperties());
        ebayAutoTemplate.setExcludeShipToLocations(ebayTemplate.getExcludeShipToLocations());
        ebayAutoTemplate.setShippingLocations(ebayTemplate.getShippingLocations());
        ebayAutoTemplate.setRefundOption(ebayTemplate.getRefundOption());
        ebayAutoTemplate.setReturnsAcceptedOption(ebayTemplate.getReturnsAcceptedOption());
        ebayAutoTemplate.setReturnsWithin(ebayTemplate.getReturnsWithin());
        ebayAutoTemplate.setReturnsWithinOption(ebayTemplate.getReturnsWithinOption());
        ebayAutoTemplate.setReturnsDescription(ebayTemplate.getReturnsDescription());
        ebayAutoTemplate.setShippingCostPaidBy(ebayTemplate.getShippingCostPaidBy());
        ebayAutoTemplate.setIsSchedulePublish(ebayTemplate.getIsSchedulePublish());
        ebayAutoTemplate.setIsScheduled(ebayTemplate.getIsScheduled());
        ebayAutoTemplate.setDispatchTimeMax(ebayTemplate.getDispatchTimeMax());
        ebayAutoTemplate.setDescriptionTemplateId(ebayTemplate.getDescriptionTemplateId());
        ebayAutoTemplate.setVariationProperties(ebayTemplate.getVariationProperties());
        ebayAutoTemplate.setEbayItemShippingServicesJsString(ebayTemplate.getEbayItemShippingServicesJsString());

        ebayAutoTemplate.setIsFreeFee(ebayTemplate.getIsFreeFee() == null ? false : ebayTemplate.getIsFreeFee());
        ebayAutoTemplate.setIsParent(true);
        ebayAutoTemplate.setTemplateStatus(TemplateStatusEnum.WAIT_PUBLISH.intCode());
        ebayAutoTemplate.setTemplateType(TemplateTypeEnum.AUTO_PUBLISH.intCode());
        ebayAutoTemplate.setIsUsable(ebayTemplate.getIsUsable());
        ebayAutoTemplate.setISBN(ebayTemplate.getISBN());
        if(StringUtils.isNotBlank(ebayTemplate.getUPC())) {
            ebayAutoTemplate.setUPC("Does not apply");
        }
        ebayAutoTemplate.setCustomLabel(ebayTemplate.getCustomLabel());
        ebayAutoTemplate.setShippingService(ebayTemplate.getShippingService());
        ebayAutoTemplate.setIntlShippingService(ebayTemplate.getIntlShippingService());
//        ebayAutoTemplate.setStartDate(ebayTemplate.getStartDate());
//        ebayAutoTemplate.setEndDate(ebayTemplate.getEndDate());
        ebayAutoTemplate.setListingFee(ebayTemplate.getListingFee());
        ebayAutoTemplate.setIsReadd(ebayTemplate.getIsReadd());
        ebayAutoTemplate.setIsTitleChange(ebayTemplate.getIsTitleChange());
        ebayAutoTemplate.setReaddStatus(ebayTemplate.getReaddStatus());
        ebayAutoTemplate.setInternationalReturnsAcceptedOption(ebayTemplate.getInternationalReturnsAcceptedOption());
        ebayAutoTemplate.setInternationalRefundOption(ebayTemplate.getInternationalRefundOption());
        ebayAutoTemplate.setInternationalReturnsWithinOption(ebayTemplate.getInternationalReturnsWithinOption());
        ebayAutoTemplate.setInternationalShippingCostPaidBy(ebayTemplate.getInternationalShippingCostPaidBy());
        ebayAutoTemplate.setGalleryType(ebayTemplate.getGalleryType());

        ebayAutoTemplate.setSkuDataSource(ebayTemplate.getSkuDataSource());
        ebayAutoTemplate.setVatPercent(ebayTemplate.getVatPercent());
        ebayAutoTemplate.setUpdateBeforePublisSuccess(ebayTemplate.getUpdateBeforePublisSuccess());
        ebayAutoTemplate.setApplyState(ApplyStatusEnum.YES.getIntCode());
        return ebayAutoTemplate;
    }
}

package com.estone.erp.publish.ebay.jobHandler;

import com.alibaba.fastjson.JSON;
import com.ebay.soap.eBLBaseComponents.ListingTypeCodeType;
import com.estone.erp.common.jobHandler.AbstractJobHandler;
import com.estone.erp.publish.common.component.StatusCode;
import com.estone.erp.publish.common.json.ResponseJson;
import com.estone.erp.publish.ebay.bean.PublishBean;
import com.estone.erp.publish.ebay.enums.EbayTemplateTableEnum;
import com.estone.erp.publish.ebay.enums.PublishTypeEnum;
import com.estone.erp.publish.ebay.enums.TemplateStatusEnum;
import com.estone.erp.publish.ebay.enums.TemplateTypeEnum;
import com.estone.erp.publish.ebay.model.EbayTemplate;
import com.estone.erp.publish.ebay.model.EbayTemplateExample;
import com.estone.erp.publish.ebay.mq.PublishSend;
import com.estone.erp.publish.ebay.service.EbayTemplateService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * 重上需要的拍卖模板
 * @Auther yucm
 * @Date 2022/2/23
 */
@Slf4j
@Component
public class EbayRedoAuctionTemplateHandler extends AbstractJobHandler {

    @Resource
    private EbayTemplateService ebayTemplateService;
    @Resource
    private PublishSend publishSend;

    public EbayRedoAuctionTemplateHandler() {
        super(EbayRedoAuctionTemplateHandler.class.getName());
    }

    @Getter
    @Setter
    static class InnerParam{
        private List<String> accounts;
        private List<Long> ids;
    }


    @Override
    @XxlJob("EbayRedoAuctionTemplateHandler")
    public ReturnT<String> run(String param) throws Exception {
        XxlJobLogger.log("-------autoEnd start--------");

        InnerParam innerParam = null;
        if(StringUtils.isNotBlank(param)){
            try {
                innerParam = JSON.parseObject(param, InnerParam.class);
            }catch (Exception e){
                XxlJobLogger.log("-------解析param出错：--------", e);
                return ReturnT.FAIL;
            }
        }
        if(innerParam == null){
            innerParam = new InnerParam();
        }
        List<String> accounts = innerParam.getAccounts();
        List<Long> ids = innerParam.getIds();

        /**
         * 处理拍卖模板重上
         */
        handendDateTemplate(accounts, ids);

        return ReturnT.SUCCESS;
    }


    private void handendDateTemplate(List<String> accounts, List<Long> ids) {
        Date currentDate = new Date();
        int offset = 0;
        int limit = 100;
        int page = 1;

        EbayTemplateExample example = new EbayTemplateExample();
        EbayTemplateExample.Criteria criteria = example.createCriteria()
                .andIsParentEqualTo(false) // 模板
                .andListingTypeEqualTo(ListingTypeCodeType.CHINESE.value()) // 拍卖类型
                .andEndDateLessThan(currentDate)// 结束时间小于当前时间
                .andIsEndEqualTo(false) // 未结束
                .andTemplateStatusEqualTo(TemplateStatusEnum.PUBLISH_SUCCESS.getCode()); // 刊登成功
        example.setTable(EbayTemplateTableEnum.EBAY_TEMPLATE.getCode());
        example.setLimit(limit);
        example.setOffset(offset);
        example.setOrderByClause("id asc");

        if(CollectionUtils.isNotEmpty(accounts)) {
            criteria.andAccountNumberIn(accounts);
        }
        if(CollectionUtils.isNotEmpty(ids)) {
            criteria.andIdIn(ids);
        }

        // 一直处理第一页数据 修改后后面数据会成为第一页
        while (true) {
            List<EbayTemplate> ebayTemplates = ebayTemplateService.selectByExample(example);

            XxlJobLogger.log("-------第{}页--------", page);
            if(CollectionUtils.isEmpty(ebayTemplates)) {
                XxlJobLogger.log("-------第{}页数据为空更新完毕--------", page);
                break;
            }

            // 标记当前模板结束 需要重上的
            for (EbayTemplate ebayTemplate : ebayTemplates) {
                Date redoEndDate = ebayTemplate.getRedoEndDate();
                Integer redoAmountSurplus = ebayTemplate.getRedoAmountSurplus();
                Long id = ebayTemplate.getId();
                ResponseJson response = new ResponseJson(StatusCode.SUCCESS);
                try{
                    // 重上次数存在且大于0 重上结束时间为空或者大于当前时间 新建一个重上模板
                    if((redoAmountSurplus != null && redoAmountSurplus > 0) && (redoEndDate == null || (new Date()).before(redoEndDate))) {
                        ebayTemplate.setTemplateType(TemplateTypeEnum.AUCTION_REDO.getCode());
                        ebayTemplate.setTemplateStatus(TemplateStatusEnum.WAIT_PUBLISH.intCode());
                        ebayTemplateService.insert(ebayTemplate);

                        PublishBean publishBean = new PublishBean();
                        publishBean.setTempId(ebayTemplate.getId());
                        publishBean.setPublishType(PublishTypeEnum.temp.getCode());

                        // 发送队列刊登
                        response = publishSend.tempPublishSend(ebayTemplate, publishBean);
                    }

                    // 当前模板标记结束 发送队列失败的时候不修改 下次重试
                    if(StatusCode.SUCCESS.equals(response.getStatus())) {
                        ebayTemplateService.updateIsEndByPrimaryKey(true, Arrays.asList(id));
                    }
                }catch (Exception e) {
                    XxlJobLogger.log("-------模板{}发生异常 {}--------", ebayTemplate.getId(), e.getMessage());
                }
            }
        }
    }
}

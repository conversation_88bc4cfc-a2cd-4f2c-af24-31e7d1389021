package com.estone.erp.publish.ebay.controller;

import com.alibaba.fastjson.TypeReference;
import com.estone.erp.common.model.api.ApiRequestParam;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.model.api.CQuery;
import com.estone.erp.common.model.api.CQueryResult;
import com.estone.erp.common.util.Asserts;
import com.estone.erp.publish.common.ErrorCode;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.common.component.StatusCode;
import com.estone.erp.publish.common.json.ResponseJson;
import com.estone.erp.publish.ebay.api.EbayTaxonomyApi;
import com.estone.erp.publish.ebay.bean.EbayCategoryVO;
import com.estone.erp.publish.ebay.bean.taxonomy.Aspect;
import com.estone.erp.publish.ebay.call.EbayGetCategoryFeaturesCall;
import com.estone.erp.publish.ebay.call.GetEbayCategorysCall;
import com.estone.erp.publish.ebay.model.EbayCategory;
import com.estone.erp.publish.ebay.model.EbayCategoryCriteria;
import com.estone.erp.publish.ebay.service.EbayCategoryService;
import com.estone.erp.publish.ebay.service.EbayCategorySpecificsService;
import com.estone.erp.publish.ebay.util.EbayAccountUtils;
import com.estone.erp.publish.system.account.AccountUtils;
import com.estone.erp.publish.system.account.SaleAccountAndBusinessResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> ebaycategory
 * 2019-09-06 09:53:23
 */
@RestController
@Slf4j
public class EbayCategoryController {
    @Resource
    private EbayCategoryService ebayCategoryService;

    @Resource
    private EbayCategorySpecificsService ebayCategorySpecificsService;

    @Resource
    private EbayTaxonomyApi ebayTaxonomyApi;

    @PostMapping("/ebayCategory")
    public ApiResult<?> postEbayCategory(@RequestBody(required = true) ApiRequestParam<String> requestParam) {
        String method = requestParam.getMethod();
        if (StringUtils.isNotBlank(method)) {
            switch (method) {
                case "searchEbayCategory": // 查询列表
                    CQuery<EbayCategoryCriteria> cquery = requestParam.getArgsValue(new TypeReference<CQuery<EbayCategoryCriteria>>() {});
                    Asserts.isTrue(cquery != null, ErrorCode.PARAM_EMPTY_ERROR);
                    CQueryResult<EbayCategory> results = ebayCategoryService.search(cquery);
                    return results;
                case "addEbayCategory": // 添加
                    EbayCategory ebayCategory = requestParam.getArgsValue(new TypeReference<EbayCategory>() {});
                    ebayCategoryService.insert(ebayCategory);
                    return ApiResult.newSuccess(ebayCategory);
                }
        }
        return ApiResult.newSuccess();
    }

    @GetMapping(value = "/ebayCategory/getEbayCategories")
    public ApiResult<?> getEbayCategories(@RequestParam("parentCategoryId") String parentCategoryId,
            @RequestParam("site") String site, @RequestParam("categoryLevel") Integer categorylevel,
            @RequestParam("accountNumber") String accountNumber) {
        if (StringUtils.isBlank(parentCategoryId) || StringUtils.isBlank(site) || StringUtils.isBlank(accountNumber)) {
            return ApiResult.newError("请求参数错误：父类目id或Ebay站点不能为空");
        }
        List<EbayCategory> categories = new ArrayList<EbayCategory>();
        EbayCategoryCriteria query = new EbayCategoryCriteria();
        query.setSite(site);
        if (parentCategoryId.equals("0")) {
            query.setCategorylevel(new Integer(1));
            categories = ebayCategoryService.selectByExample(query.getExample());
            if (CollectionUtils.isEmpty(categories)) {
                // 在线获取Ebay产品分类
                SaleAccountAndBusinessResponse ebayAccount = AccountUtils.getSaleAccountByAccountNumber(SaleChannel.CHANNEL_EBAY, accountNumber);
                ebayAccount.setAccountSite(site);
                GetEbayCategorysCall call = new GetEbayCategorysCall(ebayAccount);
                categories = call.getCategoriesFromEbay(ebayAccount, "", 0, site);
            }
        } else {
            query.setParentcategoryid(parentCategoryId);
            query.setCategorylevel(categorylevel + 1);
            categories = ebayCategoryService.selectByExample(query.getExample());

            // 在线获取Ebay子产品分类
            if (CollectionUtils.isEmpty(categories)) {
                SaleAccountAndBusinessResponse ebayAccount = AccountUtils.getSaleAccountByAccountNumber(SaleChannel.CHANNEL_EBAY, accountNumber);
                ebayAccount.setAccountSite(site);
                GetEbayCategorysCall call = new GetEbayCategorysCall(ebayAccount);
                List<EbayCategory> ebayCategoriesList = call.getCategoriesFromEbay(ebayAccount, parentCategoryId,
                        categorylevel, site);
                categories.addAll(ebayCategoriesList);
            }
        }

        // 去重
        List<EbayCategory> ebayCategorys = new ArrayList<EbayCategory>(categories.size());
        List<String> ebayCategorysIds = new ArrayList<String>();
        for (int i = 0; i < categories.size(); i++) {
            EbayCategory ebayCategory = (EbayCategory) categories.get(i);
            // 去除父一个分类
            if (!ebayCategorysIds.contains(ebayCategory.getCategoryid())
                    && ebayCategory.getCategorylevel() == (categorylevel + 1)) {
                ebayCategorys.add(ebayCategory);
                ebayCategorysIds.add(ebayCategory.getCategoryid());
            }
        }
        return ApiResult.newSuccess(ebayCategorys);
    }

    @GetMapping(value = "/ebayCategory/getAllChildEbayCategories")
    public ApiResult<?> getAllChildEbayCategories(@RequestParam("site") String site,
            @RequestParam("parentCategoryId") String parentCategoryId) {
        if (StringUtils.isBlank(site)) {
            return ApiResult.newError("请求参数错误：Ebay站点不能为空");
        }
        List<EbayCategory> categories = new ArrayList<EbayCategory>();
        EbayCategoryCriteria query = new EbayCategoryCriteria();
        query.setSite(site);
        if (StringUtils.isNotEmpty(parentCategoryId)) {
            query.setParentcategoryid(parentCategoryId);
        }
        List<String> ebayCategorysIds = new ArrayList<String>();
        List<EbayCategory> ebayCategorysList = ebayCategoryService.selectByExample(query.getExample());
        for (int i = 0; i < ebayCategorysList.size(); i++) {
            EbayCategory ebayCategory = (EbayCategory) ebayCategorysList.get(i);
            if (!ebayCategorysIds.contains(ebayCategory.getCategoryid())) {
                categories.add(ebayCategory);
                ebayCategorysIds.add(ebayCategory.getCategoryid());
            }
        }
        log.warn(" ebayCategorys 去重前:[" + ebayCategorysList.size() + "] ebayCategorysId 去重后:[" + categories.size()
                + "]");
        return ApiResult.newSuccess(categories);
    }

    @PostMapping(value = "/ebayCategory/getSuggestedCategoriesByKeywords")
    public ApiResult<?> getSuggestedCategoriesByKeywords(@RequestParam("keywords") String keywords,
            @RequestParam("accountNumber") String accountNumber, @RequestParam("site") String site) {
        if (StringUtils.isBlank(keywords) || StringUtils.isBlank(accountNumber) || StringUtils.isBlank(site)) {
            return ApiResult.newError("请求参数错误：参数不能为空");
        }

        try {
            List<EbayCategory> ebayCategorys = ebayCategoryService.getSuggestedCategoriesByKeywords(keywords, accountNumber, site);
            return ApiResult.newSuccess(ebayCategorys);
        }catch (Exception e) {
            return ApiResult.newError(e.getMessage());
        }
    }

    @GetMapping(value = "/ebayCategory/getRecommendationPairs")
    public ApiResult<?> getRecommendationPairs(@RequestParam("site") String site,
            @RequestParam("categoryId") String categoryId, @RequestParam("accountNumber") String accountNumber) {
        if (StringUtils.isBlank(site)) {
            return ApiResult.newError("请求参数错误：Ebay站点不能为空");
        }
        Map<String, Object> returnMap = new HashMap<>();
        SaleAccountAndBusinessResponse ebayAccount = AccountUtils.getSaleAccountByAccountNumber(SaleChannel.CHANNEL_EBAY, accountNumber);
        if(ebayAccount == null) {
            return ApiResult.newError("未找到当前账号！");
        }

        List<Aspect> list = null;
        try{
            list = ebayCategorySpecificsService.getAspect(categoryId, site, ebayAccount);
        } catch (Exception e) {
            return ApiResult.newError(e.getMessage());
        }

        //查询分类是否支持多属性,先本地查询,在同步
        EbayCategory ebayCategory = ebayCategoryService.selectBySiteAndCategoryId(site, categoryId);

        //分类是否支持多属性 ISBN是否必填
        String is_support_variation_key = "issupportvariation";
        String ISBN_enable = "ISBNEnable";
        String item_compatibility_enabled_code = "itemCompatibilityEnabledCode";

        if(ebayCategory == null) {
            return ApiResult.newError("当前站点不存在当前类目");
        }
        if(ebayCategory.getVariationsEnabled() != null && ebayCategory.getISBNEnabled() != null && null != ebayCategory.getItemCompatibilityEnabledCode()){
            if(ebayCategory.getVariationsEnabled()){
                returnMap.put(is_support_variation_key , "1");
            }else{
                returnMap.put(is_support_variation_key , "0");
            }

            if(ebayCategory.getISBNEnabled()) {
                returnMap.put(ISBN_enable , "1");
            }else{
                returnMap.put(ISBN_enable , "0");
            }

            returnMap.put(item_compatibility_enabled_code, ebayCategory.getItemCompatibilityEnabledCode());
        }else{
            // 同步平台分类特征
            EbayGetCategoryFeaturesCall featuresCall = new EbayGetCategoryFeaturesCall(ebayAccount);
            ResponseJson categoryFeaturesRsp = featuresCall.getCategoryFeatures(categoryId, site);

            String variationsEnabled = "";
            String ISBNEnabled = "";
            String itemCompatibilityEnabledCode = "";

            //同步成功，保存在本地
            if(StringUtils.equalsIgnoreCase(StatusCode.SUCCESS, categoryFeaturesRsp.getStatus()) && categoryFeaturesRsp.getBody() != null) {
                variationsEnabled =  categoryFeaturesRsp.getBody().get("variationsEnabled").toString();
                ISBNEnabled =  categoryFeaturesRsp.getBody().get("ISBNEnabled").toString();
                itemCompatibilityEnabledCode =  categoryFeaturesRsp.getBody().get(item_compatibility_enabled_code).toString();

                // 修改本地
                EbayCategory newEbayCategory = new EbayCategory();
                newEbayCategory.setId(ebayCategory.getId());
                newEbayCategory.setVariationsEnabled(variationsEnabled.equals("1"));
                newEbayCategory.setISBNEnabled(ISBNEnabled.equals("1"));
                newEbayCategory.setItemCompatibilityEnabledCode(itemCompatibilityEnabledCode);
                ebayCategoryService.updateByPrimaryKeySelective(newEbayCategory);
            }

            returnMap.put(is_support_variation_key , variationsEnabled);
            returnMap.put(ISBN_enable , ISBNEnabled);
            returnMap.put(item_compatibility_enabled_code , itemCompatibilityEnabledCode);
        }
        returnMap.put("recommendationPairs", list);
        returnMap.put("category", ebayCategory);
        return ApiResult.newSuccess(returnMap);
    }

    @PutMapping(value = "/ebayCategory/{id}")
    public ApiResult<?> putEbayCategory(@PathVariable(value = "id", required = true) Long id, @RequestBody ApiRequestParam<String> requestParam) {
        String method = requestParam.getMethod();
        if (StringUtils.isNotBlank(method)) {
            switch (method) {
                case "updateEbayCategory": // 单个修改
                    EbayCategory ebayCategory = requestParam.getArgsValue(new TypeReference<EbayCategory>() {});
                    ebayCategoryService.updateByPrimaryKeySelective(ebayCategory);
                    return ApiResult.newSuccess(ebayCategory);
                }
        }
        return ApiResult.newSuccess();
    }

    @GetMapping(value = "/getCategorySubtree/{categoryId}")
    public ApiResult<?> getCategorySubtree(@PathVariable(value = "categoryId", required = true) String categoryId) {

        SaleAccountAndBusinessResponse ebayAccount = AccountUtils.getSaleAccountByAccountNumber(SaleChannel.CHANNEL_EBAY, "wensh_73", true);

        try{
            String body = ebayTaxonomyApi.getCategorySubtree(categoryId, "UK", ebayAccount);
            return ApiResult.newSuccess(body);
        } catch (Exception e) {
            return ApiResult.newError(e.getMessage());
        }
    }

    @PostMapping(value = "/ebayCategory/syncEbayCategoryTree")
    public ApiResult<?> syncEbayCategoryTree(@RequestBody SaleAccountAndBusinessResponse account) {
        if(null == account) {
            return ApiResult.newError("参数为空！");
        }
        String accountNumber = account.getAccountNumber();
        String site = account.getAccountSite();
        SaleAccountAndBusinessResponse ebayAccount = AccountUtils.getSaleAccountByAccountNumber(SaleChannel.CHANNEL_EBAY, accountNumber, false);

        try{
            ebayCategoryService.syncEbayCategoryTree(site, ebayAccount);
            return ApiResult.newSuccess();
        } catch (Exception e) {
            return ApiResult.newError(e.getMessage());
        }
    }

    @GetMapping("/ebayCategory/getSiteAllCategory")
    public ApiResult<List<EbayCategory>> getSiteAllCategory(@RequestParam(name = "site", required = false) String site,
                                                            @RequestParam(name = "accountNumber", required = false) String accountNumber) {
        if (StringUtils.isBlank(site) && StringUtils.isBlank(accountNumber)) {
            return ApiResult.newError("参数为空！");
        }
        if (StringUtils.isBlank(site)) {
            SaleAccountAndBusinessResponse ebayAccount = AccountUtils.getSaleAccountByAccountNumber(SaleChannel.CHANNEL_EBAY, accountNumber, true);
            if (null == ebayAccount) {
                return ApiResult.newError("查询账号信息为空！");
            }
            site = ebayAccount.getAccountSite();
            if (StringUtils.isBlank(site)) {
                return ApiResult.newError("账号站点信息为空！");
            }
        }
        List<EbayCategory> ebayCategoryList = ebayCategoryService.selectCategoryBySite(site);
        return ApiResult.newSuccess(ebayCategoryList);
    }

    /**
     * 根据站点获取类目树
     */
    @GetMapping("/ebayCategory/siteTree")
    public ApiResult<List<EbayCategoryVO>> getSiteTree(@RequestParam(name = "site", required = false) String site,
                                                       @RequestParam(name = "accountNumber", required = false) String accountNumber) {
        if (StringUtils.isBlank(site) && StringUtils.isBlank(accountNumber)) {
            return ApiResult.newError("参数为空！");
        }
        if (StringUtils.isBlank(site)) {
            site = EbayAccountUtils.getAccountSite(accountNumber);
        }
        List<EbayCategoryVO> ebayCategoryList = ebayCategoryService.getCategorySiteTree(site);
        return ApiResult.newSuccess(ebayCategoryList);
    }

    @PostMapping(value = "/ebayCategory/deleteExpireBySite")
    public ApiResult<?> syncEbayCategoryTree(@RequestParam String site, @RequestParam Integer days) {
        try {
            ebayCategoryService.deleteExpireBySite(site, days);
            return ApiResult.newSuccess();
        }catch (Exception e){
            return ApiResult.newError(e.getMessage());
        }
    }
}
package com.estone.erp.publish.ebay.service;

import java.util.List;

import com.estone.erp.common.model.api.CQuery;
import com.estone.erp.common.model.api.CQueryResult;
import com.estone.erp.publish.ebay.model.EbayItemSyncRecord;
import com.estone.erp.publish.ebay.model.EbayItemSyncRecordCriteria;
import com.estone.erp.publish.ebay.model.EbayItemSyncRecordExample;

/**
 * <AUTHOR> ebay_item_sync_record
 * 2019-09-21 14:55:52
 */
public interface EbayItemSyncRecordService {
    int countByExample(EbayItemSyncRecordExample example);

    CQueryResult<EbayItemSyncRecord> search(CQuery<EbayItemSyncRecordCriteria> cquery);

    List<EbayItemSyncRecord> selectByExample(EbayItemSyncRecordExample example);

    EbayItemSyncRecord selectByPrimaryKey(Integer id);

    int insert(EbayItemSyncRecord record);

    int insertSelective(EbayItemSyncRecord record);

    int updateByPrimaryKeySelective(EbayItemSyncRecord record);

    int updateByExampleSelective(EbayItemSyncRecord record, EbayItemSyncRecordExample example);

    int deleteByPrimaryKey(Integer id);

    int deleteByExample(EbayItemSyncRecordExample example);

    void batchInsert(List<EbayItemSyncRecord> createEbaySyncRecordList);
}
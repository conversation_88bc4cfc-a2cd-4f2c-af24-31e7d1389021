package com.estone.erp.publish.ebay.model;

import lombok.Data;

import java.io.Serializable;
import java.sql.Timestamp;

@Data
public class EbayChangeStockSku implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     *  database column ebay_change_stock_sku.id
     */
    private Integer id;

    /**
     * sku database column ebay_change_stock_sku.sku
     */
    private String sku;

    /**
     * 系统库存 可用-待发 database column ebay_change_stock_sku.system_stock
     */
    private Integer systemStock;

    /**
     * 创建时间 database column ebay_change_stock_sku.create_time
     */
    private Timestamp createTime;
}
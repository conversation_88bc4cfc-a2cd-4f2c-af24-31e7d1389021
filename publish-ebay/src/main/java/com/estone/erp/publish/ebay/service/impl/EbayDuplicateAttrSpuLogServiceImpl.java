package com.estone.erp.publish.ebay.service.impl;

import com.estone.erp.common.model.api.CQuery;
import com.estone.erp.common.model.api.CQueryResult;
import com.estone.erp.publish.ebay.mapper.EbayDuplicateAttrSpuLogMapper;
import com.estone.erp.publish.ebay.model.EbayDuplicateAttrSpuLog;
import com.estone.erp.publish.ebay.model.EbayDuplicateAttrSpuLogCriteria;
import com.estone.erp.publish.ebay.model.EbayDuplicateAttrSpuLogExample;
import com.estone.erp.publish.ebay.service.EbayDuplicateAttrSpuLogService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;

/**
 * <AUTHOR> ebay_duplicate_attr_spu_log
 * 2023-11-09 11:52:10
 */
@Service("ebayDuplicateAttrSpuLogService")
@Slf4j
public class EbayDuplicateAttrSpuLogServiceImpl implements EbayDuplicateAttrSpuLogService {
    @Resource
    private EbayDuplicateAttrSpuLogMapper ebayDuplicateAttrSpuLogMapper;

    @Override
    public int countByExample(EbayDuplicateAttrSpuLogExample example) {
        Assert.notNull(example, "example is null!");
        return ebayDuplicateAttrSpuLogMapper.countByExample(example);
    }

    @Override
    public CQueryResult<EbayDuplicateAttrSpuLog> search(CQuery<EbayDuplicateAttrSpuLogCriteria> cquery) {
        Assert.notNull(cquery, "cquery is null!");
        EbayDuplicateAttrSpuLogCriteria query = cquery.getSearch();
        EbayDuplicateAttrSpuLogExample example = query.getExample();
        long total = 0;
        int totalPages = 0;
        // 是否分页
        if (cquery.isPageReqired()) {
            total = ebayDuplicateAttrSpuLogMapper.countByExample(example);
            totalPages = (int) Math.ceil((double) total / cquery.getLimit());
            example.setLimit(cquery.getLimit());
            example.setOffset(cquery.getOffset());
        }
        List<EbayDuplicateAttrSpuLog> ebayDuplicateAttrSpuLogs = ebayDuplicateAttrSpuLogMapper.selectByExample(example);
        // 组装结果
        CQueryResult<EbayDuplicateAttrSpuLog> result = new CQueryResult<>();
        result.setTotal(total);
        result.setTotalPages(totalPages);
        result.setRows(ebayDuplicateAttrSpuLogs);
        return result;
    }

    @Override
    public EbayDuplicateAttrSpuLog selectByPrimaryKey(Integer id) {
        Assert.notNull(id, "id is null!");
        return ebayDuplicateAttrSpuLogMapper.selectByPrimaryKey(id);
    }

    @Override
    public List<EbayDuplicateAttrSpuLog> selectByExample(EbayDuplicateAttrSpuLogExample example) {
        Assert.notNull(example, "example is null!");
        return ebayDuplicateAttrSpuLogMapper.selectByExample(example);
    }

    @Override
    public int insert(EbayDuplicateAttrSpuLog record) {
        Assert.notNull(record, "record is null!");
        // 默认加时间和人
        return ebayDuplicateAttrSpuLogMapper.insert(record);
    }

    @Override
    public int updateByPrimaryKeySelective(EbayDuplicateAttrSpuLog record) {
        Assert.notNull(record, "record is null!");
        // 默认加修改时间和修改人
        return ebayDuplicateAttrSpuLogMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public int updateByExampleSelective(EbayDuplicateAttrSpuLog record, EbayDuplicateAttrSpuLogExample example) {
        Assert.notNull(record, "record is null!");
        // 默认加修改时间和修改人
        return ebayDuplicateAttrSpuLogMapper.updateByExampleSelective(record, example);
    }

    @Override
    public int deleteByPrimaryKey(List<Integer> ids) {
        Assert.notNull(ids, "ids is null!");
        return ebayDuplicateAttrSpuLogMapper.deleteByPrimaryKey(ids);
    }

    @Override
    public List<Integer> collectTimeRangeIds(LocalDateTime starTime, LocalDateTime endTime,List<String> itemStatusList) {
        List<EbayDuplicateAttrSpuLog> logList = ebayDuplicateAttrSpuLogMapper.collectTimeRangeIds(starTime, endTime,itemStatusList);
        if (CollectionUtils.isEmpty(logList)) {
            return Collections.emptyList();
        }

        Set<String> spuSkuSets = new HashSet<>();
        List<Integer> idList = new ArrayList<>();
        for (EbayDuplicateAttrSpuLog log : logList) {
            String key = log.getSpu() + log.getSku();
            if (!spuSkuSets.contains(key)) {
                spuSkuSets.add(key);
                idList.add(log.getId());
            }
        }
        return idList;
    }

}
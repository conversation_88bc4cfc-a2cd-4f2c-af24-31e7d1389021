package com.estone.erp.publish.ebay.mapper;

import com.estone.erp.publish.ebay.model.EbayCategoryCompatibility;
import com.estone.erp.publish.ebay.model.EbayCategoryCompatibilityExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface EbayCategoryCompatibilityMapper {
    int countByExample(EbayCategoryCompatibilityExample example);

    int deleteByPrimaryKey(List<Long> ids);

    int insert(EbayCategoryCompatibility record);

    EbayCategoryCompatibility selectByPrimaryKey(Long id);

    List<EbayCategoryCompatibility> selectByExample(EbayCategoryCompatibilityExample example);

    int updateByExampleSelective(@Param("record") EbayCategoryCompatibility record, @Param("example") EbayCategoryCompatibilityExample example);

    int updateByPrimaryKeySelective(EbayCategoryCompatibility record);

    EbayCategoryCompatibility selectBySiteAndCategoryId(@Param("site") String site, @Param("categoryId") String categoryId);
}
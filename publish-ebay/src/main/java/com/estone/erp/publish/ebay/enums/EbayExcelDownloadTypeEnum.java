package com.estone.erp.publish.ebay.enums;

/**
 * excel下载类型
 * <AUTHOR>
 * @date 2022/12/6 17:33
 */
public enum  EbayExcelDownloadTypeEnum {

    ITEM_DOWNLOAD("item_download", "在线列表导出"),

    LONG_TAIL_DOWNLOAD("long_tail_download", "长尾刊登导出"),

    DOWNLOAD_ITEMS("download_items", "导出item"),

    RECOMMEND_PUBLISH_DOWNLOAD("recommend_publish_download", "导出推荐刊登列表")
    ;

    /**
     * 英文
     */
    private String code;

    /**
     * 中文
     */
    private String msg;

    private EbayExcelDownloadTypeEnum(String code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public String getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }
}

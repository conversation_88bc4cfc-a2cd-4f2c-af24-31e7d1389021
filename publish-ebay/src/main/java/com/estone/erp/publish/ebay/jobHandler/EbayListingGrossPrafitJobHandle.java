package com.estone.erp.publish.ebay.jobHandler;

import com.alibaba.fastjson.JSON;
import com.estone.erp.common.constant.StrConstant;
import com.estone.erp.common.jobHandler.AbstractJobHandler;
import com.estone.erp.common.mq.PublishQueues;
import com.estone.erp.common.mq.RabbitMqSender;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.common.util.DateUtils;
import com.estone.erp.publish.ebay.enums.EbayCalcGrossPrafitTypeEnum;
import com.estone.erp.publish.ebay.enums.OperateLogEnum;
import com.estone.erp.publish.ebay.model.EbayCalcPriceRule;
import com.estone.erp.publish.ebay.model.EbayCalcPriceRuleExample;
import com.estone.erp.publish.ebay.model.EbayOperateLog;
import com.estone.erp.publish.ebay.model.EbayOperateLogExample;
import com.estone.erp.publish.ebay.mq.modal.EbayListingGrossPrafitBean;
import com.estone.erp.publish.ebay.service.EbayCalcPriceRuleService;
import com.estone.erp.publish.ebay.service.EbayOperateLogService;
import com.estone.erp.publish.elasticsearch.model.SaleAccount;
import com.estone.erp.publish.elasticsearch.model.beanrequest.EsSaleAccountRequest;
import com.estone.erp.publish.elasticsearch.service.SaleAccountService;
import com.estone.erp.publish.mq.PublishMqConfig;
import com.estone.erp.publish.system.account.ChannelEnum.SaleAccountStastusEnum;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Description 计算ebay listing 的毛利毛利率，通过队列分发到所有服务去计算
 **/
@Slf4j
@Component
public class EbayListingGrossPrafitJobHandle extends AbstractJobHandler {

    public EbayListingGrossPrafitJobHandle() {
        super("EbayListingGrossPrafitJobHandle");
    }

    @Resource
    private RabbitMqSender rabbitMqSender;
    @Resource
    private EbayOperateLogService ebayOperateLogService;
    @Resource
    private EbayCalcPriceRuleService ebayCalcPriceRuleService;
    @Resource
    private SaleAccountService saleAccountService;

    @Getter
    @Setter
    static class InnerParam{
        private List<String> accounts;
        private String type;
        /**
         * 配置变更时间范围
         */
        private Timestamp fromDate;
        private Timestamp toDate;
        private List<String> ids;
    }
    
    @Override
    @XxlJob("EbayListingGrossPrafitJobHandle")
    public ReturnT<String> run(String param) throws Exception {
        XxlJobLogger.log("***************** 计算ebay在线listing毛利毛利率发送队列开始 *****************");
        InnerParam innerParam = null;
        if(StringUtils.isNotBlank(param)){
            try {
                innerParam = JSON.parseObject(param, InnerParam.class);
            }catch (Exception e){
                XxlJobLogger.log("-------解析param出错：--------", e);
                return ReturnT.FAIL;
            }
        }
        if(innerParam == null){
            innerParam = new InnerParam();
        }
        List<String> accounts = innerParam.getAccounts();
        String type = innerParam.getType();
        Timestamp fromDate = innerParam.getFromDate();
        Timestamp toDate = innerParam.getToDate();
        List<String> ids = innerParam.getIds();

        // es查询 需要的账号站点
        List<String> statusList = new ArrayList<>();
        statusList.add(SaleAccountStastusEnum.NORMAL.getCode());
        statusList.add(SaleAccountStastusEnum.EXCEPTION.getCode());
        statusList.add(SaleAccountStastusEnum.TO_ENABLE.getCode());
        String[] withFields = {"accountNumber","accountSite"};
        EsSaleAccountRequest request = new EsSaleAccountRequest();
        request.setSaleChannel(SaleChannel.CHANNEL_EBAY);
        request.setAccountNumberList(accounts);
        request.setAccountStatusList(statusList);
        List<SaleAccount> saleAccounts = saleAccountService.getSaleAccountsEs(request, withFields);

        // 算价规则发生变更的站点
        List<String> changeCalcPriceRuleSites = new ArrayList<String>();
        if(StringUtils.isBlank(type)) {
            changeCalcPriceRuleSites = getChangeCalcPriceRuleSites(fromDate, toDate);
        }
        XxlJobLogger.log("计算ebay在线listing毛利毛利率的店铺数量" + saleAccounts.size());
        for (SaleAccount saleAccount : saleAccounts) {
            String accountNumber = saleAccount.getAccountNumber();
            if(StringUtils.isBlank(accountNumber) || StringUtils.isBlank(saleAccount.getAccountSite())) {
                continue;
            }

            try {
                EbayListingGrossPrafitBean bean = new EbayListingGrossPrafitBean();
                bean.setAccountNumber(saleAccount.getAccountNumber());
                bean.setSite(saleAccount.getAccountSite());
                if(StringUtils.isNotBlank(type)) {
                    bean.setType(type);
                }else if(changeCalcPriceRuleSites.contains(saleAccount.getAccountSite())) {
                    bean.setType(EbayCalcGrossPrafitTypeEnum.CALC_PRICE_RULE_DIFFERENT.getCode());
                } else {
                    bean.setType(EbayCalcGrossPrafitTypeEnum.CALC_PRAFIT_IS_NULL.getCode());
                }
                bean.setIds(ids);
                bean.setUserName(StrConstant.ADMIN);

                rabbitMqSender.send(PublishMqConfig.EBAY_API_DIRECT_EXCHANGE, PublishQueues.PUBLISH_EBAY_LISTING_GROSS_PRAFIT_KEY, bean);
            }catch (Exception e) {
                XxlJobLogger.log(accountNumber + " error " + e.getMessage());
            }
        }

        XxlJobLogger.log("***************** 计算ebay在线listing毛利毛利率发送队列结束 *****************");
        return ReturnT.SUCCESS;
    }

    private List<String> getChangeCalcPriceRuleSites(Timestamp fromDate, Timestamp toDate) {
        List<String> sites = new ArrayList<>();
        if(null == fromDate) {
            Date date = DateUtils.getDateBegin(-2);
            fromDate = new Timestamp(date.getTime());
        }
        if(null == toDate) {
            Date date = DateUtils.getDateBegin(0);
            toDate = new Timestamp(date.getTime());
        }

        // 查询操作日志
        EbayOperateLogExample operateLogExample = new EbayOperateLogExample();
        operateLogExample.createCriteria()
                .andTypeEqualTo(OperateLogEnum.UPDATE_CALC_PRICE_RULE.getCode())
                .andCreateDateGreaterThanOrEqualTo(fromDate)
                .andCreateDateLessThan(toDate);
        List<EbayOperateLog> operateLogs = ebayOperateLogService.selectByExample(operateLogExample);
        if(CollectionUtils.isEmpty(operateLogs)) {
            return sites;
        }

        /**
         * 算价配置 变更影响算毛利率的字段
         */
        List<String> fieldNames = Arrays.asList("site", "label", "fromPrice", "toPrice", "domesticLogistics", "internationalLogistics");
        List<Integer> businessIds = operateLogs.stream().filter(o->{
            String fieldName = o.getFieldName();
            Integer businessId = o.getBusinessId();
            if(StringUtils.isNotBlank(fieldName) && null != businessId &&  fieldNames.contains(fieldName)) {
                return true;
            }else {
                return false;
            }
        }).map(EbayOperateLog::getBusinessId).collect(Collectors.toList());

        if(CollectionUtils.isNotEmpty(businessIds)) {
            EbayCalcPriceRuleExample calcPriceRuleExample = new EbayCalcPriceRuleExample();
            calcPriceRuleExample.createCriteria().andIdIn(businessIds);
            List<EbayCalcPriceRule> ruleList = ebayCalcPriceRuleService.selectByExample(calcPriceRuleExample);
            sites = ruleList.stream().filter(o->StringUtils.isNotBlank(o.getSite())).map(EbayCalcPriceRule::getSite).collect(Collectors.toList());
        }
        return sites;
    }
}
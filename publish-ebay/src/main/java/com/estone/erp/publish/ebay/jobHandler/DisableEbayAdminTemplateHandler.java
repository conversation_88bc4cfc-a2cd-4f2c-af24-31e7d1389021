package com.estone.erp.publish.ebay.jobHandler;

import com.alibaba.fastjson.JSON;
import com.estone.erp.common.constant.StrConstant;
import com.estone.erp.common.jobHandler.AbstractJobHandler;
import com.estone.erp.publish.ebay.bean.EbayIntStringMap;
import com.estone.erp.publish.ebay.enums.MarketplaceEnum;
import com.estone.erp.publish.ebay.model.EbayAutoTemplateExample;
import com.estone.erp.publish.ebay.service.EbayAutoTemplateService;
import com.estone.erp.publish.ebay.service.EbayCategoryService;
import com.estone.erp.publish.platform.enums.ApplyStatusEnum;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * ES-3092 将admin范本分类不存在的范本数据禁用
 *
 * @Auther yucm
 * @Date 2022/1/11
 */
@Slf4j
@Component
public class DisableEbayAdminTemplateHandler extends AbstractJobHandler {

    @Resource
    private EbayAutoTemplateService ebayAutoTemplateService;
    @Resource
    private EbayCategoryService ebayCategoryService;

    public DisableEbayAdminTemplateHandler() {
        super(DisableEbayAdminTemplateHandler.class.getName());
    }

    @Getter
    @Setter
    static class InnerParam{
        private List<Long> ids;
        private String site;
    }

    @Override
    @XxlJob("DisableEbayAdminTemplateHandler")
    public ReturnT<String> run(String param) throws Exception {
        InnerParam innerParam = null;
        if(StringUtils.isNotBlank(param)){
            try {
                innerParam = JSON.parseObject(param, InnerParam.class);
            }catch (Exception e){
                XxlJobLogger.log("-------解析param出错：--------", e);
                return ReturnT.FAIL;
            }
        }
        if(innerParam == null){
            innerParam = new InnerParam();
        }

        List<Long> inputIds = innerParam.getIds();
        String inputSite = innerParam.getSite();
        MarketplaceEnum[] marketplaceEnums = MarketplaceEnum.values();
        for (MarketplaceEnum marketplaceEnum : marketplaceEnums) {
            String site = marketplaceEnum.getSite();
            if(StringUtils.isNotBlank(inputSite) && !inputSite.equalsIgnoreCase(site)) {
                continue;
            }
            XxlJobLogger.log(site);
            List<String> categoryList = ebayCategoryService.getCategoryIdBySite(site);
            if(CollectionUtils.isEmpty(categoryList)) {
                XxlJobLogger.log(site + "分类为空！");
                continue;
            }
            int offset = 0;
            int limit = 5000;
            EbayAutoTemplateExample example = new EbayAutoTemplateExample();
            EbayAutoTemplateExample.Criteria criteria = example.createCriteria();
            criteria.andSiteEqualTo(site);
            if(CollectionUtils.isNotEmpty(inputIds)) {
                criteria.andIdIn(inputIds);
            }
            example.setLimit(limit);
            while (true) {
                try {
                    example.setOffset(offset);
                    List<EbayIntStringMap> ebayIntStringMaps = ebayAutoTemplateService.selectTemplateCategoryByExample(example);
                    if(CollectionUtils.isEmpty(ebayIntStringMaps)) {
                        XxlJobLogger.log(site + "完结！");
                        break;
                    }

                    List<EbayIntStringMap> notExistCategoryList = ebayIntStringMaps.stream().filter(o->!categoryList.contains(o.getValue())).collect(Collectors.toList());
                    List<Long> ids = notExistCategoryList.stream().map(o->o.getKey()).collect(Collectors.toList());
                    if(CollectionUtils.isNotEmpty(notExistCategoryList)) {
                        XxlJobLogger.log("不存在分类" + JSON.toJSONString(notExistCategoryList));

                        ebayAutoTemplateService.batchUpdateApplyState(ids, ApplyStatusEnum.NO.getIntCode(), StrConstant.ADMIN, "分类不存在系统禁用！");
                    }
                }catch (Exception e) {
                    XxlJobLogger.log(site + offset + "发生异常" + e.getMessage());
                } finally {
                    offset += limit;
                }
            }
        }

        return ReturnT.SUCCESS;
    }
}
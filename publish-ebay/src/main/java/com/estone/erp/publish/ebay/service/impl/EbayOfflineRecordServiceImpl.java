package com.estone.erp.publish.ebay.service.impl;

import com.estone.erp.common.model.api.CQuery;
import com.estone.erp.common.model.api.CQueryResult;
import com.estone.erp.publish.ebay.mapper.EbayOfflineRecordMapper;
import com.estone.erp.publish.ebay.model.EbayOfflineRecord;
import com.estone.erp.publish.ebay.model.EbayOfflineRecordCriteria;
import com.estone.erp.publish.ebay.model.EbayOfflineRecordExample;
import com.estone.erp.publish.ebay.service.EbayOfflineRecordService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * 2024-04-26 18:07:03
 */
@Service("ebayOfflineRecordService")
@Slf4j
public class EbayOfflineRecordServiceImpl implements EbayOfflineRecordService {
    @Resource
    private EbayOfflineRecordMapper ebayOfflineRecordMapper;

    @Override
    public int countByExample(EbayOfflineRecordExample example) {
        Assert.notNull(example, "example is null!");
        return ebayOfflineRecordMapper.countByExample(example);
    }

    @Override
    public CQueryResult<EbayOfflineRecord> search(CQuery<EbayOfflineRecordCriteria> cquery) {
        Assert.notNull(cquery, "cquery is null!");
        EbayOfflineRecordCriteria query = cquery.getSearch();
        EbayOfflineRecordExample example = query.getExample();
        long total = 0;
        int totalPages = 0;
        // 是否分页
        if (cquery.isPageReqired()) {
            total = ebayOfflineRecordMapper.countByExample(example);
            totalPages = (int) Math.ceil((double) total / cquery.getLimit());
            example.setLimit(cquery.getLimit());
            example.setOffset(cquery.getOffset());
        }
        List<EbayOfflineRecord> ebayOfflineRecords = ebayOfflineRecordMapper.selectByExample(example);
        // 组装结果
        CQueryResult<EbayOfflineRecord> result = new CQueryResult<>();
        result.setTotal(total);
        result.setTotalPages(totalPages);
        result.setRows(ebayOfflineRecords);
        return result;
    }

    @Override
    public EbayOfflineRecord selectByPrimaryKey(Integer id) {
        Assert.notNull(id, "id is null!");
        return ebayOfflineRecordMapper.selectByPrimaryKey(id);
    }

    @Override
    public List<EbayOfflineRecord> selectByExample(EbayOfflineRecordExample example) {
        Assert.notNull(example, "example is null!");
        return ebayOfflineRecordMapper.selectByExample(example);
    }

    @Override
    public int insert(EbayOfflineRecord record) {
        Assert.notNull(record, "record is null!");
        // 默认加时间和人
        return ebayOfflineRecordMapper.insert(record);
    }

    @Override
    public int updateByPrimaryKeySelective(EbayOfflineRecord record) {
        Assert.notNull(record, "record is null!");
        // 默认加修改时间和修改人
        return ebayOfflineRecordMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public int updateStatusByIds(List<Integer> idList, Integer exeStatus, Boolean actualOffline) {
        if (CollectionUtils.isEmpty(idList)) {
            return 0;
        }
        Timestamp updateTime = new Timestamp(System.currentTimeMillis());
        return ebayOfflineRecordMapper.updateStatusByIds(idList, exeStatus, actualOffline, updateTime);
    }

    @Override
    public int updateByExampleSelective(EbayOfflineRecord record, EbayOfflineRecordExample example) {
        Assert.notNull(record, "record is null!");
        // 默认加修改时间和修改人
        return ebayOfflineRecordMapper.updateByExampleSelective(record, example);
    }

    @Override
    public int deleteByPrimaryKey(List<Integer> ids) {
        Assert.notNull(ids, "ids is null!");
        return ebayOfflineRecordMapper.deleteByPrimaryKey(ids);
    }

    @Override
    public void batchInsert(List<EbayOfflineRecord> offlineRecords) {
        ebayOfflineRecordMapper.batchInsert(offlineRecords);
    }

    @Override
    public int deleteByCreateDate(String accountNumber, Date createDate) {
        return ebayOfflineRecordMapper.deleteByCreateDate(accountNumber, createDate);
    }
}
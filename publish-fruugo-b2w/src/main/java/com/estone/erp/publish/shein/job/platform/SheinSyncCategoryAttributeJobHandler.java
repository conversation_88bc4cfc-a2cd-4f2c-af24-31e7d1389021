package com.estone.erp.publish.shein.job.platform;

import com.estone.erp.common.jobHandler.AbstractJobHandler;
import com.estone.erp.common.util.PagingUtils;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.elasticsearch.util.EsAccountUtils;
import com.estone.erp.publish.shein.model.SheinAccountCategory;
import com.estone.erp.publish.shein.model.SheinCategory;
import com.estone.erp.publish.shein.model.SheinCategoryExample;
import com.estone.erp.publish.shein.platform.SheinApiClient;
import com.estone.erp.publish.shein.platform.model.request.ProductAttributeTemplateRequest;
import com.estone.erp.publish.shein.platform.model.request.WholeCategoriesAttributesRequest;
import com.estone.erp.publish.shein.platform.model.response.AllCategoriesAttributesResponse;
import com.estone.erp.publish.shein.platform.model.response.ApiBaseMultipleResponse;
import com.estone.erp.publish.shein.platform.model.response.QueryAttributeTemplateResponse;
import com.estone.erp.publish.shein.service.SheinAccountCategoryAttributeValueService;
import com.estone.erp.publish.shein.service.SheinAccountCategoryService;
import com.estone.erp.publish.shein.service.SheinCategoryAttributeService;
import com.estone.erp.publish.shein.service.SheinCategoryService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * shein 平台，同步店铺品牌
 */
@Slf4j
@Component
public class SheinSyncCategoryAttributeJobHandler extends AbstractJobHandler {

    @Autowired
    private SheinApiClient sheinApiClient;

    @Autowired
    private SheinCategoryAttributeService sheinCategoryAttributeService;

    @Autowired
    private SheinCategoryService sheinCategoryService;

    @Autowired
    private SheinAccountCategoryAttributeValueService sheinAccountCategoryAttributeValueService;

    @Autowired
    private SheinAccountCategoryService sheinAccountCategoryService;

    public SheinSyncCategoryAttributeJobHandler() {
        super(SheinSyncCategoryAttributeJobHandler.class.getName());
    }
    @XxlJob("sheinSyncCategoryAttributeJobHandler")
    @Override
    public ReturnT<String> run(String param) throws Exception {
        SheinCategoryExample example = new SheinCategoryExample();
        example.createCriteria().andProductTypeIdGreaterThan(0);
        List<SheinCategory> categories = sheinCategoryService.selectByExample(example);
        List<Integer> productIds = categories.stream().map(SheinCategory::getProductTypeId).filter(Objects::nonNull).distinct().collect(Collectors.toList());

        List<List<Integer>> lists = PagingUtils.newPagingList(productIds, 10);
        for (List<Integer> list : lists) {
            WholeCategoriesAttributesRequest request = new WholeCategoriesAttributesRequest();
            request.setLanguage("zh-cn");
            request.setBusinessMode("5");
            request.setProductTypeIdList(list);
            try {
                ApiBaseMultipleResponse<List<AllCategoriesAttributesResponse>> test = sheinApiClient.getPlatformCategoriesAttributes(request);
                if (test.isSuccess()) {
                    ApiBaseMultipleResponse.Info<List<AllCategoriesAttributesResponse>> info = test.getInfo();
                    List<AllCategoriesAttributesResponse> data = info.getData();
                    for (AllCategoriesAttributesResponse datum : data) {
                        sheinCategoryAttributeService.removeDuplicatesAndSave(datum);
                    }
                } else {
                    XxlJobLogger.log("查询全平台分类属性失败:{}", test.getMsg());
                }
            } catch (Exception e) {
                XxlJobLogger.log("查询全平台分类属性失败:{}", e.getMessage());
            }
        }

        List<String> accountListByEs = EsAccountUtils.getPlatformNormaLAccountListByEs(SaleChannel.CHANNEL_SHEIN);
        for (String accountNumber : accountListByEs) {
            try {
                doAccountCategoryAttribute(accountNumber);
            } catch (Exception e) {
                XxlJobLogger.log("店铺账号查询属性失败：{}", accountNumber);
            }
        }
        return ReturnT.SUCCESS;
    }

    private void doAccountCategoryAttribute(String accountNumber) {
        SheinAccountCategory sheinAccountCategory = sheinAccountCategoryService.getSheinAccountCategory(accountNumber);
        if (sheinAccountCategory == null) {
            return;
        }
        String categoryIds = sheinAccountCategory.getLeafNodeIds();
        // 非叶子结点是没有产品类型id
        List<Integer> ids = Arrays.stream(categoryIds.split(",")).map(Integer::valueOf).collect(Collectors.toList());

        SheinCategoryExample categoryExample = new SheinCategoryExample();
        categoryExample.createCriteria().andCategoryIdIn(ids).andProductTypeIdGreaterThan(0);
        List<SheinCategory> categories = sheinCategoryService.selectByExample(categoryExample);

        Set<Integer> productIds = categories.stream().map(SheinCategory::getProductTypeId).collect(Collectors.toSet());
        List<List<Integer>> lists = PagingUtils.newPagingList(new ArrayList<>(productIds), 10);

        for (List<Integer> list : lists) {
            try {
                ProductAttributeTemplateRequest request = new ProductAttributeTemplateRequest();
                request.setProductTypeIdList(list);
                ApiBaseMultipleResponse<List<QueryAttributeTemplateResponse>> test = sheinApiClient.getAccountCategoryAttributeTemplate(accountNumber, request);
                if (test.isSuccess()) {
                    ApiBaseMultipleResponse.Info<List<QueryAttributeTemplateResponse>> info = test.getInfo();
                    List<QueryAttributeTemplateResponse> data = info.getData();
                    for (QueryAttributeTemplateResponse datum : data) {
                        sheinAccountCategoryAttributeValueService.removeDuplicatesAndSave(accountNumber, datum);
                    }
                } else {
                    XxlJobLogger.log("店铺账号查询属性失败：{}", accountNumber);
                }
            } catch (Exception e) {
                XxlJobLogger.log("店铺账号查询属性失败：{}", accountNumber);
            }
        }
    }
}

package com.estone.erp.publish.fruugo.call.model.request.filter;

import com.estone.erp.publish.fruugo.call.model.request.ProductFilter;
import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * @Description: SKU状态过滤器
 * <AUTHOR>
 * @Date 2024/11/15 下午3:31
 */
@Data
@Builder
public class StatusFilter extends ProductFilter {

    @Builder.Default
    private String type = "skuStatus";

    private List<String> status;

}

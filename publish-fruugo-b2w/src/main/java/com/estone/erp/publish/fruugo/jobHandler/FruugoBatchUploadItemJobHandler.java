package com.estone.erp.publish.fruugo.jobHandler;

import com.alibaba.fastjson.JSON;
import com.estone.erp.common.jobHandler.AbstractJobHandler;
import com.estone.erp.common.mq.PublishQueues;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.common.util.CacheUtils;
import com.estone.erp.publish.mq.PublishMqConfig;
import com.estone.erp.publish.system.account.AccountUtils;
import com.estone.erp.publish.system.account.SaleAccountAndBusinessResponse;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Semaphore;
import java.util.stream.Collectors;

/**
 * 1、fruugo模板刊登或者队列刊登符合刊登，生成处理报告后，将给平台的数据记录到中间表，每30分钟(可调整)定时任务扫描中间表待提交数据
 * a、每个店铺可提交数据按照创建时间升序取所有符合100的整数倍数据，分批次按照100条一次请求提交给平台
 * b、每个店铺不足100条的数据按照最后1条创建时间判定是否超过30分钟（做成系统参数配置），若超出30分钟也提交一次请求
 */
@Slf4j
@Component
public class FruugoBatchUploadItemJobHandler extends AbstractJobHandler {
    private final Semaphore SYNC_SP = new Semaphore(50);


    @Resource
    private RabbitTemplate rabbitTemplate;

    public FruugoBatchUploadItemJobHandler() {
        super(FruugoBatchUploadItemJobHandler.class.getName());
    }

    @Data
    static class InnerParam {
        private List<String> accountNumbers;
        private Integer batchCount;
    }

    @XxlJob("FruugoBatchUploadItemJobHandler")
    @Override
    public ReturnT<String> run(String param) throws Exception {
        XxlJobLogger.log("*****************系统自动刊登任务开始*****************");
        InnerParam innerParam = passParam(param, InnerParam.class);
        List<String> accountNumbers = new ArrayList<>();
        Integer batchCount = 100;
        if (ObjectUtils.isNotEmpty(innerParam)) {
            if (CollectionUtils.isNotEmpty(innerParam.getAccountNumbers())) {
                accountNumbers = innerParam.getAccountNumbers();
                XxlJobLogger.log("执行店铺：" + JSON.toJSONString(innerParam.getAccountNumbers()));
            }
            if (ObjectUtils.isNotEmpty(innerParam.getBatchCount())) {
                batchCount = innerParam.getBatchCount();
                XxlJobLogger.log("执行批次条数：" + innerParam.getBatchCount());
            }
        }
        //执行店铺
        List<String> executeAccountList = new ArrayList<>();
        //获取新店铺
        List<SaleAccountAndBusinessResponse> saleAccountList = AccountUtils.getSaleAccountListBySaleChannel(SaleChannel.CHANNEL_FRUUGO);
        if (CollectionUtils.isEmpty(saleAccountList)) {
            XxlJobLogger.log("未获取到店铺");
            return ReturnT.SUCCESS;
        }
        List<String> newAccountList = saleAccountList.stream().filter(saleAccount ->
                        BooleanUtils.isTrue(saleAccount.getColBool2()))
                .map(SaleAccountAndBusinessResponse::getAccountNumber).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(newAccountList)) {
            XxlJobLogger.log("未获取到新店铺");
            return ReturnT.SUCCESS;
        }
        //如果accountNumbers不为空，过滤得到新店铺
        if (CollectionUtils.isNotEmpty(accountNumbers)) {
            executeAccountList = accountNumbers.stream().filter(newAccountList::contains).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(executeAccountList)) {
                XxlJobLogger.log("参数中没有新店铺可执行");
                return ReturnT.SUCCESS;
            }
        } else {
            executeAccountList = newAccountList;
        }

        //获取参数间隔时间
        String intervalTime = CacheUtils.getSystemParamValue(SaleChannel.CHANNEL_FRUUGO, "FRUUGO", "BATCH_PUBLISH_INTERVAL_TIME", 10);
        if (StringUtils.isBlank(intervalTime)) {
            XxlJobLogger.log("未获取到参数间隔时间");
            return ReturnT.SUCCESS;

        }
        //按新店铺循环提交
        for (String accountNumber : executeAccountList) {
            Map<String, Object> map = new HashMap<>();
            map.put("accountNumber", accountNumber);
            map.put("intervalTime", intervalTime);
            map.put("batchCount", batchCount);
            rabbitTemplate.convertAndSend(PublishMqConfig.FRUUGO_API_DIRECT_EXCHANGE, PublishQueues.FRUUGO_BATCH_SUBMIT_ROUTING_KEY, map);

        }
        XxlJobLogger.log("*****************系统自动刊登任务结束*****************");
        return ReturnT.SUCCESS;
    }




}

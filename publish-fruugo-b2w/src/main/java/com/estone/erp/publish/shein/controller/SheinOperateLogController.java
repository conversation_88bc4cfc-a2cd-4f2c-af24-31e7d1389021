package com.estone.erp.publish.shein.controller;

import com.alibaba.fastjson.TypeReference;
import com.estone.erp.common.model.api.ApiRequestParam;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.model.api.CQuery;
import com.estone.erp.common.model.api.CQueryResult;
import com.estone.erp.common.util.Asserts;
import com.estone.erp.publish.common.ErrorCode;
import com.estone.erp.publish.shein.model.SheinOperateLog;
import com.estone.erp.publish.shein.model.SheinOperateLogCriteria;
import com.estone.erp.publish.shein.service.SheinOperateLogService;
import javax.annotation.Resource;
import org.apache.commons.lang.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * 2024-04-01 11:13:52
 */
@RestController
@RequestMapping("sheinOperateLog")
public class SheinOperateLogController {
    @Resource
    private SheinOperateLogService sheinOperateLogService;

    @PostMapping
    public ApiResult<?> postSheinOperateLog(@RequestBody(required = true) ApiRequestParam<String> requestParam) {
        String method = requestParam.getMethod();
        if (StringUtils.isNotBlank(method)) {
            switch (method) {
                case "searchSheinOperateLog": // 查询列表
                    CQuery<SheinOperateLogCriteria> cquery = requestParam.getArgsValue(new TypeReference<CQuery<SheinOperateLogCriteria>>() {});
                    Asserts.isTrue(cquery != null, ErrorCode.PARAM_EMPTY_ERROR);
                    CQueryResult<SheinOperateLog> results = sheinOperateLogService.search(cquery);
                    return results;
                case "addSheinOperateLog": // 添加
                    SheinOperateLog sheinOperateLog = requestParam.getArgsValue(new TypeReference<SheinOperateLog>() {});
                    sheinOperateLogService.insert(sheinOperateLog);
                    return ApiResult.newSuccess(sheinOperateLog);
                }
        }
        return ApiResult.newSuccess();
    }

    @GetMapping(value = "/{id}")
    public ApiResult<?> getSheinOperateLog(@PathVariable(value = "id", required = true) Integer id) {
        SheinOperateLog sheinOperateLog = sheinOperateLogService.selectByPrimaryKey(id);
        return ApiResult.newSuccess(sheinOperateLog);
    }

    @PutMapping(value = "/{id}")
    public ApiResult<?> putSheinOperateLog(@PathVariable(value = "id", required = true) Integer id, @RequestBody ApiRequestParam<String> requestParam) {
        String method = requestParam.getMethod();
        if (StringUtils.isNotBlank(method)) {
            switch (method) {
                case "updateSheinOperateLog": // 单个修改
                    SheinOperateLog sheinOperateLog = requestParam.getArgsValue(new TypeReference<SheinOperateLog>() {});
                    sheinOperateLogService.updateByPrimaryKeySelective(sheinOperateLog);
                    return ApiResult.newSuccess(sheinOperateLog);
                }
        }
        return ApiResult.newSuccess();
    }
}
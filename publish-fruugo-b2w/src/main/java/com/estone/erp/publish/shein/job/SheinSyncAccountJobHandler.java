package com.estone.erp.publish.shein.job;

import com.estone.erp.common.jobHandler.AbstractJobHandler;
import com.estone.erp.publish.shein.service.SheinAccountConfigService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @program: publish
 * @author: dinghong
 * @create: 2024/4/1
 **/
@Slf4j
@Component
public class SheinSyncAccountJobHandler extends AbstractJobHandler {

    @Autowired
    private SheinAccountConfigService sheinAccountConfigService;

    public SheinSyncAccountJobHandler() {
        super(SheinSyncAccountJobHandler.class.getName());
    }

    @Override
    @XxlJob("SheinSyncAccountJobHandler")
    public ReturnT<String> run(String param) throws Exception {
        XxlJobLogger.log("xxlJOB-Shein店铺配置同步店铺定时任务-开始");
        sheinAccountConfigService.syncAccountInfo(null);
        XxlJobLogger.log("xxlJOB-Shein店铺配置同步店铺定时任务-结束");
        return ReturnT.SUCCESS;
    }

}

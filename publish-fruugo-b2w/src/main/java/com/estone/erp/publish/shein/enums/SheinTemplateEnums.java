package com.estone.erp.publish.shein.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023-07-12 10:02
 */
public interface SheinTemplateEnums {

    /**
     * 刊登状态
     */
    @Getter
    @AllArgsConstructor
    enum PublishStatus {
        Waiting(1, "待刊登"),
        PUBLISHING(2, "刊登中"),
        SUCCESS(3, "刊登成功"),
        ERROR(4, "刊登失败");

        private final int code;
        private final String desc;


        public boolean isTrue(int val) {
            return this.code == val;
        }
    }

    /**
     * 刊登类型
     */
    @Getter
    @AllArgsConstructor
    enum PublishType {
        AUTO_PUBLISH(1, "自动刊登"),
        NORMAL(2, "普通刊登");

        private final int code;
        private final String desc;


        public boolean isTrue(int val) {
            return this.code == val;
        }
    }

    /**
     * 刊登角色
     */
    @Getter
    @AllArgsConstructor
    enum PublishRole {
        SYSTEM(0, "系统刊登"),
        SALE_MAN(1, "销售刊登");

        private final int code;
        private final String desc;


        public boolean isTrue(int val) {
            return this.code == val;
        }
    }

    /**
     * admin范本状态
     */
    @Getter
    @AllArgsConstructor
    enum AdminTemplateStatus {
        DISABLE(0, "禁用"),
        ENABLE(1, "启用");

        private final int code;
        private final String desc;

        public boolean isTrue(int val) {
            return this.code == val;
        }
    }


    /**
     * 上传图片方案
     * @link https://open.sheincorp.com/documents/system/4d96fc8f-4913-4211-8630-5d81e7fcc010
     */
    enum UploadImageStandardEnum {
        NOW(0, "现有方案"),
        STANDARD_A(1, "方案a"),
        STANDARD_B(2, "方案B"),
        ;

        @Getter
        private final Integer code;

        @Getter
        private final String desc;

        UploadImageStandardEnum(Integer code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public static boolean isNewStandard(Integer uploadImageStandard) {
            return Objects.equals(STANDARD_A.getCode(), uploadImageStandard) || Objects.equals(STANDARD_B.getCode(), uploadImageStandard);
        }
    }
}

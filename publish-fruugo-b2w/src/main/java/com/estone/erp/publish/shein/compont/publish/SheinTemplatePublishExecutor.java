package com.estone.erp.publish.shein.compont.publish;

import com.estone.erp.common.exception.BusinessException;
import com.estone.erp.publish.common.context.DataContextHolder;
import com.estone.erp.publish.shein.compont.publish.param.SheinTemplatePublishParam;
import com.estone.erp.publish.shein.enums.SheinTemplateEnums;
import com.estone.erp.publish.shein.model.vo.SheinTemplateDo;
import com.estone.erp.publish.shein.service.SheinTemplateService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 模板刊登
 *
 * <AUTHOR>
 * @date 2023/12/20 15:21
 */
@Slf4j
@Component
public class SheinTemplatePublishExecutor extends PublishExecutor<SheinTemplatePublishParam> {

    @Autowired
    private SheinTemplateService sheinTemplateService;

    @Override
    protected SheinTemplateDo getTemplateData(SheinTemplatePublishParam param) throws BusinessException {
        DataContextHolder.setUsername(param.getUser());

        // 查询模板
        SheinTemplateDo template = sheinTemplateService.getTemplateById(param.getTemplateId());
        if (null == template
//                || SheinTemplateEnums.PublishStatus.ERROR.getCode() == template.getPublishStatus()
                || SheinTemplateEnums.PublishStatus.SUCCESS.getCode() == (template.getPublishStatus())) {
            throw new RuntimeException("刊登失败，该模板状态无法刊登:" + param.getTemplateId());
        }

        // 重复刊登拦截
        templateValidation.validateRepeatPublish(template);

        // 保存模板
        template.setPublishType(SheinTemplateEnums.PublishType.NORMAL.getCode());
        template.setPublishRole(SheinTemplateEnums.PublishRole.SALE_MAN.getCode());
        template.setPublishStatus(SheinTemplateEnums.PublishStatus.PUBLISHING.getCode());
        sheinTemplateService.saveOrUpdate(template);
        return template;
    }
}

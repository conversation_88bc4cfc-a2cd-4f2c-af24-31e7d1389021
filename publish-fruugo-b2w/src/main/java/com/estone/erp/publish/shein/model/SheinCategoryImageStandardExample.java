package com.estone.erp.publish.shein.model;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;

public class SheinCategoryImageStandardExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Integer offset;

    public SheinCategoryImageStandardExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return offset;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Integer value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Integer value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Integer value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Integer value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Integer value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Integer> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Integer> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Integer value1, Integer value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Integer value1, Integer value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andCategoryIdIsNull() {
            addCriterion("category_id is null");
            return (Criteria) this;
        }

        public Criteria andCategoryIdIsNotNull() {
            addCriterion("category_id is not null");
            return (Criteria) this;
        }

        public Criteria andCategoryIdEqualTo(Integer value) {
            addCriterion("category_id =", value, "categoryId");
            return (Criteria) this;
        }

        public Criteria andCategoryIdNotEqualTo(Integer value) {
            addCriterion("category_id <>", value, "categoryId");
            return (Criteria) this;
        }

        public Criteria andCategoryIdGreaterThan(Integer value) {
            addCriterion("category_id >", value, "categoryId");
            return (Criteria) this;
        }

        public Criteria andCategoryIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("category_id >=", value, "categoryId");
            return (Criteria) this;
        }

        public Criteria andCategoryIdLessThan(Integer value) {
            addCriterion("category_id <", value, "categoryId");
            return (Criteria) this;
        }

        public Criteria andCategoryIdLessThanOrEqualTo(Integer value) {
            addCriterion("category_id <=", value, "categoryId");
            return (Criteria) this;
        }

        public Criteria andCategoryIdIn(List<Integer> values) {
            addCriterion("category_id in", values, "categoryId");
            return (Criteria) this;
        }

        public Criteria andCategoryIdNotIn(List<Integer> values) {
            addCriterion("category_id not in", values, "categoryId");
            return (Criteria) this;
        }

        public Criteria andCategoryIdBetween(Integer value1, Integer value2) {
            addCriterion("category_id between", value1, value2, "categoryId");
            return (Criteria) this;
        }

        public Criteria andCategoryIdNotBetween(Integer value1, Integer value2) {
            addCriterion("category_id not between", value1, value2, "categoryId");
            return (Criteria) this;
        }

        public Criteria andSpuImageDetailShowIsNull() {
            addCriterion("spu_image_detail_show is null");
            return (Criteria) this;
        }

        public Criteria andSpuImageDetailShowIsNotNull() {
            addCriterion("spu_image_detail_show is not null");
            return (Criteria) this;
        }

        public Criteria andSpuImageDetailShowEqualTo(Boolean value) {
            addCriterion("spu_image_detail_show =", value, "spuImageDetailShow");
            return (Criteria) this;
        }

        public Criteria andSpuImageDetailShowNotEqualTo(Boolean value) {
            addCriterion("spu_image_detail_show <>", value, "spuImageDetailShow");
            return (Criteria) this;
        }

        public Criteria andSpuImageDetailShowGreaterThan(Boolean value) {
            addCriterion("spu_image_detail_show >", value, "spuImageDetailShow");
            return (Criteria) this;
        }

        public Criteria andSpuImageDetailShowGreaterThanOrEqualTo(Boolean value) {
            addCriterion("spu_image_detail_show >=", value, "spuImageDetailShow");
            return (Criteria) this;
        }

        public Criteria andSpuImageDetailShowLessThan(Boolean value) {
            addCriterion("spu_image_detail_show <", value, "spuImageDetailShow");
            return (Criteria) this;
        }

        public Criteria andSpuImageDetailShowLessThanOrEqualTo(Boolean value) {
            addCriterion("spu_image_detail_show <=", value, "spuImageDetailShow");
            return (Criteria) this;
        }

        public Criteria andSpuImageDetailShowIn(List<Boolean> values) {
            addCriterion("spu_image_detail_show in", values, "spuImageDetailShow");
            return (Criteria) this;
        }

        public Criteria andSpuImageDetailShowNotIn(List<Boolean> values) {
            addCriterion("spu_image_detail_show not in", values, "spuImageDetailShow");
            return (Criteria) this;
        }

        public Criteria andSpuImageDetailShowBetween(Boolean value1, Boolean value2) {
            addCriterion("spu_image_detail_show between", value1, value2, "spuImageDetailShow");
            return (Criteria) this;
        }

        public Criteria andSpuImageDetailShowNotBetween(Boolean value1, Boolean value2) {
            addCriterion("spu_image_detail_show not between", value1, value2, "spuImageDetailShow");
            return (Criteria) this;
        }

        public Criteria andSpuImageDetailRequiredIsNull() {
            addCriterion("spu_image_detail_required is null");
            return (Criteria) this;
        }

        public Criteria andSpuImageDetailRequiredIsNotNull() {
            addCriterion("spu_image_detail_required is not null");
            return (Criteria) this;
        }

        public Criteria andSpuImageDetailRequiredEqualTo(Boolean value) {
            addCriterion("spu_image_detail_required =", value, "spuImageDetailRequired");
            return (Criteria) this;
        }

        public Criteria andSpuImageDetailRequiredNotEqualTo(Boolean value) {
            addCriterion("spu_image_detail_required <>", value, "spuImageDetailRequired");
            return (Criteria) this;
        }

        public Criteria andSpuImageDetailRequiredGreaterThan(Boolean value) {
            addCriterion("spu_image_detail_required >", value, "spuImageDetailRequired");
            return (Criteria) this;
        }

        public Criteria andSpuImageDetailRequiredGreaterThanOrEqualTo(Boolean value) {
            addCriterion("spu_image_detail_required >=", value, "spuImageDetailRequired");
            return (Criteria) this;
        }

        public Criteria andSpuImageDetailRequiredLessThan(Boolean value) {
            addCriterion("spu_image_detail_required <", value, "spuImageDetailRequired");
            return (Criteria) this;
        }

        public Criteria andSpuImageDetailRequiredLessThanOrEqualTo(Boolean value) {
            addCriterion("spu_image_detail_required <=", value, "spuImageDetailRequired");
            return (Criteria) this;
        }

        public Criteria andSpuImageDetailRequiredIn(List<Boolean> values) {
            addCriterion("spu_image_detail_required in", values, "spuImageDetailRequired");
            return (Criteria) this;
        }

        public Criteria andSpuImageDetailRequiredNotIn(List<Boolean> values) {
            addCriterion("spu_image_detail_required not in", values, "spuImageDetailRequired");
            return (Criteria) this;
        }

        public Criteria andSpuImageDetailRequiredBetween(Boolean value1, Boolean value2) {
            addCriterion("spu_image_detail_required between", value1, value2, "spuImageDetailRequired");
            return (Criteria) this;
        }

        public Criteria andSpuImageDetailRequiredNotBetween(Boolean value1, Boolean value2) {
            addCriterion("spu_image_detail_required not between", value1, value2, "spuImageDetailRequired");
            return (Criteria) this;
        }

        public Criteria andSpuImageDetailSingleIsNull() {
            addCriterion("spu_image_detail_single is null");
            return (Criteria) this;
        }

        public Criteria andSpuImageDetailSingleIsNotNull() {
            addCriterion("spu_image_detail_single is not null");
            return (Criteria) this;
        }

        public Criteria andSpuImageDetailSingleEqualTo(Boolean value) {
            addCriterion("spu_image_detail_single =", value, "spuImageDetailSingle");
            return (Criteria) this;
        }

        public Criteria andSpuImageDetailSingleNotEqualTo(Boolean value) {
            addCriterion("spu_image_detail_single <>", value, "spuImageDetailSingle");
            return (Criteria) this;
        }

        public Criteria andSpuImageDetailSingleGreaterThan(Boolean value) {
            addCriterion("spu_image_detail_single >", value, "spuImageDetailSingle");
            return (Criteria) this;
        }

        public Criteria andSpuImageDetailSingleGreaterThanOrEqualTo(Boolean value) {
            addCriterion("spu_image_detail_single >=", value, "spuImageDetailSingle");
            return (Criteria) this;
        }

        public Criteria andSpuImageDetailSingleLessThan(Boolean value) {
            addCriterion("spu_image_detail_single <", value, "spuImageDetailSingle");
            return (Criteria) this;
        }

        public Criteria andSpuImageDetailSingleLessThanOrEqualTo(Boolean value) {
            addCriterion("spu_image_detail_single <=", value, "spuImageDetailSingle");
            return (Criteria) this;
        }

        public Criteria andSpuImageDetailSingleIn(List<Boolean> values) {
            addCriterion("spu_image_detail_single in", values, "spuImageDetailSingle");
            return (Criteria) this;
        }

        public Criteria andSpuImageDetailSingleNotIn(List<Boolean> values) {
            addCriterion("spu_image_detail_single not in", values, "spuImageDetailSingle");
            return (Criteria) this;
        }

        public Criteria andSpuImageDetailSingleBetween(Boolean value1, Boolean value2) {
            addCriterion("spu_image_detail_single between", value1, value2, "spuImageDetailSingle");
            return (Criteria) this;
        }

        public Criteria andSpuImageDetailSingleNotBetween(Boolean value1, Boolean value2) {
            addCriterion("spu_image_detail_single not between", value1, value2, "spuImageDetailSingle");
            return (Criteria) this;
        }

        public Criteria andSpuImageSquareShowIsNull() {
            addCriterion("spu_image_square_show is null");
            return (Criteria) this;
        }

        public Criteria andSpuImageSquareShowIsNotNull() {
            addCriterion("spu_image_square_show is not null");
            return (Criteria) this;
        }

        public Criteria andSpuImageSquareShowEqualTo(Boolean value) {
            addCriterion("spu_image_square_show =", value, "spuImageSquareShow");
            return (Criteria) this;
        }

        public Criteria andSpuImageSquareShowNotEqualTo(Boolean value) {
            addCriterion("spu_image_square_show <>", value, "spuImageSquareShow");
            return (Criteria) this;
        }

        public Criteria andSpuImageSquareShowGreaterThan(Boolean value) {
            addCriterion("spu_image_square_show >", value, "spuImageSquareShow");
            return (Criteria) this;
        }

        public Criteria andSpuImageSquareShowGreaterThanOrEqualTo(Boolean value) {
            addCriterion("spu_image_square_show >=", value, "spuImageSquareShow");
            return (Criteria) this;
        }

        public Criteria andSpuImageSquareShowLessThan(Boolean value) {
            addCriterion("spu_image_square_show <", value, "spuImageSquareShow");
            return (Criteria) this;
        }

        public Criteria andSpuImageSquareShowLessThanOrEqualTo(Boolean value) {
            addCriterion("spu_image_square_show <=", value, "spuImageSquareShow");
            return (Criteria) this;
        }

        public Criteria andSpuImageSquareShowIn(List<Boolean> values) {
            addCriterion("spu_image_square_show in", values, "spuImageSquareShow");
            return (Criteria) this;
        }

        public Criteria andSpuImageSquareShowNotIn(List<Boolean> values) {
            addCriterion("spu_image_square_show not in", values, "spuImageSquareShow");
            return (Criteria) this;
        }

        public Criteria andSpuImageSquareShowBetween(Boolean value1, Boolean value2) {
            addCriterion("spu_image_square_show between", value1, value2, "spuImageSquareShow");
            return (Criteria) this;
        }

        public Criteria andSpuImageSquareShowNotBetween(Boolean value1, Boolean value2) {
            addCriterion("spu_image_square_show not between", value1, value2, "spuImageSquareShow");
            return (Criteria) this;
        }

        public Criteria andSpuImageSquareRequiredIsNull() {
            addCriterion("spu_image_square_required is null");
            return (Criteria) this;
        }

        public Criteria andSpuImageSquareRequiredIsNotNull() {
            addCriterion("spu_image_square_required is not null");
            return (Criteria) this;
        }

        public Criteria andSpuImageSquareRequiredEqualTo(Boolean value) {
            addCriterion("spu_image_square_required =", value, "spuImageSquareRequired");
            return (Criteria) this;
        }

        public Criteria andSpuImageSquareRequiredNotEqualTo(Boolean value) {
            addCriterion("spu_image_square_required <>", value, "spuImageSquareRequired");
            return (Criteria) this;
        }

        public Criteria andSpuImageSquareRequiredGreaterThan(Boolean value) {
            addCriterion("spu_image_square_required >", value, "spuImageSquareRequired");
            return (Criteria) this;
        }

        public Criteria andSpuImageSquareRequiredGreaterThanOrEqualTo(Boolean value) {
            addCriterion("spu_image_square_required >=", value, "spuImageSquareRequired");
            return (Criteria) this;
        }

        public Criteria andSpuImageSquareRequiredLessThan(Boolean value) {
            addCriterion("spu_image_square_required <", value, "spuImageSquareRequired");
            return (Criteria) this;
        }

        public Criteria andSpuImageSquareRequiredLessThanOrEqualTo(Boolean value) {
            addCriterion("spu_image_square_required <=", value, "spuImageSquareRequired");
            return (Criteria) this;
        }

        public Criteria andSpuImageSquareRequiredIn(List<Boolean> values) {
            addCriterion("spu_image_square_required in", values, "spuImageSquareRequired");
            return (Criteria) this;
        }

        public Criteria andSpuImageSquareRequiredNotIn(List<Boolean> values) {
            addCriterion("spu_image_square_required not in", values, "spuImageSquareRequired");
            return (Criteria) this;
        }

        public Criteria andSpuImageSquareRequiredBetween(Boolean value1, Boolean value2) {
            addCriterion("spu_image_square_required between", value1, value2, "spuImageSquareRequired");
            return (Criteria) this;
        }

        public Criteria andSpuImageSquareRequiredNotBetween(Boolean value1, Boolean value2) {
            addCriterion("spu_image_square_required not between", value1, value2, "spuImageSquareRequired");
            return (Criteria) this;
        }

        public Criteria andSkcImageDetailShowIsNull() {
            addCriterion("skc_image_detail_show is null");
            return (Criteria) this;
        }

        public Criteria andSkcImageDetailShowIsNotNull() {
            addCriterion("skc_image_detail_show is not null");
            return (Criteria) this;
        }

        public Criteria andSkcImageDetailShowEqualTo(Boolean value) {
            addCriterion("skc_image_detail_show =", value, "skcImageDetailShow");
            return (Criteria) this;
        }

        public Criteria andSkcImageDetailShowNotEqualTo(Boolean value) {
            addCriterion("skc_image_detail_show <>", value, "skcImageDetailShow");
            return (Criteria) this;
        }

        public Criteria andSkcImageDetailShowGreaterThan(Boolean value) {
            addCriterion("skc_image_detail_show >", value, "skcImageDetailShow");
            return (Criteria) this;
        }

        public Criteria andSkcImageDetailShowGreaterThanOrEqualTo(Boolean value) {
            addCriterion("skc_image_detail_show >=", value, "skcImageDetailShow");
            return (Criteria) this;
        }

        public Criteria andSkcImageDetailShowLessThan(Boolean value) {
            addCriterion("skc_image_detail_show <", value, "skcImageDetailShow");
            return (Criteria) this;
        }

        public Criteria andSkcImageDetailShowLessThanOrEqualTo(Boolean value) {
            addCriterion("skc_image_detail_show <=", value, "skcImageDetailShow");
            return (Criteria) this;
        }

        public Criteria andSkcImageDetailShowIn(List<Boolean> values) {
            addCriterion("skc_image_detail_show in", values, "skcImageDetailShow");
            return (Criteria) this;
        }

        public Criteria andSkcImageDetailShowNotIn(List<Boolean> values) {
            addCriterion("skc_image_detail_show not in", values, "skcImageDetailShow");
            return (Criteria) this;
        }

        public Criteria andSkcImageDetailShowBetween(Boolean value1, Boolean value2) {
            addCriterion("skc_image_detail_show between", value1, value2, "skcImageDetailShow");
            return (Criteria) this;
        }

        public Criteria andSkcImageDetailShowNotBetween(Boolean value1, Boolean value2) {
            addCriterion("skc_image_detail_show not between", value1, value2, "skcImageDetailShow");
            return (Criteria) this;
        }

        public Criteria andSkcImageDetailRequiredIsNull() {
            addCriterion("skc_image_detail_required is null");
            return (Criteria) this;
        }

        public Criteria andSkcImageDetailRequiredIsNotNull() {
            addCriterion("skc_image_detail_required is not null");
            return (Criteria) this;
        }

        public Criteria andSkcImageDetailRequiredEqualTo(Boolean value) {
            addCriterion("skc_image_detail_required =", value, "skcImageDetailRequired");
            return (Criteria) this;
        }

        public Criteria andSkcImageDetailRequiredNotEqualTo(Boolean value) {
            addCriterion("skc_image_detail_required <>", value, "skcImageDetailRequired");
            return (Criteria) this;
        }

        public Criteria andSkcImageDetailRequiredGreaterThan(Boolean value) {
            addCriterion("skc_image_detail_required >", value, "skcImageDetailRequired");
            return (Criteria) this;
        }

        public Criteria andSkcImageDetailRequiredGreaterThanOrEqualTo(Boolean value) {
            addCriterion("skc_image_detail_required >=", value, "skcImageDetailRequired");
            return (Criteria) this;
        }

        public Criteria andSkcImageDetailRequiredLessThan(Boolean value) {
            addCriterion("skc_image_detail_required <", value, "skcImageDetailRequired");
            return (Criteria) this;
        }

        public Criteria andSkcImageDetailRequiredLessThanOrEqualTo(Boolean value) {
            addCriterion("skc_image_detail_required <=", value, "skcImageDetailRequired");
            return (Criteria) this;
        }

        public Criteria andSkcImageDetailRequiredIn(List<Boolean> values) {
            addCriterion("skc_image_detail_required in", values, "skcImageDetailRequired");
            return (Criteria) this;
        }

        public Criteria andSkcImageDetailRequiredNotIn(List<Boolean> values) {
            addCriterion("skc_image_detail_required not in", values, "skcImageDetailRequired");
            return (Criteria) this;
        }

        public Criteria andSkcImageDetailRequiredBetween(Boolean value1, Boolean value2) {
            addCriterion("skc_image_detail_required between", value1, value2, "skcImageDetailRequired");
            return (Criteria) this;
        }

        public Criteria andSkcImageDetailRequiredNotBetween(Boolean value1, Boolean value2) {
            addCriterion("skc_image_detail_required not between", value1, value2, "skcImageDetailRequired");
            return (Criteria) this;
        }

        public Criteria andSkcImageDetailSingleIsNull() {
            addCriterion("skc_image_detail_single is null");
            return (Criteria) this;
        }

        public Criteria andSkcImageDetailSingleIsNotNull() {
            addCriterion("skc_image_detail_single is not null");
            return (Criteria) this;
        }

        public Criteria andSkcImageDetailSingleEqualTo(Boolean value) {
            addCriterion("skc_image_detail_single =", value, "skcImageDetailSingle");
            return (Criteria) this;
        }

        public Criteria andSkcImageDetailSingleNotEqualTo(Boolean value) {
            addCriterion("skc_image_detail_single <>", value, "skcImageDetailSingle");
            return (Criteria) this;
        }

        public Criteria andSkcImageDetailSingleGreaterThan(Boolean value) {
            addCriterion("skc_image_detail_single >", value, "skcImageDetailSingle");
            return (Criteria) this;
        }

        public Criteria andSkcImageDetailSingleGreaterThanOrEqualTo(Boolean value) {
            addCriterion("skc_image_detail_single >=", value, "skcImageDetailSingle");
            return (Criteria) this;
        }

        public Criteria andSkcImageDetailSingleLessThan(Boolean value) {
            addCriterion("skc_image_detail_single <", value, "skcImageDetailSingle");
            return (Criteria) this;
        }

        public Criteria andSkcImageDetailSingleLessThanOrEqualTo(Boolean value) {
            addCriterion("skc_image_detail_single <=", value, "skcImageDetailSingle");
            return (Criteria) this;
        }

        public Criteria andSkcImageDetailSingleIn(List<Boolean> values) {
            addCriterion("skc_image_detail_single in", values, "skcImageDetailSingle");
            return (Criteria) this;
        }

        public Criteria andSkcImageDetailSingleNotIn(List<Boolean> values) {
            addCriterion("skc_image_detail_single not in", values, "skcImageDetailSingle");
            return (Criteria) this;
        }

        public Criteria andSkcImageDetailSingleBetween(Boolean value1, Boolean value2) {
            addCriterion("skc_image_detail_single between", value1, value2, "skcImageDetailSingle");
            return (Criteria) this;
        }

        public Criteria andSkcImageDetailSingleNotBetween(Boolean value1, Boolean value2) {
            addCriterion("skc_image_detail_single not between", value1, value2, "skcImageDetailSingle");
            return (Criteria) this;
        }

        public Criteria andSkcImageSquareShowIsNull() {
            addCriterion("skc_image_square_show is null");
            return (Criteria) this;
        }

        public Criteria andSkcImageSquareShowIsNotNull() {
            addCriterion("skc_image_square_show is not null");
            return (Criteria) this;
        }

        public Criteria andSkcImageSquareShowEqualTo(Boolean value) {
            addCriterion("skc_image_square_show =", value, "skcImageSquareShow");
            return (Criteria) this;
        }

        public Criteria andSkcImageSquareShowNotEqualTo(Boolean value) {
            addCriterion("skc_image_square_show <>", value, "skcImageSquareShow");
            return (Criteria) this;
        }

        public Criteria andSkcImageSquareShowGreaterThan(Boolean value) {
            addCriterion("skc_image_square_show >", value, "skcImageSquareShow");
            return (Criteria) this;
        }

        public Criteria andSkcImageSquareShowGreaterThanOrEqualTo(Boolean value) {
            addCriterion("skc_image_square_show >=", value, "skcImageSquareShow");
            return (Criteria) this;
        }

        public Criteria andSkcImageSquareShowLessThan(Boolean value) {
            addCriterion("skc_image_square_show <", value, "skcImageSquareShow");
            return (Criteria) this;
        }

        public Criteria andSkcImageSquareShowLessThanOrEqualTo(Boolean value) {
            addCriterion("skc_image_square_show <=", value, "skcImageSquareShow");
            return (Criteria) this;
        }

        public Criteria andSkcImageSquareShowIn(List<Boolean> values) {
            addCriterion("skc_image_square_show in", values, "skcImageSquareShow");
            return (Criteria) this;
        }

        public Criteria andSkcImageSquareShowNotIn(List<Boolean> values) {
            addCriterion("skc_image_square_show not in", values, "skcImageSquareShow");
            return (Criteria) this;
        }

        public Criteria andSkcImageSquareShowBetween(Boolean value1, Boolean value2) {
            addCriterion("skc_image_square_show between", value1, value2, "skcImageSquareShow");
            return (Criteria) this;
        }

        public Criteria andSkcImageSquareShowNotBetween(Boolean value1, Boolean value2) {
            addCriterion("skc_image_square_show not between", value1, value2, "skcImageSquareShow");
            return (Criteria) this;
        }

        public Criteria andSkcImageSquareRequiredIsNull() {
            addCriterion("skc_image_square_required is null");
            return (Criteria) this;
        }

        public Criteria andSkcImageSquareRequiredIsNotNull() {
            addCriterion("skc_image_square_required is not null");
            return (Criteria) this;
        }

        public Criteria andSkcImageSquareRequiredEqualTo(Boolean value) {
            addCriterion("skc_image_square_required =", value, "skcImageSquareRequired");
            return (Criteria) this;
        }

        public Criteria andSkcImageSquareRequiredNotEqualTo(Boolean value) {
            addCriterion("skc_image_square_required <>", value, "skcImageSquareRequired");
            return (Criteria) this;
        }

        public Criteria andSkcImageSquareRequiredGreaterThan(Boolean value) {
            addCriterion("skc_image_square_required >", value, "skcImageSquareRequired");
            return (Criteria) this;
        }

        public Criteria andSkcImageSquareRequiredGreaterThanOrEqualTo(Boolean value) {
            addCriterion("skc_image_square_required >=", value, "skcImageSquareRequired");
            return (Criteria) this;
        }

        public Criteria andSkcImageSquareRequiredLessThan(Boolean value) {
            addCriterion("skc_image_square_required <", value, "skcImageSquareRequired");
            return (Criteria) this;
        }

        public Criteria andSkcImageSquareRequiredLessThanOrEqualTo(Boolean value) {
            addCriterion("skc_image_square_required <=", value, "skcImageSquareRequired");
            return (Criteria) this;
        }

        public Criteria andSkcImageSquareRequiredIn(List<Boolean> values) {
            addCriterion("skc_image_square_required in", values, "skcImageSquareRequired");
            return (Criteria) this;
        }

        public Criteria andSkcImageSquareRequiredNotIn(List<Boolean> values) {
            addCriterion("skc_image_square_required not in", values, "skcImageSquareRequired");
            return (Criteria) this;
        }

        public Criteria andSkcImageSquareRequiredBetween(Boolean value1, Boolean value2) {
            addCriterion("skc_image_square_required between", value1, value2, "skcImageSquareRequired");
            return (Criteria) this;
        }

        public Criteria andSkcImageSquareRequiredNotBetween(Boolean value1, Boolean value2) {
            addCriterion("skc_image_square_required not between", value1, value2, "skcImageSquareRequired");
            return (Criteria) this;
        }

        public Criteria andSkuImageDetailShowIsNull() {
            addCriterion("sku_image_detail_show is null");
            return (Criteria) this;
        }

        public Criteria andSkuImageDetailShowIsNotNull() {
            addCriterion("sku_image_detail_show is not null");
            return (Criteria) this;
        }

        public Criteria andSkuImageDetailShowEqualTo(Boolean value) {
            addCriterion("sku_image_detail_show =", value, "skuImageDetailShow");
            return (Criteria) this;
        }

        public Criteria andSkuImageDetailShowNotEqualTo(Boolean value) {
            addCriterion("sku_image_detail_show <>", value, "skuImageDetailShow");
            return (Criteria) this;
        }

        public Criteria andSkuImageDetailShowGreaterThan(Boolean value) {
            addCriterion("sku_image_detail_show >", value, "skuImageDetailShow");
            return (Criteria) this;
        }

        public Criteria andSkuImageDetailShowGreaterThanOrEqualTo(Boolean value) {
            addCriterion("sku_image_detail_show >=", value, "skuImageDetailShow");
            return (Criteria) this;
        }

        public Criteria andSkuImageDetailShowLessThan(Boolean value) {
            addCriterion("sku_image_detail_show <", value, "skuImageDetailShow");
            return (Criteria) this;
        }

        public Criteria andSkuImageDetailShowLessThanOrEqualTo(Boolean value) {
            addCriterion("sku_image_detail_show <=", value, "skuImageDetailShow");
            return (Criteria) this;
        }

        public Criteria andSkuImageDetailShowIn(List<Boolean> values) {
            addCriterion("sku_image_detail_show in", values, "skuImageDetailShow");
            return (Criteria) this;
        }

        public Criteria andSkuImageDetailShowNotIn(List<Boolean> values) {
            addCriterion("sku_image_detail_show not in", values, "skuImageDetailShow");
            return (Criteria) this;
        }

        public Criteria andSkuImageDetailShowBetween(Boolean value1, Boolean value2) {
            addCriterion("sku_image_detail_show between", value1, value2, "skuImageDetailShow");
            return (Criteria) this;
        }

        public Criteria andSkuImageDetailShowNotBetween(Boolean value1, Boolean value2) {
            addCriterion("sku_image_detail_show not between", value1, value2, "skuImageDetailShow");
            return (Criteria) this;
        }

        public Criteria andSkuImageDetailRequiredIsNull() {
            addCriterion("sku_image_detail_required is null");
            return (Criteria) this;
        }

        public Criteria andSkuImageDetailRequiredIsNotNull() {
            addCriterion("sku_image_detail_required is not null");
            return (Criteria) this;
        }

        public Criteria andSkuImageDetailRequiredEqualTo(Boolean value) {
            addCriterion("sku_image_detail_required =", value, "skuImageDetailRequired");
            return (Criteria) this;
        }

        public Criteria andSkuImageDetailRequiredNotEqualTo(Boolean value) {
            addCriterion("sku_image_detail_required <>", value, "skuImageDetailRequired");
            return (Criteria) this;
        }

        public Criteria andSkuImageDetailRequiredGreaterThan(Boolean value) {
            addCriterion("sku_image_detail_required >", value, "skuImageDetailRequired");
            return (Criteria) this;
        }

        public Criteria andSkuImageDetailRequiredGreaterThanOrEqualTo(Boolean value) {
            addCriterion("sku_image_detail_required >=", value, "skuImageDetailRequired");
            return (Criteria) this;
        }

        public Criteria andSkuImageDetailRequiredLessThan(Boolean value) {
            addCriterion("sku_image_detail_required <", value, "skuImageDetailRequired");
            return (Criteria) this;
        }

        public Criteria andSkuImageDetailRequiredLessThanOrEqualTo(Boolean value) {
            addCriterion("sku_image_detail_required <=", value, "skuImageDetailRequired");
            return (Criteria) this;
        }

        public Criteria andSkuImageDetailRequiredIn(List<Boolean> values) {
            addCriterion("sku_image_detail_required in", values, "skuImageDetailRequired");
            return (Criteria) this;
        }

        public Criteria andSkuImageDetailRequiredNotIn(List<Boolean> values) {
            addCriterion("sku_image_detail_required not in", values, "skuImageDetailRequired");
            return (Criteria) this;
        }

        public Criteria andSkuImageDetailRequiredBetween(Boolean value1, Boolean value2) {
            addCriterion("sku_image_detail_required between", value1, value2, "skuImageDetailRequired");
            return (Criteria) this;
        }

        public Criteria andSkuImageDetailRequiredNotBetween(Boolean value1, Boolean value2) {
            addCriterion("sku_image_detail_required not between", value1, value2, "skuImageDetailRequired");
            return (Criteria) this;
        }

        public Criteria andFlagTypeIsNull() {
            addCriterion("flag_type is null");
            return (Criteria) this;
        }

        public Criteria andFlagTypeIsNotNull() {
            addCriterion("flag_type is not null");
            return (Criteria) this;
        }

        public Criteria andFlagTypeEqualTo(Integer value) {
            addCriterion("flag_type =", value, "flagType");
            return (Criteria) this;
        }

        public Criteria andFlagTypeNotEqualTo(Integer value) {
            addCriterion("flag_type <>", value, "flagType");
            return (Criteria) this;
        }

        public Criteria andFlagTypeGreaterThan(Integer value) {
            addCriterion("flag_type >", value, "flagType");
            return (Criteria) this;
        }

        public Criteria andFlagTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("flag_type >=", value, "flagType");
            return (Criteria) this;
        }

        public Criteria andFlagTypeLessThan(Integer value) {
            addCriterion("flag_type <", value, "flagType");
            return (Criteria) this;
        }

        public Criteria andFlagTypeLessThanOrEqualTo(Integer value) {
            addCriterion("flag_type <=", value, "flagType");
            return (Criteria) this;
        }

        public Criteria andFlagTypeIn(List<Integer> values) {
            addCriterion("flag_type in", values, "flagType");
            return (Criteria) this;
        }

        public Criteria andFlagTypeNotIn(List<Integer> values) {
            addCriterion("flag_type not in", values, "flagType");
            return (Criteria) this;
        }

        public Criteria andFlagTypeBetween(Integer value1, Integer value2) {
            addCriterion("flag_type between", value1, value2, "flagType");
            return (Criteria) this;
        }

        public Criteria andFlagTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("flag_type not between", value1, value2, "flagType");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Timestamp value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Timestamp value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Timestamp value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Timestamp value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Timestamp value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Timestamp> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Timestamp> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Timestamp value1, Timestamp value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(Timestamp value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(Timestamp value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(Timestamp value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(Timestamp value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(Timestamp value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<Timestamp> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<Timestamp> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(Timestamp value1, Timestamp value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}
package com.estone.erp.publish.shein.platform.model.request.publish;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.List;

/**
 * 商品图片
 */
@Data
public class ImageInfo {

    /**
     * 否
     * 编辑在售商品时必传
     */
    @JSONField(name = "image_group_code")
    private String imageGroupCode;

    /**
     * 是
     * 图片列表
     */
    @JSONField(name = "image_info_list")
    private List<ImageInfoValue> imageInfoList;

    @Data
    public static class ImageInfoValue {
        /**
         * 否
         * 商品图片编辑时需要该字段，
         */
        @JSONField(name = "image_item_id")
        private Long imageItemId;

        /**
         * 是
         * 图片序号，不允许重复
         */
        @JSONField(name = "image_sort")
        private Integer imageSort;
        /**
         * 是
         * 图片类型 1-主图,2-细节图（最多11张）,5-方块图,6-色块图(色块图单SKC非必填，多SKC必填)
         */
        @JSONField(name = "image_type")
        private Integer imageType;
        /**
         * 是
         * 平台图片链接，不允许外部链接，
         */
        @JSONField(name = "image_url")
        private String imageUrl;

        @JSONField(serialize = false, deserialize = false)
        public transient String msg;
    }
}

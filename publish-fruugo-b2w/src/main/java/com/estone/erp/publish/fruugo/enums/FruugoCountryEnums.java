package com.estone.erp.publish.fruugo.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @Description:
 * <AUTHOR>
 * @Date 2024/11/4 下午2:15
 */
@Getter
@AllArgsConstructor
public enum FruugoCountryEnums {
    AE("AE", "阿联酋"),
    AT("AT", "奥地利"),
    AU("AU", "澳大利亚"),
    BE("BE", "比利时"),
    BH("BH", "巴林"),
    CA("CA", "加拿大"),
    CH("CH", "瑞士"),
    CN("CN", "中国"),
    CZ("CZ", "捷克"),
    DE("DE", "德国"),
    DK("DK", "丹麦"),
    EE("EE", "爱沙尼亚"),
    EG("EG", "埃及"),
    ES("ES", "西班牙"),
    FI("FI", "芬兰"),
    FR("FR", "法国"),
    GR("GR", "希腊"),
    HU("HU", "匈牙利"),
    IE("IE", "爱尔兰"),
    IL("IL", "以色列"),
    IN("IN", "印度"),
    IT("IT", "意大利"),
    JP("JP", "日本"),
    KW("KW", "科威特"),
    LT("LT", "立陶宛"),
    LU("LU", "卢森堡"),
    LV("LV", "拉脱维亚"),
    MY("MY", "马来西亚"),
    NL("NL", "荷兰"),
    NO("NO", "挪威"),
    NZ("NZ", "新西兰"),
    PH("PH", "菲律宾"),
    PL("PL", "波兰"),
    PT("PT", "葡萄牙"),
    QA("QA", "卡塔尔"),
    RO("RO", "罗马尼亚"),
    RU("RU", "俄罗斯"),
    SA("SA", "沙特阿拉伯"),
    SE("SE", "瑞典"),
    SG("SG", "新加坡"),
    SK("SK", "斯洛伐克"),
    TR("TR", "土耳其"),
    US("US", "美国"),
    ZA("ZA", "南非");

    private String code;
    private String name;

}

package com.estone.erp.publish.shein.compont.template;

import com.estone.erp.publish.platform.enums.SkuDataSourceEnum;
import com.estone.erp.publish.shein.compont.template.builder.SheinSingleProductTemplateBuilder;
import com.estone.erp.publish.shein.compont.template.builder.SheinTemplateBuilder;
import com.estone.erp.publish.shein.model.vo.SheinBuilderTemplateDO;
import com.estone.erp.publish.shein.model.vo.SheinTemplateDo;
import com.estone.erp.publish.shein.compont.model.SheinTemplateProductVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-07-05 11:55
 */
@Slf4j
@Component
public class SheinTemplateBuilderHandler {
    @Autowired
    private SheinSingleProductTemplateBuilder sheinSingleProductTemplateBuilder;

    private SheinTemplateBuilder getBuilder(SheinBuilderTemplateDO param) {
        if (!SkuDataSourceEnum.COMPOSE_SYSTEM.isTrue(param.getSkuDataSource())) {
            // 默认产品系统单品
            return sheinSingleProductTemplateBuilder;
        }

        throw new RuntimeException("暂不支持该数据源");
    }

    /**
     * 根据货号生成模板信息
     *
     * @param builderTemplate
     * @return
     */
    public SheinTemplateDo builderTemplate(SheinBuilderTemplateDO builderTemplate) {
        SheinTemplateBuilder builder = getBuilder(builderTemplate);
        return builder.builderTemplate(builderTemplate);
    }

    /**
     * 获取模板对应简要产品信息
     */
    public SheinTemplateProductVo getTemplateProductInfo(SheinBuilderTemplateDO param) {
        SheinTemplateBuilder builder = getBuilder(param);
        return builder.getTemplateProductInfo(param.getArticleNumber());
    }


    /**
     * 获取货号对应可以刊登的sku
     */
    public List<String> getCanPublishSku(SheinBuilderTemplateDO build) {
        SheinTemplateBuilder builder = getBuilder(build);
        return builder.getCanPublishSku(build.getArticleNumber());
    }

}

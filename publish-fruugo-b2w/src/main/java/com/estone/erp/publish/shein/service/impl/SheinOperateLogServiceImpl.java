package com.estone.erp.publish.shein.service.impl;

import com.estone.erp.common.model.api.CQuery;
import com.estone.erp.common.model.api.CQueryResult;
import com.estone.erp.publish.shein.mapper.SheinOperateLogMapper;
import com.estone.erp.publish.shein.model.SheinOperateLog;
import com.estone.erp.publish.shein.model.SheinOperateLogCriteria;
import com.estone.erp.publish.shein.model.SheinOperateLogExample;
import com.estone.erp.publish.shein.service.SheinOperateLogService;

import java.util.List;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

/**
 * <AUTHOR>
 * 2024-04-01 11:13:52
 */
@Service("sheinOperateLogService")
@Slf4j
public class SheinOperateLogServiceImpl implements SheinOperateLogService {
    @Resource
    private SheinOperateLogMapper sheinOperateLogMapper;

    @Override
    public int countByExample(SheinOperateLogExample example) {
        Assert.notNull(example, "example is null!");
        return sheinOperateLogMapper.countByExample(example);
    }

    @Override
    public CQueryResult<SheinOperateLog> search(CQuery<SheinOperateLogCriteria> cquery) {
        Assert.notNull(cquery, "cquery is null!");
        SheinOperateLogCriteria query = cquery.getSearch();
        SheinOperateLogExample example = query.getExample();
        example.setOrderByClause("create_date desc");
        long total = 0;
        int totalPages = 0;
        // 是否分页
        if (cquery.isPageReqired()) {
            total = sheinOperateLogMapper.countByExample(example);
            totalPages = (int) Math.ceil((double) total / cquery.getLimit());
            example.setLimit(cquery.getLimit());
            example.setOffset(cquery.getOffset());
        }
        List<SheinOperateLog> sheinOperateLogs = sheinOperateLogMapper.selectByExample(example);
        // 组装结果
        CQueryResult<SheinOperateLog> result = new CQueryResult<>();
        result.setTotal(total);
        result.setTotalPages(totalPages);
        result.setRows(sheinOperateLogs);
        return result;
    }

    @Override
    public SheinOperateLog selectByPrimaryKey(Integer id) {
        Assert.notNull(id, "id is null!");
        return sheinOperateLogMapper.selectByPrimaryKey(id);
    }

    @Override
    public List<SheinOperateLog> selectByExample(SheinOperateLogExample example) {
        Assert.notNull(example, "example is null!");
        return sheinOperateLogMapper.selectByExample(example);
    }

    @Override
    public int insert(SheinOperateLog record) {
        Assert.notNull(record, "record is null!");
        // 默认加时间和人
        return sheinOperateLogMapper.insert(record);
    }

    @Override
    public int updateByPrimaryKeySelective(SheinOperateLog record) {
        Assert.notNull(record, "record is null!");
        // 默认加修改时间和修改人
        return sheinOperateLogMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public int updateByExampleSelective(SheinOperateLog record, SheinOperateLogExample example) {
        Assert.notNull(record, "record is null!");
        // 默认加修改时间和修改人
        return sheinOperateLogMapper.updateByExampleSelective(record, example);
    }

    @Override
    public int deleteByPrimaryKey(List<Integer> ids) {
        Assert.notNull(ids, "ids is null!");
        return sheinOperateLogMapper.deleteByPrimaryKey(ids);
    }
}
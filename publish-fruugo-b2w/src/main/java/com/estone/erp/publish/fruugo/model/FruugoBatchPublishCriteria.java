package com.estone.erp.publish.fruugo.model;

import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * 2025-01-14 09:26:04
 */
public class FruugoBatchPublishCriteria extends FruugoBatchPublish {
    private static final long serialVersionUID = 1L;

    public FruugoBatchPublishExample getExample() {
        FruugoBatchPublishExample example = new FruugoBatchPublishExample();
        FruugoBatchPublishExample.Criteria criteria = example.createCriteria();
        if (StringUtils.isNotBlank(this.getAccount())) {
            criteria.andAccountEqualTo(this.getAccount());
        }
        if (this.getTemplateId() != null) {
            criteria.andTemplateIdEqualTo(this.getTemplateId());
        }
        if (StringUtils.isNotBlank(this.getPublishInfo())) {
            criteria.andPublishInfoEqualTo(this.getPublishInfo());
        }
        if (this.getPublishStatus() != null) {
            criteria.andPublishStatusEqualTo(this.getPublishStatus());
        }
        if (this.getCreateTime() != null) {
            criteria.andCreateTimeEqualTo(this.getCreateTime());
        }
        return example;
    }
}
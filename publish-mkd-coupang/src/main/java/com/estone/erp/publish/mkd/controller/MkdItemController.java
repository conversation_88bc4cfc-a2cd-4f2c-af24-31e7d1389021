package com.estone.erp.publish.mkd.controller;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.estone.erp.common.constant.RedisConstant;
import com.estone.erp.common.model.PmsSkuStatusEnum;
import com.estone.erp.common.model.SeaweedFile;
import com.estone.erp.common.model.api.ApiRequestParam;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.model.api.CQuery;
import com.estone.erp.common.model.usermgt_n.Employee2RedisVO;
import com.estone.erp.common.redis.config.RedisClusterTemplate;
import com.estone.erp.common.redis.util.TokenUtils;
import com.estone.erp.common.redis.util.WebUtils;
import com.estone.erp.common.util.CommonUtils;
import com.estone.erp.common.util.DateUtils;
import com.estone.erp.common.util.POIUtils;
import com.estone.erp.common.util.PublishRedisClusterUtils;
import com.estone.erp.common.util.upload.FmsApiUtil;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.common.WebSocketRequestDTO;
import com.estone.erp.publish.common.executors.MkdExecutors;
import com.estone.erp.publish.elasticsearch.model.EsMkdItem;
import com.estone.erp.publish.elasticsearch.model.beanrequest.EsMkdItemRequest;
import com.estone.erp.publish.elasticsearch.service.EsMkdItemService;
import com.estone.erp.publish.elasticsearch.util.EsAccountUtils;
import com.estone.erp.publish.feginService.modle.BatchPriceCalculatorRequest;
import com.estone.erp.publish.feginService.modle.BatchPriceCalculatorResponse;
import com.estone.erp.publish.mkd.constant.CommonConstant;
import com.estone.erp.publish.mkd.constant.MkdConstant;
import com.estone.erp.publish.mkd.enums.SiteEnum;
import com.estone.erp.publish.mkd.handler.MkdSyncListingHandler;
import com.estone.erp.publish.mkd.model.dto.MkdUpdateDto;
import com.estone.erp.publish.mkd.service.MkdItemService;
import com.estone.erp.publish.mkd.util.SkuUtils;
import com.estone.erp.publish.platform.model.DrainageSku;
import com.estone.erp.publish.platform.model.DrainageSkuCriteria;
import com.estone.erp.publish.platform.model.DrainageSkuExample;
import com.estone.erp.publish.platform.service.DrainageSkuService;
import com.estone.erp.publish.platform.util.Platform;
import com.estone.erp.publish.system.account.AccountUtils;
import com.estone.erp.publish.system.account.PriceCalculatedUtil;
import com.estone.erp.publish.system.account.SaleAccountAndBusinessResponse;
import com.estone.erp.publish.system.erpCommon.ErpCommonWebSocketApiClient;
import com.estone.erp.publish.system.newUsermgt.NewUsermgtUtils;
import com.estone.erp.publish.system.product.ProductInfo;
import com.xxl.job.core.util.DateUtil;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.apache.tools.ant.util.FileUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileOutputStream;
import java.io.OutputStream;
import java.lang.reflect.Field;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

@Slf4j
@RestController
@RequestMapping("/mkdItem")
public class MkdItemController {

    @Autowired
    MkdItemService mkdItemService;

    @Autowired
    EsMkdItemService esMkdItemService;

    @Resource
    private DrainageSkuService drainageSkuService;

    @Resource
    private ErpCommonWebSocketApiClient webSocketClient;

    @Resource
    private RedisClusterTemplate redisClusterTemplate;

    @Autowired
    private MkdSyncListingHandler syncListingHandler;

    private static final int exportSize = 400000;

    /**
     * 同步所有账号
     *
     * @return
     */
    @PostMapping("syncAll")
    public ApiResult<?> syncAll() {
        /*权限控制----start*/
        ApiResult<Boolean> superAdminOrEquivalent = NewUsermgtUtils.isSuperAdminOrEquivalent(SaleChannel.CHANNEL_MERCADOLIBRE);
        if (!superAdminOrEquivalent.isSuccess()) {
            return ApiResult.newError(superAdminOrEquivalent.getErrorMsg());
        }
        if (!superAdminOrEquivalent.getResult()) {
            return ApiResult.newError("该账号暂无全量同步去权限！");
        }
        /*权限控制----end*/

        String syncKey = MkdConstant.syncKey;
        String syncDate = PublishRedisClusterUtils.get(syncKey);
        if (StringUtils.isNotBlank(syncDate)) {
            return ApiResult.newError(String.format("一小时内只能同步一次全量账号，请在 " + syncDate + " 之后重试"));
        } else {
            //下一次同步时间
            Long syncDateNext = System.currentTimeMillis() + MkdConstant.oneHour;
            //插入Redis
            PublishRedisClusterUtils.set(syncKey, DateUtil.formatDateTime(new Date(syncDateNext)), MkdConstant.oneHour, TimeUnit.MILLISECONDS);
        }
        mkdItemService.syncAllItem(null);
        return ApiResult.newSuccess("后台已开始同步店铺产品，请到处理报告中查看结果");
    }

    /**
     * 同步单个店铺账号
     *
     * @return
     */
    @PostMapping("/syncAccount")
    public ApiResult<?> syncAccount(@RequestBody(required = true) ApiRequestParam<String> requestParam) {
        EsMkdItemRequest esMkdItemRequest = requestParam.getArgsValue(new TypeReference<EsMkdItemRequest>() {
        });
        List<String> accounts = esMkdItemRequest.getAccountNumber();
        for (String account : accounts) {
            if (StringUtils.isBlank(account)) {
                continue;
            }
            mkdItemService.syncAllItem(account);
        }
        return ApiResult.newSuccess("后台已开始同步店铺产品，请到处理报告中查看结果");
    }

    /**
     * 同步店铺
     *
     * @return
     */
    @PostMapping("/syncItems")
    public ApiResult<?> syncItems(@RequestBody(required = true) ApiRequestParam<String> requestParam) {
        EsMkdItemRequest esMkdItemRequest = requestParam.getArgsValue(new TypeReference<EsMkdItemRequest>() {
        });
        String account = esMkdItemRequest.getAccount();
        List<String> cbtItemIds = esMkdItemRequest.getItemId();
        for (String cbtItemId : cbtItemIds) {
            MkdExecutors.executeSyncAccount(() -> {
                SaleAccountAndBusinessResponse accountNumber = AccountUtils.getSaleAccountByAccountNumber(SaleChannel.CHANNEL_MERCADOLIBRE, account, false);
                if (accountNumber != null) {
                    syncListingHandler.syncIteByCbtId(accountNumber, cbtItemId);
                }
            });
        }
        return ApiResult.newSuccess("后台已开始同步产品，请到处理报告中查看结果");
    }



    private ApiResult<Object> batchSelectItemId(EsMkdItemRequest esMkdItemRequest, int variationsSize, Set<String> itemIdSet) {

        //如果变体字段有值，那就先按条件查询
        Page<EsMkdItem> allPage = esMkdItemService.page(esMkdItemRequest, variationsSize, 0, "itemId");

        if (CollectionUtils.isEmpty(allPage.getContent())) {
            return ApiResult.newError("未查询到数据");
        }
        //增加itemId，查询主体信息
        itemIdSet.addAll(allPage.getContent().stream().map(o -> o.getItemId()).collect(Collectors.toList()));
        return null;
    }

    private ApiResult<Object> insertSupervisorData(EsMkdItemRequest esMkdItemRequest) {
        //如果传入店铺为空并且不是超管或者最高权限者，则查询当前登录人下级列表
        ApiResult<Boolean> superAdminOrEquivalent = NewUsermgtUtils.isSuperAdminOrEquivalent(SaleChannel.CHANNEL_MERCADOLIBRE);
        if (!superAdminOrEquivalent.isSuccess()) {
            return ApiResult.newError(superAdminOrEquivalent.getErrorMsg());
        }
        if (CollectionUtils.isEmpty(esMkdItemRequest.getAccountNumber()) && !superAdminOrEquivalent.getResult()) {
            //查询销售对应店铺列表
            ApiResult<List<String>> authorAccountListResult = EsAccountUtils.getAuthorAccountList(SaleChannel.CHANNEL_MERCADOLIBRE, false);
            if (!authorAccountListResult.isSuccess()) {
                return ApiResult.newError(authorAccountListResult.getErrorMsg());
            }
            //查询销售对应店铺列表
            List<String> authorAccountList = authorAccountListResult.getResult();
            if (CollectionUtils.isEmpty(authorAccountList)) {
                return ApiResult.newError("未查询到可用店铺列表！");
            }
            esMkdItemRequest.setAccountNumber(authorAccountList);
        }
        return null;
    }


    /**
     * 整合变体map,形式为：<itemId,item对象>
     *
     * @param variationsPage
     * @return
     */
    private Map<String, List<EsMkdItem>> getItemSkuMap(Page<EsMkdItem> variationsPage) {
        Map<String, List<EsMkdItem>> mkdItemMap = new HashMap<>();
        for (EsMkdItem mkdItem : variationsPage.getContent()) {
            if (CollectionUtils.isEmpty(mkdItemMap.get(mkdItem.getSku()))) {
                List<EsMkdItem> itemList = new ArrayList<>();
                itemList.add(mkdItem);
                mkdItemMap.put(mkdItem.getSku(), itemList);
            } else {
                List<EsMkdItem> itemList = mkdItemMap.get(mkdItem.getSku());
                itemList.add(mkdItem);
                mkdItemMap.put(mkdItem.getSku(), itemList);
            }
        }
        return mkdItemMap;
    }

    /**
     * 修改库存
     *
     * @param requestParam
     * @return
     */
    @PostMapping("/updateInventory")
    public ApiResult<String> updateInventory(@RequestBody(required = true) ApiRequestParam<String> requestParam) {
        CQuery<List<MkdUpdateDto>> cquery = requestParam.getArgsValue(new TypeReference<CQuery<List<MkdUpdateDto>>>() {
        });
        List<MkdUpdateDto> search = cquery.getSearch();

        for (MkdUpdateDto mkdUpdateDto : search) {
            MkdExecutors.executeListingUpdateStock(()-> {
                mkdItemService.updateInventory(mkdUpdateDto);
            });
        }
        return ApiResult.newSuccess("后台已开始修改库存，请到处理报告中查看结果");
    }


    /**
     * 修改库存检查清仓甩卖
     * @param ids
     * @return
     */
    @PostMapping("checkUpdateStock")
    public ApiResult<Map<String,Integer>> checkReductionAndClean(@RequestBody List<String> ids) {
        return mkdItemService.checkReductionAndClean(ids);
    }

    /**
     * 修改价格
     *
     * @param requestParam
     * @return
     */
    @PostMapping("/updatePrice")
    public ApiResult<?> updatePrice(@RequestBody(required = true) ApiRequestParam<String> requestParam) {
        CQuery<List<MkdUpdateDto>> cquery = requestParam.getArgsValue(new TypeReference<CQuery<List<MkdUpdateDto>>>() {
        });
        List<MkdUpdateDto> search = cquery.getSearch();

        for (MkdUpdateDto mkdUpdateDto : search) {
            MkdExecutors.executeUpdateStock(() -> {
                mkdItemService.updatePrice(mkdUpdateDto);
            });
        }
        return ApiResult.newSuccess("后台已开始修改价格，请到处理报告中查看结果");
    }

    /**
     * 下架
     *
     * @param requestParam
     * @return
     */
    @PostMapping("/delete")
    public ApiResult<?> delete(@RequestBody(required = true) ApiRequestParam<String> requestParam) {
        CQuery<List<MkdUpdateDto>> cquery = requestParam.getArgsValue(new TypeReference<CQuery<List<MkdUpdateDto>>>() {
        });
        List<MkdUpdateDto> search = cquery.getSearch();
        List<String> itemIds = search.stream()
                .map(MkdUpdateDto::getItemId)
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toList());
        return mkdItemService.offlineItems(itemIds);
    }

    /**
     * 修改价格
     *
     * @param requestParam
     * @return
     */
    @PostMapping("/getPriceCalculate")
    public ApiResult<?> getPriceCalculate(@RequestBody(required = true) ApiRequestParam<String> requestParam) {
        CQuery<List<MkdUpdateDto>> cquery = requestParam.getArgsValue(new TypeReference<CQuery<List<MkdUpdateDto>>>() {
        });
        List<MkdUpdateDto> search = cquery.getSearch();

        //获取计算后的价格对应Map，<itemId-站点,price>
        Map<String, Double> priceCalculate = getPrices(search);

        //产品列表,<sku,产品信息>
        Map<String, ProductInfo> productInfoMap = getProductInfo(search);

        //筛选item下最重的sku的价格信息
        Map<String, Double> priceMap = new HashMap<>();
        for (MkdUpdateDto mkdUpdateDto : search) {
            //如果是单体
            if (CollectionUtils.isEmpty(mkdUpdateDto.getVariationsSkuList())) {
                priceMap.put(mkdUpdateDto.getItemId(), priceCalculate.get(mkdUpdateDto.getSku() + "-" + mkdUpdateDto.getSite()));
            } else {
                Double weight = 0.0;
                Double usPrice = 0.0;
                for (String sku : mkdUpdateDto.getVariationsSkuList()) {
                    ProductInfo productInfo = productInfoMap.get(sku);
                    if (productInfo != null) {
                        //计算总重量
                        Double calcSkuWeight = SkuUtils.calcSkuWeight(productInfo);
                        //取最重的一个计算的价格
                        if (calcSkuWeight > weight) {
                            weight = calcSkuWeight;
                            usPrice = priceCalculate.get(sku + "-" + mkdUpdateDto.getSite());
                        }
                    }
                }
                priceMap.put(mkdUpdateDto.getItemId(), usPrice);
            }
        }
        return ApiResult.newSuccess(priceMap);
    }

    /**
     * 获取产品列表
     *
     * @param mkdList
     * @return
     */
    private Map<String, ProductInfo> getProductInfo(List<MkdUpdateDto> mkdList) {
        List<String> skuList = new ArrayList<>();
        for (MkdUpdateDto mkdUpdateDto : mkdList) {
            if (StringUtils.isNotBlank(mkdUpdateDto.getSku())) {
                skuList.add(mkdUpdateDto.getSku());
            } else {
                if (CollectionUtils.isNotEmpty(mkdUpdateDto.getVariationsSkuList())) {
                    for (String sku : mkdUpdateDto.getVariationsSkuList()) {
                        skuList.add(sku);
                    }
                }
            }
        }
        Map<String, ProductInfo> productInfo = mkdItemService.getProductInfo(skuList);
        return productInfo;
    }

    /**
     * 获取计算价格
     *
     * @return
     */
    private Map<String, Double> getPrices(List<MkdUpdateDto> mkdList) {
        List<BatchPriceCalculatorRequest> reqList = new ArrayList<>();
        for (MkdUpdateDto mkdUpdateDto : mkdList) {
            //如果是单体直接获取
            if (CollectionUtils.isEmpty(mkdUpdateDto.getVariationsSkuList())) {
                BatchPriceCalculatorRequest req = new BatchPriceCalculatorRequest();
                req.setArticleNumber(mkdUpdateDto.getSku());
                req.setQuantity(1);
                req.setSaleChannel(Platform.Mercadolibre.name());
                req.setSite(mkdUpdateDto.getSite());
                req.setCountryCode(mkdUpdateDto.getSite());
                req.setShippingMethod(SiteEnum.getLogistics(mkdUpdateDto.getSite()));
                req.setGrossProfitRate(mkdUpdateDto.getProfitMargin());
                reqList.add(req);
            } else {
                for (String sku : mkdUpdateDto.getVariationsSkuList()) {
                    BatchPriceCalculatorRequest req = new BatchPriceCalculatorRequest();
                    req.setArticleNumber(sku);
                    req.setQuantity(1);
                    req.setSaleChannel(Platform.Mercadolibre.name());
                    req.setSite(mkdUpdateDto.getSite());
                    req.setCountryCode(mkdUpdateDto.getSite());
                    req.setShippingMethod(SiteEnum.getLogistics(mkdUpdateDto.getSite()));
                    req.setGrossProfitRate(mkdUpdateDto.getProfitMargin());
                    reqList.add(req);
                }
            }
        }
        //请求
        ApiResult<List<BatchPriceCalculatorResponse>> calcApiResult = PriceCalculatedUtil.batchPriceCalculator(reqList, 3);
        Map<String, Double> priceMap = new HashMap<>();
        if (calcApiResult.isSuccess()) {
            List<BatchPriceCalculatorResponse> results = calcApiResult.getResult();
            for (BatchPriceCalculatorResponse result : results) {
                if (result.getPrice() != null && result.getForeignPrice() != null) {
                    priceMap.put(result.getArticleNumber() + "-" + result.getSite(), result.getForeignPrice());
                }
            }
        }
        return priceMap;
    }


    /**
     * 查询设置的引流sku列表
     *
     * @param param
     * @return
     * @throws Exception
     */
    @PostMapping("/getDrainageSku")
    public ApiResult<?> getDrainageSku(@RequestBody @ApiParam(name = "param", required = true) ApiRequestParam<String> param) throws Exception {
        DrainageSku dto = param.getArgsValue(new TypeReference<DrainageSku>() {
        });
        DrainageSkuCriteria drainageSkuCriteria = new DrainageSkuCriteria();
        drainageSkuCriteria.setAccountNumber(dto.getAccountNumber());
        drainageSkuCriteria.setPlatform(dto.getPlatform());
        drainageSkuCriteria.setIsDrainage(true);
        DrainageSkuExample example = drainageSkuCriteria.getExample();
        List<DrainageSku> skuList = drainageSkuService.selectByExample(example);
        if (CollectionUtils.isEmpty(skuList)) {
            return ApiResult.newError(null);
        }
        //检查数据
        checkMode(skuList);
        return ApiResult.newSuccess(skuList);
    }

    /**
     * 保存引流sku列表
     *
     * @param param
     * @return
     * @throws Exception
     */
    @PostMapping("saveDrainageSku")
    public ApiResult<?> saveDrainageSku(@RequestBody @ApiParam(name = "param", required = true) ApiRequestParam<String> param) throws Exception {
        List<DrainageSku> dtoList = param.getArgsValue(new TypeReference<List<DrainageSku>>() {
        });
        int skuSize = dtoList.stream().distinct().collect(Collectors.toList()).size();
        //判断是否有重复的sku
        if (skuSize < dtoList.size()) {
            return ApiResult.newError("请勿设置重复的sku");
        }
        //检查数据
        List<String> list = checkMode(dtoList);
        if (CollectionUtils.isNotEmpty(list)) {
            return ApiResult.newError("sku:" + list.toString() + " 不存在或状态不为正常，如果是变体请输入子SKU");
        }
        ApiResult<?> result = drainageSkuService.updateOrInsert(dtoList);
        if (result.isSuccess()) {
            return ApiResult.newSuccess(CollectionUtils.isNotEmpty(list) ? "以下sku在listing中状态不为正常或不存在，自动设置为取消：" + list.toString() : "设置成功");
        } else {
            return result;
        }

    }


    public List<String> checkMode(List<DrainageSku> dtoList) {
        List<String> reqList = new ArrayList<>();
        //查询列表
        List<String> collect = dtoList.stream().filter(o -> o.getIsDrainage()).map(o -> o.getSku()).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(collect)) {
            return null;
        }
        EsMkdItemRequest esMkdItemRequest = new EsMkdItemRequest();
        //只查正常状态
        esMkdItemRequest.setSku(collect);
        esMkdItemRequest.setAccountNumber(Arrays.asList(dtoList.get(0).getAccountNumber()));
        esMkdItemRequest.setStatus(Arrays.asList("active"));
        //查询item表
        Page<EsMkdItem> page = esMkdItemService.page(esMkdItemRequest, 50, 0);
        //整合
        Map<String, List<EsMkdItem>> itemMap = getItemSkuMap(page);
        //判断返回状态（不存在，或者状态不是正常都置为false）
        for (DrainageSku drainageSku : dtoList) {
            List<EsMkdItem> itemList = itemMap.get(drainageSku.getSku());
            //不存在,不正常
            if (drainageSku.getIsDrainage() && CollectionUtils.isEmpty(itemList)) {
                drainageSku.setIsDrainage(false);
                reqList.add(drainageSku.getSku());
            }
        }
        return reqList;
    }

    /**
     * 导出美客多列表数据
     */
    @PostMapping("exportItemData")
    public ApiResult<?> exportItemData(@RequestBody(required = true) ApiRequestParam<String> requestParam, HttpServletResponse response) {
        if (redisClusterTemplate
                .existsKey(RedisConstant.MKD_PREFIX_SYSTEM + DateUtils.format(new Date(), "yyyy-MM-dd"))) {
            return ApiResult.newError("有用户正在同步美客多文件，请稍后操作！");
        }
        EsMkdItemRequest esMkdItemRequest = requestParam.getArgsValue(new TypeReference<EsMkdItemRequest>() {
        });

        //去除空格
        if (CollectionUtils.isNotEmpty(esMkdItemRequest.getSku())) {
            esMkdItemRequest.setSku(esMkdItemRequest.getSku().stream().map(o -> o.replace(" ", "")).collect(Collectors.toList()));
        }
        //去除空格
        if (CollectionUtils.isNotEmpty(esMkdItemRequest.getItemId())) {
            esMkdItemRequest.setItemId(esMkdItemRequest.getItemId().stream().map(StringUtils::trim).collect(Collectors.toList()));
        }

        /*权限控制----start*/
        ApiResult<Object> superAdminOrEquivalent = insertSupervisorData(esMkdItemRequest);
        if (superAdminOrEquivalent != null && !superAdminOrEquivalent.isSuccess()) {
            return superAdminOrEquivalent;
        }
        /*权限控制----end*/

        //查询变体的页面大小
        int variationsSize = 1000000;

        //用于存放查询的itemId（先用所有入参查询一次获取所有符合条件的主体和变体信息，并且itemId）
        Set<String> itemIdSet = new HashSet<>();

        // 查询出所有itemidSet
        ApiResult<Object> newError = batchSelectItemId(esMkdItemRequest, variationsSize, itemIdSet);
        if (CollectionUtils.isNotEmpty(itemIdSet)){
            if (itemIdSet.size() > exportSize){
                return ApiResult.newError("此次导出数据量超过40W,请减少数据重新导出!");
            }
        }
        if (newError != null && !newError.isSuccess()) return newError;
        //获取用户的token 并拿到用户的id
        String accessToken = WebUtils.getAccessToken();
        Employee2RedisVO employeeByToken = TokenUtils.getEmployeeByToken(accessToken);
        Long employeeId = employeeByToken.getEmployeeId();
        Executors.newSingleThreadExecutor().execute(() -> {
            redisClusterTemplate.set(RedisConstant.MKD_PREFIX_SYSTEM + DateUtils.format(new Date(), "yyyy-MM-dd"),
                    DateUtils.format(new Date(), "yyyy-MM-dd"), 10, TimeUnit.MINUTES);
            OutputStream os = null;
            String fileName = "MercadolibreItem-" + UUID.randomUUID() + ".xlsx";
            try {
                //置空请求参数
                int batchSize = 50000;
                // 获取导出的头部
                String attrs = esMkdItemRequest.getAttrs();
                List<String> fields = Arrays.asList(attrs.split(","));
                Set<String> titleArr = new LinkedHashSet<>(fields);
                final String[] headers = new String[titleArr.size()];
                int index = 0;
                // 循环获取标题头
                for (String columnName : titleArr) {
                    headers[index] = CommonConstant.EXPORT_MERCADOLIBRE_TABLE_COLUMNS.get(columnName);
                    index++;
                }
                AtomicInteger atomicInteger = new AtomicInteger(1);
                // 创建写出流
                os = new FileOutputStream(fileName);
                // 创建Workbook
                Workbook wb = new XSSFWorkbook();
                // 批量查询关联itemIdSet数据
                CommonUtils.batchResolve(new ArrayList<>(itemIdSet), batchSize, item -> {
                    try {
                        // 批量插入不同sheet
                        batchInsertTableSheet(item, atomicInteger, headers, titleArr, wb);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                });
                wb.write(os);
            } catch (Exception e) {
                e.printStackTrace();
            } finally {
                IOUtils.closeQuietly(os);
            }

            //创建压缩文件上传文件服务器
            WebSocketRequestDTO webSocketRequestDTO = new WebSocketRequestDTO();
            try {
                ApiResult<SeaweedFile> file = FmsApiUtil.publishFileUpload(new File(fileName), fileName, "mkd_module", "");
                if (file.isSuccess()){
                    log.info("生成的地址为"+file.getResult().getUrl());
                    webSocketRequestDTO.setContent(file.getResult().getUrl());
                    webSocketRequestDTO.setDesc("美客多列表导出成功");
                }else {
                    webSocketRequestDTO.setContent("美客多列表导出失败");
                    webSocketRequestDTO.setDesc("上传文件系统地址为空");
                }

            }catch (Exception e){
                webSocketRequestDTO.setContent("销售结汇明细导出失败");
                webSocketRequestDTO.setDesc("导出过程中系统出错");
                log.error(e.getMessage());
            } finally {
                //删除掉文件
                FileUtils.delete(new File(fileName));
                webSocketRequestDTO.setType("download");
                webSocketRequestDTO.setUserId(Math.toIntExact(employeeId));
                webSocketRequestDTO.setExpiredDate(DateUtils.addDays(new Date(),3));
                webSocketClient.sendWebSocketMessage(webSocketRequestDTO);
                log.info("已经发送到webSocket服务器");
                redisClusterTemplate.del(RedisConstant.MKD_PREFIX_SYSTEM + DateUtils.format(new Date(), "yyyy-MM-dd"));
            }
        });

        return ApiResult.newSuccess("在努力生成文件中，请稍等！！！！");
    }

    /**
     * 批量新增不同sheet
     * @param item
     * @param atomicInteger
     * @param headers
     * @param titleArr
     * @param wb
     * @throws java.io.IOException
     */
    private void batchInsertTableSheet(List<String> item, AtomicInteger atomicInteger, String[] headers, Set<String> titleArr, Workbook wb) throws java.io.IOException {
        CopyOnWriteArrayList<EsMkdItem> esMkdItems = new CopyOnWriteArrayList<>();
        //整合一个已查询出的变体集合<iemId,变体List>
        ConcurrentHashMap<String, List<EsMkdItem>> itemMap = new ConcurrentHashMap<>();
        List<EsMkdItem> exportDataList = new ArrayList<>();
        EsMkdItemRequest esMkdItemRequest = new EsMkdItemRequest();
        esMkdItemRequest.setItemId(item);
        // 查询该批数据的条数
        long pageTotal = esMkdItemService.countSelectData(esMkdItemRequest);
        int pageIndex = 1;
        int pageSize = 20000;
        if (pageTotal > pageSize) {
            pageIndex = (int) (pageTotal % pageSize == 0 ? pageTotal / pageSize : pageTotal / pageSize + 1);
        }
        CountDownLatch countDownLatch = new CountDownLatch(pageIndex);
        for (int i = 0; i < pageIndex; i++) {
            int finalI = i;
            MkdExecutors.executeSelectItemData(new Runnable() {
                @Override
                public void run() {
                    try {
                        // 批量查询数据
                        Page<EsMkdItem> page = esMkdItemService.page(esMkdItemRequest, pageSize, finalI);
                        for (EsMkdItem mkdItem : page.getContent()) {
                            //只收集变体
                            if (mkdItem.getIsVariation() == 1) {
                                if (CollectionUtils.isEmpty(itemMap.get(mkdItem.getItemId()))) {
                                    List<EsMkdItem> itemList = new ArrayList<>();
                                    itemList.add(mkdItem);
                                    itemMap.put(mkdItem.getItemId(), itemList);
                                } else {
                                    List<EsMkdItem> itemList = itemMap.get(mkdItem.getItemId());
                                    itemList.add(mkdItem);
                                    itemMap.put(mkdItem.getItemId(), itemList);
                                }
                            } else {
                                //非变体
                                esMkdItems.add(mkdItem);
                            }
                        }
                    }catch (Exception e){
                        log.error(e.getMessage());
                    }finally {
                        countDownLatch.countDown();
                    }
                }
            });
        }
        try {
            countDownLatch.await();
            //组装返参
            for (EsMkdItem mkdItem : esMkdItems) {
                // 获取查看改itemId是否存在子变体
                List<EsMkdItem> esMkdItemList = itemMap.get(mkdItem.getItemId());
                if (CollectionUtils.isNotEmpty(esMkdItemList)) {
                    // 存在则将子变体数据放入导出data列表中
                    exportDataList.addAll(esMkdItemList);
                } else {
                    // 不存在则将父变体数据放入data列表中
                    exportDataList.add(mkdItem);
                }
            }
            // 开始导出数据
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            final List<List<String>> awLists = new ArrayList<>();
            int sheetIndex = atomicInteger.getAndIncrement();
            POIUtils.createLocalExcel(headers, exportDataList, esMkdItem -> {
                return getDataLists(titleArr, headers, sdf, awLists, (EsMkdItem) esMkdItem);
            }, sheetIndex, wb);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }

    }


    private List<List<String>> getDataLists(Set<String> titleArr, String[] headers, SimpleDateFormat sdf, List<List<String>> awLists, EsMkdItem esMkdItem) {
        // 这里处理每行数据
        awLists.clear();
        List<String> awList = new ArrayList<>(headers.length);
        //获取反射类
        Class<? extends EsMkdItem> aClass = esMkdItem.getClass();
        for (String columnName : titleArr) {
            // 根据名称获取字段
            try {
                Field declaredField = aClass.getDeclaredField(columnName);
                //设置可访问私有属性
                declaredField.setAccessible(true);
                // 获取字段类型
                Class<?> type = declaredField.getType();
                Object value = declaredField.get(esMkdItem);
                // 根据类型不同 进行不同的处理方法
                if (type == List.class) {
                    // 类型是list集合
                    List<Object> list = (List<Object>) value;
                    if (CollectionUtils.isNotEmpty(list)) {
                        awList.add(joinStrByList(list));
                    } else {
                        awList.add("");
                    }
                } else if (type == JSONObject.class) {
                    // 不管是拿属性还是属性值都是通过属性这个参数获取
                    Field attributes = aClass.getDeclaredField("attributes");
                    attributes.setAccessible(true);
                    JSONObject attributesValue = (JSONObject) attributes.get(esMkdItem);
                    if (attributesValue != null) {
                        Map<String, String> map = JSONObject.parseObject(attributesValue.toJSONString(), new TypeReference<Map<String, String>>() {
                        });
                        if ("attributes".equals(columnName)) {
                            // 拼接属性
                            awList.add(joinStrByList(new ArrayList<>(map.keySet())));
                        } else {
                            // 拼接属性值
                            awList.add(joinStrByList(new ArrayList<>(map.values())));
                        }
                    } else {
                        awList.add("");
                    }
                } else if (type == Date.class) {
                    // 如果是Date属性值
                    Date date = (Date) value;
                    awList.add(date == null ? "" : POIUtils.transferObj2Str(sdf.format(date)));
                } else {
                    if ("itemStatus".equals(columnName)){
                        awList.add(value == null || value.equals("") ? "" : POIUtils.transferObj2Str(PmsSkuStatusEnum.getNameByCode((String) value)));
                        continue;
                    }
                    awList.add(value == null || value.equals("") ? "" : POIUtils.transferObj2Str(value));
                }

            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        awLists.add(awList);
        return awLists;
    }

    private String joinStrByList(List<Object> list) {
        StringBuilder buffer = new StringBuilder();
        list.forEach(o -> {
            //多条数据以换行符就行分割
            buffer.append(POIUtils.transferObj2Str(o)).append(",");
        });
        return buffer.deleteCharAt(buffer.lastIndexOf(",")).toString();

    }

}

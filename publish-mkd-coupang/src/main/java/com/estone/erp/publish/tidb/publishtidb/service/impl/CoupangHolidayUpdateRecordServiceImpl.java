package com.estone.erp.publish.tidb.publishtidb.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.estone.erp.common.model.TidbPageMeta;
import com.estone.erp.publish.tidb.publishtidb.mapper.CoupangHolidayUpdateRecordMapper;
import com.estone.erp.publish.tidb.publishtidb.model.CoupangHolidayUpdateRecord;
import com.estone.erp.publish.tidb.publishtidb.service.CoupangHolidayUpdateRecordService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * coupang春节任务记录 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-08
 */
@Service
public class CoupangHolidayUpdateRecordServiceImpl extends ServiceImpl<CoupangHolidayUpdateRecordMapper, CoupangHolidayUpdateRecord> implements CoupangHolidayUpdateRecordService {

    @Override
    public List<TidbPageMeta<Long>> getPageMetaListByWrapper(LambdaQueryWrapper<CoupangHolidayUpdateRecord> wrapper) {
        return baseMapper.getTidbPageMetaMap(wrapper);

    }
}

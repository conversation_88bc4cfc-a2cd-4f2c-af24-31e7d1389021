package com.estone.erp.publish.coupang.util;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022-04-30 14:43
 */
public class CoupangUtil {

    public static String getSpuBySellerSku(String sellerSku) {
        String sku = getSkuBySellerSku(sellerSku);
        if (StringUtils.isNotBlank(sku) && sku.contains("-")) {
            return sku.substring(0, sellerSku.indexOf("-"));
        }
        return sellerSku;
    }

    public static String getSkuBySellerSku(String sellerSku) {
        //截取索：0 ～ 第一个下划线
        if (StringUtils.isNotBlank(sellerSku) && sellerSku.contains("_")) {
            return sellerSku.substring(0, sellerSku.indexOf("_"));
        }
        return null;
    }

    public static List<Integer> resolveSplitterInteger(String strValue) {
        if (StringUtils.isBlank(strValue)) {
            return Collections.emptyList();
        }
        return Arrays.stream(StringUtils.split(strValue, ","))
                .filter(NumberUtils::isDigits)
                .map(Integer::parseInt)
                .collect(Collectors.toList());
    }

    public static List<String> resolveSplitterString(String strValue) {
        if (StringUtils.isBlank(strValue)) {
            return Collections.emptyList();
        }
        return Arrays.stream(StringUtils.split(strValue, ","))
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toList());
    }
}

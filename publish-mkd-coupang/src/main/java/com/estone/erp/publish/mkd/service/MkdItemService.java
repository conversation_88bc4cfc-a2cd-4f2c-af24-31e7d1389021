package com.estone.erp.publish.mkd.service;

import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.publish.elasticsearch.model.EsMkdItem;
import com.estone.erp.publish.mkd.model.dto.MkdUpdateDto;
import com.estone.erp.publish.system.account.SaleAccountAndBusinessResponse;
import com.estone.erp.publish.system.product.ProductInfo;
import com.estone.erp.publish.system.product.bean.StockObj;

import java.util.List;
import java.util.Map;

public interface MkdItemService {
    ApiResult<?> updateInventory(MkdUpdateDto mkdUpdateDto);

    void updatePrice(MkdUpdateDto mkdUpdateDto);

    Map<String, ProductInfo> getProductInfo(List<String> sku);

    /**
     * 根据ItemId进行下架
     * @param itemIds
     * @return
     */
    ApiResult<String> offlineItems(List<String> itemIds);

    void updateSingleItemStock(EsMkdItem item, SaleAccountAndBusinessResponse accountNumber, Integer stock, StockObj stockObj);

    void updateVariationItemStock(List<EsMkdItem> items, SaleAccountAndBusinessResponse accountNumber, Map<String, Integer> variations,  Map<String, StockObj> variationStockMap );

    ApiResult<Map<String, Integer>> checkReductionAndClean(List<String> ids);

    /**
     * 全量同步
     * @param account
     */
    void syncAllItem(String account);

    /**
     * 增量同步
     * @param account 账号
     * @param page 页数
     * @param beforeDay 几天前需要同步
     */
    void syncAddItem(String account, Integer page ,Integer beforeDay);

}

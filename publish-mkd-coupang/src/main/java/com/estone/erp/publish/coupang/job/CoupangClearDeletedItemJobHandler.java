package com.estone.erp.publish.coupang.job;

import com.coupang.marketplace.client.model.product.OSellerProduct;
import com.estone.erp.common.jobHandler.AbstractJobHandler;
import com.estone.erp.common.util.LocalDateTimeUtil;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.coupang.client.CoupangClient;
import com.estone.erp.publish.coupang.common.CoupangEnums;
import com.estone.erp.publish.elasticsearch.model.ESCoupangItem;
import com.estone.erp.publish.elasticsearch.model.beanrequest.ESCoupangRequest;
import com.estone.erp.publish.elasticsearch.service.EsCoupangService;
import com.estone.erp.publish.elasticsearch.util.EsAccountUtils;
import com.estone.erp.publish.system.account.AccountUtils;
import com.estone.erp.publish.system.account.SaleAccountAndBusinessResponse;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 校验在线列表状态
 *
 * <AUTHOR>
 * @date 2024-08-15 下午5:21
 */
@Component
public class CoupangClearDeletedItemJobHandler extends AbstractJobHandler {

    @Autowired
    private EsCoupangService esCoupangService;

    public CoupangClearDeletedItemJobHandler() {
        super(CoupangClearDeletedItemJobHandler.class.getName());
    }

    @Data
    private static class InnerParam {
        private Integer expireDay;
        private List<String> accountNumbers;
        private List<Long> sellerProductIds;
    }

    /**
     * @param param
     * @return
     * @throws Exception
     */
    @Override
    @XxlJob("CoupangCheckItemStatusJobHandler")
    public ReturnT<String> run(String param) throws Exception {
        InnerParam innerParam = passParam(param, InnerParam.class);
        if (innerParam == null) {
            return ReturnT.FAIL;
        }

        List<String> accountList = EsAccountUtils.getPlatformNormaLAccountListByEs(SaleChannel.CHANNEL_COUPANG);
        if (CollectionUtils.isNotEmpty(innerParam.getAccountNumbers())) {
            accountList = innerParam.getAccountNumbers();
        }

        for (String accountNumber : accountList) {
            try {
                List<Long> productIds = loadProductIds(accountNumber, innerParam);
                XxlJobLogger.log("{}，更新状态，读取{}条数据", accountNumber, productIds.size());
                updateItemStatus(accountNumber, productIds);
            } catch (Exception e) {
                XxlJobLogger.log("{},更新状态失败,error={}", accountNumber, e.getMessage(), e);
            }
        }
        return ReturnT.SUCCESS;
    }

    private List<Long> loadProductIds(String accountNumber, InnerParam innerParam) {
        List<Long> productIds = new ArrayList<>();
        ESCoupangRequest request = new ESCoupangRequest();
        request.setAccounts(List.of(accountNumber));
        if (CollectionUtils.isNotEmpty(innerParam.getSellerProductIds())) {
            request.setSellerProductId(innerParam.getSellerProductIds());
        }
        request.setFiled(new String[]{"id", "sellerProductId"});
        LocalDateTime expireTime = LocalDateTime.now().minusDays(innerParam.getExpireDay());
        request.setSyncEndDateTime(LocalDateTimeUtil.format(expireTime));
        List<Integer> auditStatus = Arrays.asList(CoupangEnums.Status.APPROVED.getCode(), CoupangEnums.Status.PARTIAL_APPROVED.getCode());
        request.setAuditStatus(auditStatus);
        int total = esCoupangService.scrollQueryExecutorTask(request, itemList -> {
            productIds.addAll(itemList.stream().map(ESCoupangItem::getProductId).filter(Objects::nonNull).collect(Collectors.toList()));
        });
        return productIds;
    }

    private void updateItemStatus(String accountNumber, List<Long> productIds) {
        if (CollectionUtils.isEmpty(productIds)) {
            return;
        }
        SaleAccountAndBusinessResponse account = AccountUtils.getSaleAccountByAccountNumber(SaleChannel.CHANNEL_COUPANG, accountNumber, false);
        for (Long productId : productIds) {
            CoupangClient client = CoupangClient.createClient(account.getMerchantId(), account.getClientSecret(), account.getClientId());
            try {
                // productInfo
                OSellerProduct productInfo = client.readSellerProduct(productId);
                CoupangEnums.Status statusEnum = CoupangEnums.Status.getStatusByKOR(productInfo.getStatusName());
                if (statusEnum == CoupangEnums.Status.DELETED) {
                    ESCoupangItem esCoupangItem = esCoupangService.getItemById(productId);
                    esCoupangService.deleteItem(esCoupangItem);
                    XxlJobLogger.log("[{}]商品已删除,sellerProductId={}", accountNumber, productId);
                }
            } catch (Exception e) {
                XxlJobLogger.log("读取商品信息失败,error={}", e.getMessage(), e);
                return;
            }
        }
    }
}

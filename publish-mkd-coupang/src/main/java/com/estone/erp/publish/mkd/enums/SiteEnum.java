package com.estone.erp.publish.mkd.enums;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

public enum SiteEnum {
    //秘鲁
    MPE("PE",""),
    //巴西
    MLB("BR","MCD-BR"),
    //萨尔瓦多
    MSV("SV",""),
    //玻利维亚
    MBO("BO",""),
    //阿根廷
    MLA("AR",""),
    //哥伦比亚
    MCO("CO","MCD-Mail"),
    //古巴
    MCU("CU",""),
    //巴拉圭
    MPY("PY",""),
    //尼加拉瓜
    MNI("NI",""),
    //智利
    MLC("CL","MCD-Mail"),
    //洪都拉斯
    MHN("HN",""),
    //委内瑞拉
    MLV("VE",""),
    //危地马拉
    MGT("GT",""),
    //巴拿马
    MPA("MPA",""),
    //多米尼加共和国
    MRD("DO",""),
    //墨西哥
    MLM("MX","MCD-360lion"),
    //乌拉圭
    MLU("UY",""),
    //哥斯达黎加
    MCR("CR",""),
    //厄瓜多尔
    MEC("EC","");


    private String site;
    
    private String logistics;

    private SiteEnum(String site,String logistics) {
        this.site = site;
        this.logistics = logistics;
    }

    public String getSite() {
        return site;
    }

    public void getSite(String site) {
        this.site = site;
    }

    /**
     * 获取站点ID
     * @param site
     * @return
     */
    public static String getSiteId(String site) {
        SiteEnum[] values = values();
        for (SiteEnum value : values) {
            if (value.site.equals(site)) {
                return value.name();
            }
        }
        return null;
    }

    /**
     * 获取物流
     */
    public static String getLogistics(String site){
        SiteEnum[] values = values();
        for (SiteEnum value : values) {
            if (value.site.equals(site)) {
                return value.logistics;
            }
        }
        return null;
    }

    public static List<String> getAllSite() {
        return Arrays.stream(values()).map(SiteEnum::getSite).collect(Collectors.toList());
    }

}

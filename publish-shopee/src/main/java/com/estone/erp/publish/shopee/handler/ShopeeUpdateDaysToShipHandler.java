package com.estone.erp.publish.shopee.handler;

import com.alibaba.excel.util.BooleanUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.nacos.common.utils.CollectionUtils;
import com.estone.erp.common.constant.ShopeeDaysToShipConstant;
import com.estone.erp.common.util.WebUtils;
import com.estone.erp.publish.common.component.StatusCode;
import com.estone.erp.publish.common.context.DataContextHolder;
import com.estone.erp.publish.common.enums.FeedTaskResultStatusEnum;
import com.estone.erp.publish.common.enums.FeedTaskStatusEnum;
import com.estone.erp.publish.common.json.ResponseError;
import com.estone.erp.publish.common.json.ResponseJson;
import com.estone.erp.publish.elasticsearch2.model.EsShopeeItem;
import com.estone.erp.publish.elasticsearch2.service.EsShopeeItemService;
import com.estone.erp.publish.shopee.api.v2.common.ShopeeResponse;
import com.estone.erp.publish.shopee.api.v2.param.add.PreOrderDtoV2;
import com.estone.erp.publish.shopee.api.v2.param.add.tier.UpdateModelDtoV2;
import com.estone.erp.publish.shopee.api.v2.param.listing.cud.UpdateItemV2;
import com.estone.erp.publish.shopee.api.v2.param.listing.cud.UpdateModelV2;
import com.estone.erp.publish.shopee.call.v2.ShopeeBundelDealCallV2;
import com.estone.erp.publish.shopee.component.ShopeeItemEsBulkProcessor;
import com.estone.erp.publish.shopee.dto.UpdateDaysToShipDto;
import com.estone.erp.publish.shopee.enums.ShopeeFeedTaskEnum;
import com.estone.erp.publish.shopee.service.ShopeeMarketingBundleDealService;
import com.estone.erp.publish.shopee.util.ShopeeFeedTaskHandleUtil;
import com.estone.erp.publish.shopee.util.ShopeeHttpUtils;
import com.estone.erp.publish.system.account.SaleAccountAndBusinessResponse;
import com.estone.erp.publish.system.feedTask.model.FeedTask;
import com.estone.erp.publish.tidb.publishtidb.model.ShopeeBundleDealProductListing;
import com.estone.erp.publish.tidb.publishtidb.model.ShopeeBundleDealProductListingExample;
import com.estone.erp.publish.tidb.publishtidb.service.ShopeeBundleDealProductListingService;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jsoup.helper.StringUtil;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.*;

/**
 * shopee 更新发货天数
 */
@Slf4j
@Component
public class ShopeeUpdateDaysToShipHandler {

    //  存在促销导致失败
    public static final String EXIST_PROMOTION_ERROR = "product.error_cannt_edit_pre_order_in_promotion";
    // 业务失败的
    public static final String EXIST_BUSINESS_ERROR = "product.error_busi";

    @Resource
    private ShopeeItemEsBulkProcessor shopeeItemEsBulkProcessor;

    @Resource
    private ShopeeUpdateLogisticInfoHandler shopeeUpdateLogisticInfoHandler;

    @Resource
    private EsShopeeItemService esShopeeItemService;

    @Resource
    private ShopeeBundleDealProductListingService shopeeBundleDealProductListingService;

    @Resource
    private ShopeeBundleDealHandler shopeeBundleDealHandler;

    @Resource
    private ShopeeMarketingBundleDealService shopeeMarketingBundleDealService;

    @Data
    static class QueryParam {
        private Integer itemOrderTotalNumber;
        private Integer redisStock;
    }

    @Data
    public static class RsUpdateInfo {
        // 判断结局是否成功
        private boolean success;
        // 判断是否调整过
        private boolean flag;
        private String msg;
    }

    public void updateDaysToShip(UpdateDaysToShipDto item, ResponseJson response, SaleAccountAndBusinessResponse account) {
        String currentUser = StringUtils.isBlank(WebUtils.getUserName()) ? DataContextHolder.getUsername() : WebUtils.getUserName();
        if (StringUtil.isBlank(currentUser)) {
            currentUser = "admin";
        }
        String finalCreator = currentUser;
        FeedTask feedTask = ShopeeFeedTaskHandleUtil.initFeedTask((task) -> {
            task.setAssociationId(item.getId() + "");
            task.setAccountNumber(item.getItemSeller());
            task.setArticleNumber(item.getSku());
            task.setCreatedBy(finalCreator);
            task.setAttribute2(item.getItemId());
            QueryParam queryParam = new QueryParam();
            queryParam.setRedisStock(item.getRedisStock());
            queryParam.setItemOrderTotalNumber(item.getTotalOrderNumber());
            task.setAttribute3(JSON.toJSONString(queryParam));
            task.setAttribute4(item.getOldDaysToShip() + "");
            task.setAttribute5(item.getNewDaysToShip() + "");
            task.setTaskType(ShopeeFeedTaskEnum.UPDATE_DAYS_TO_SHIP.getValue());
        });
        try {
            // 所限要判断是否为变体还是单体
            ShopeeResponse doResponse = doUpdateDaysToShip(item, account);
            if (StringUtils.isNotBlank(doResponse.getError())) {
                feedTask.setTaskStatus(FeedTaskStatusEnum.FINISH.getTaskStatus());
                //默认失败
                feedTask.setResultStatus(FeedTaskResultStatusEnum.FAIL.getResultStatus());
                feedTask.setFinishTime(new Timestamp(System.currentTimeMillis()));
                feedTask.setResultMsg(JSON.toJSONString(doResponse));
                ShopeeFeedTaskHandleUtil.updateFeedTask(feedTask);
                log.error("accountNumber:{}, itemId:{}, 更新发货天数失败的结果: {}", item.getItemSeller(), item.getItemId(), doResponse.getError());
                if (response != null) {
                    response.addError(new ResponseError(StatusCode.FAIL, item.getItemId(), item.getSku(), doResponse.getError()));
                }
            } else {
                feedTask.setTaskStatus(FeedTaskStatusEnum.FINISH.getTaskStatus());
                feedTask.setResultStatus(FeedTaskResultStatusEnum.SUCCESS.getResultStatus());
                feedTask.setFinishTime(new Timestamp(System.currentTimeMillis()));
                feedTask.setResultMsg("修改前发货天数：" + item.getOldDaysToShip() + "，修改后发货天数：" + item.getNewDaysToShip());
                ShopeeFeedTaskHandleUtil.updateFeedTask(feedTask);
                if (response != null) {
                    response.addError(new ResponseError(StatusCode.SUCCESS, item.getItemId(), item.getSku(), "修改发货天数成功！"));
                }
                shopeeItemEsBulkProcessor.updateDaysToShip(item.getId(), item.getNewDaysToShip(), finalCreator);
            }
        } catch (Exception e) {
            feedTask.setTaskStatus(FeedTaskStatusEnum.FINISH.getTaskStatus());
            //默认失败
            feedTask.setResultStatus(FeedTaskResultStatusEnum.FAIL.getResultStatus());
            feedTask.setFinishTime(new Timestamp(System.currentTimeMillis()));
            feedTask.setResultMsg(e.getMessage());
            ShopeeFeedTaskHandleUtil.updateFeedTask(feedTask);
            log.error("accountNumber:{}, itemId:{}, 更新发货天数发送异常: {}", item.getItemSeller(), item.getItemId(), e.getMessage());
        }
    }

    public void updateDaysToShip(UpdateDaysToShipDto item, SaleAccountAndBusinessResponse account, Map<String, Integer> logisticNameAndIdMap, ResponseJson response) {
        String currentUser = StringUtils.isBlank(WebUtils.getUserName()) ? DataContextHolder.getUsername() : WebUtils.getUserName();
        if (StringUtil.isBlank(currentUser)) {
            currentUser = "admin";
        }
        String finalCreator = currentUser;

        FeedTask feedTask = ShopeeFeedTaskHandleUtil.initFeedTask((task) -> {
            task.setAssociationId(item.getId() + "");
            task.setAccountNumber(item.getItemSeller());
            task.setArticleNumber(item.getSku());
            task.setCreatedBy(finalCreator);
            task.setAttribute2(item.getItemId());
            QueryParam queryParam = new QueryParam();
            queryParam.setRedisStock(item.getRedisStock());
            queryParam.setItemOrderTotalNumber(item.getTotalOrderNumber());
            task.setAttribute3(JSON.toJSONString(queryParam));
            task.setAttribute4(item.getOldDaysToShip() + "");
            task.setAttribute5(item.getNewDaysToShip() + "");
            task.setTaskType(ShopeeFeedTaskEnum.UPDATE_DAYS_TO_SHIP.getValue());
        });
        StringJoiner errorList = new StringJoiner(",");
        StringJoiner successList = new StringJoiner(",");
        EsShopeeItem allById = esShopeeItemService.findAllById(item.getId());
        // 避免重试次数过多
        int size = 0;
        while (true) {
            ShopeeResponse shopeeResponse = doUpdateDaysToShip(item, account);
            String error = shopeeResponse.getError();
            String message = shopeeResponse.getMessage();
            if (StringUtils.isBlank(error)) {
                feedTask.setTaskStatus(FeedTaskStatusEnum.FINISH.getTaskStatus());
                feedTask.setResultStatus(FeedTaskResultStatusEnum.SUCCESS.getResultStatus());
                feedTask.setFinishTime(new Timestamp(System.currentTimeMillis()));
                String addMsg = successList.toString();
                if (StringUtils.isNotBlank(addMsg)) {
                    addMsg = ", 涉及流程:" + addMsg;
                } else {
                    addMsg = "";
                }
                feedTask.setResultMsg("修改前发货天数：" + item.getOldDaysToShip() + "，修改后发货天数：" + item.getNewDaysToShip() + addMsg);
                ShopeeFeedTaskHandleUtil.updateFeedTask(feedTask);
                shopeeItemEsBulkProcessor.updateDaysToShip(item.getId(), item.getNewDaysToShip(), finalCreator);
                if (response != null) {
                    response.addError(new ResponseError(StatusCode.SUCCESS, item.getItemId(), item.getSku(), "修改发货天数成功！"));
                }
                return;
            }
            errorList.add(error + ":" + message);
            if (size > 2) {
                break;
            }
            RsUpdateInfo rsUpdateInfo = doUpdateDaysToShipError(allById, account, logisticNameAndIdMap, error, message);
            boolean success = rsUpdateInfo.isSuccess();
            if (!success) {
                boolean flag = rsUpdateInfo.isFlag();
                if (flag) {
                    errorList.add(rsUpdateInfo.getMsg());
                }
                break;
            } else {
                successList.add(rsUpdateInfo.getMsg());
            }
            size++;
        }

        feedTask.setTaskStatus(FeedTaskStatusEnum.FINISH.getTaskStatus());
        //默认失败
        feedTask.setResultStatus(FeedTaskResultStatusEnum.FAIL.getResultStatus());
        feedTask.setFinishTime(new Timestamp(System.currentTimeMillis()));

        String errorListString = errorList.toString();
        String successListString = successList.toString();

        String msg = "";
        if (StringUtils.isNotBlank(errorListString)) {
            msg = "失败原因：" + errorListString;
        }
        if (StringUtils.isNotBlank(successListString)) {
            msg = msg + ", 涉及成功流程：" + successListString;
        }
        feedTask.setResultMsg(msg);
        ShopeeFeedTaskHandleUtil.updateFeedTask(feedTask);
        if (response != null) {
            response.addError(new ResponseError(StatusCode.FAIL, item.getItemId(), item.getSku(), msg));
        }
    }

    /**
     * 更新发货天数
     * 规则，如果是物流错误，就将物流信息删除，再修改，如果是优惠套装错误，就取消优惠套装，再修改
     *
     * @param item
     * @param account
     */
    public void updateDaysToShip(UpdateDaysToShipDto item, SaleAccountAndBusinessResponse account, Map<String, Integer> logisticNameAndIdMap) {
        String currentUser = StringUtils.isBlank(WebUtils.getUserName()) ? DataContextHolder.getUsername() : WebUtils.getUserName();
        if (StringUtil.isBlank(currentUser)) {
            currentUser = "admin";
        }
        String finalCreator = currentUser;

        FeedTask feedTask = ShopeeFeedTaskHandleUtil.initFeedTask((task) -> {
            task.setAssociationId(item.getId() + "");
            task.setAccountNumber(item.getItemSeller());
            task.setArticleNumber(item.getSku());
            task.setCreatedBy(finalCreator);
            task.setAttribute2(item.getItemId());
            QueryParam queryParam = new QueryParam();
            queryParam.setRedisStock(item.getRedisStock());
            queryParam.setItemOrderTotalNumber(item.getTotalOrderNumber());
            task.setAttribute3(JSON.toJSONString(queryParam));
            task.setAttribute4(item.getOldDaysToShip() + "");
            task.setAttribute5(item.getNewDaysToShip() + "");
            task.setTaskType(ShopeeFeedTaskEnum.UPDATE_DAYS_TO_SHIP.getValue());
        });
        StringJoiner errorList = new StringJoiner(",");
        StringJoiner successList = new StringJoiner(",");
        EsShopeeItem allById = esShopeeItemService.findAllById(item.getId());
        // 避免重试次数过多
        int size = 0;
        while (true) {
            ShopeeResponse shopeeResponse = doUpdateDaysToShip(item, account);
            String error = shopeeResponse.getError();
            String message = shopeeResponse.getMessage();
            if (StringUtils.isBlank(error)) {
                feedTask.setTaskStatus(FeedTaskStatusEnum.FINISH.getTaskStatus());
                feedTask.setResultStatus(FeedTaskResultStatusEnum.SUCCESS.getResultStatus());
                feedTask.setFinishTime(new Timestamp(System.currentTimeMillis()));
                String addMsg = successList.toString();
                if (StringUtils.isNotBlank(addMsg)) {
                    addMsg = ", 涉及流程:" + addMsg;
                } else {
                    addMsg = "";
                }
                feedTask.setResultMsg("修改前发货天数：" + item.getOldDaysToShip() + "，修改后发货天数：" + item.getNewDaysToShip() + addMsg);
                ShopeeFeedTaskHandleUtil.updateFeedTask(feedTask);
                shopeeItemEsBulkProcessor.updateDaysToShip(item.getId(), item.getNewDaysToShip(), finalCreator);
                return;
            }
            errorList.add(error + ":" + message);
            if (size > 2) {
                break;
            }
            RsUpdateInfo rsUpdateInfo = doUpdateDaysToShipError(allById, account, logisticNameAndIdMap, error, message);
            boolean success = rsUpdateInfo.isSuccess();
            if (!success) {
                boolean flag = rsUpdateInfo.isFlag();
                if (flag) {
                    errorList.add(rsUpdateInfo.getMsg());
                }
                break;
            } else {
                successList.add(rsUpdateInfo.getMsg());
            }
            size++;
        }

        feedTask.setTaskStatus(FeedTaskStatusEnum.FINISH.getTaskStatus());
        //默认失败
        feedTask.setResultStatus(FeedTaskResultStatusEnum.FAIL.getResultStatus());
        feedTask.setFinishTime(new Timestamp(System.currentTimeMillis()));

        String errorListString = errorList.toString();
        String successListString = successList.toString();

        String msg = "";
        if (StringUtils.isNotBlank(errorListString)) {
            msg = "失败原因：" + errorListString;
        }
        if (StringUtils.isNotBlank(successListString)) {
            msg = msg + ", 涉及成功流程：" + successListString;
        }
        feedTask.setResultMsg(msg);
        ShopeeFeedTaskHandleUtil.updateFeedTask(feedTask);
    }

    public RsUpdateInfo doUpdateDaysToShipError(EsShopeeItem item, SaleAccountAndBusinessResponse account, Map<String, Integer> logisticNameAndIdMap, String error, String message) {
        if (error.equalsIgnoreCase(EXIST_BUSINESS_ERROR)) {
            // 判断站点
            Set<String> removeLogisticName = LogisticNameConstant.getRemoveLogisticName(item.getSite());
            boolean flag = false;
            if (CollectionUtils.isNotEmpty(removeLogisticName)) {
                for (String name : removeLogisticName) {
                    if (message.contains(name)) {
                        flag = true;
                        break;
                    }
                }
            }
            // 只要有包含一个，就将其他也给关闭了。避免重复关闭
            if (flag) {
                String errorMsg = shopeeUpdateLogisticInfoHandler.updateLogisticInfo(account, item, removeLogisticName, logisticNameAndIdMap);
                if (StringUtils.isNotBlank(errorMsg)) {
                    RsUpdateInfo rsUpdateInfo = new RsUpdateInfo();
                    rsUpdateInfo.setSuccess(false);
                    rsUpdateInfo.setFlag(true);
                    rsUpdateInfo.setMsg("调整物流失败：" + errorMsg);
                    return rsUpdateInfo;
                } else {
                    try {
                        Thread.sleep(1000);
                    } catch (InterruptedException e) {
                        log.error(e.getMessage());
                    }
                    RsUpdateInfo rsUpdateInfo = new RsUpdateInfo();
                    rsUpdateInfo.setSuccess(true);
                    rsUpdateInfo.setFlag(true);
                    rsUpdateInfo.setMsg("调整物流成功");
                    return rsUpdateInfo;
                }
            }
        }

        List<Long> list = shopeeMarketingBundleDealService.getNotExpireBundleId(account.getAccountNumber());
        if (CollectionUtils.isEmpty(list)) {
            // 没有优惠套装，不修改
            RsUpdateInfo rsUpdateInfo = new RsUpdateInfo();
            rsUpdateInfo.setSuccess(false);
            rsUpdateInfo.setFlag(true);
            rsUpdateInfo.setMsg("数据库没有找到item的优惠套装，不修改");
            return rsUpdateInfo;
        }
        // 无论如何都要进行优惠套装取消操作
        // 判断是否有优惠套装，如果有，取消优惠套装，再修改
        ShopeeBundleDealProductListingExample example = new ShopeeBundleDealProductListingExample();
        example.createCriteria().andAccountNumberEqualTo(account.getAccountNumber()).andItemIdEqualTo(Long.valueOf(item.getItemId()))
                .andBundleDealIdIn(list);
        List<ShopeeBundleDealProductListing> shopeeBundleDealProductListings = shopeeBundleDealProductListingService.selectByExample(example);

        if (CollectionUtils.isEmpty(shopeeBundleDealProductListings)) {
            // 没有优惠套装，不修改
            RsUpdateInfo rsUpdateInfo = new RsUpdateInfo();
            rsUpdateInfo.setSuccess(false);
            rsUpdateInfo.setFlag(true);
            rsUpdateInfo.setMsg("数据库没有找到item的优惠套装，不修改");
            return rsUpdateInfo;
        }
        ShopeeBundleDealProductListing shopeeBundleDealProductListing = shopeeBundleDealProductListings.get(0);
        String msg = shopeeBundleDealHandler.deleteBundleDeal(account, shopeeBundleDealProductListing);
        if (StringUtils.isNotBlank(msg)) {
            RsUpdateInfo rsUpdateInfo = new RsUpdateInfo();
            rsUpdateInfo.setSuccess(false);
            rsUpdateInfo.setFlag(true);
            rsUpdateInfo.setMsg("取消优惠套装失败：" + msg);
            return rsUpdateInfo;
        } else {
            RsUpdateInfo rsUpdateInfo = new RsUpdateInfo();
            rsUpdateInfo.setSuccess(true);
            rsUpdateInfo.setFlag(true);
            rsUpdateInfo.setMsg("取消优惠套装成功：itemId:" + item.getItemId() + "，优惠套装：" + shopeeBundleDealProductListing.getBundleDealId());
            return rsUpdateInfo;
        }
    }

    public ShopeeResponse doUpdateDaysToShip(UpdateDaysToShipDto item, SaleAccountAndBusinessResponse account) {
        // 所限要判断是否为变体还是单体
        ShopeeResponse doResponse = null;
        if (BooleanUtils.isTrue(item.getIsFather()) && BooleanUtils.isTrue(item.getIsGood())) {
            UpdateItemV2 v2 = new UpdateItemV2();
            v2.setItemId(Long.valueOf(item.getItemId()));
            PreOrderDtoV2 preOrder = new PreOrderDtoV2();
            preOrder.setDaysToShip(item.getNewDaysToShip());
            preOrder.setIsPreOrder(ShopeeDaysToShipConstant.isPreOrder(item.getNewDaysToShip()));
            v2.setPreOrder(preOrder);
            doResponse = ShopeeHttpUtils.doPostV2(account, v2);
        } else {
            UpdateModelV2 updateModelV2 = new UpdateModelV2();
            updateModelV2.setItemId(Long.valueOf(item.getItemId()));

            UpdateModelDtoV2 model = new UpdateModelDtoV2();
            model.setModelId(Long.valueOf(item.getModelId()));
            model.setModelSku(item.getItemSku());
            Integer newDaysToShip = item.getNewDaysToShip();
            PreOrderDtoV2 preOrderDtoV2 = new PreOrderDtoV2();
            preOrderDtoV2.setDaysToShip(newDaysToShip);
            preOrderDtoV2.setIsPreOrder(ShopeeDaysToShipConstant.isPreOrder(newDaysToShip));
            model.setPreOrder(preOrderDtoV2);
            updateModelV2.setModel(List.of(model));
            doResponse = ShopeeHttpUtils.doPostV2(account, updateModelV2);
        }
        return doResponse;
    }

}

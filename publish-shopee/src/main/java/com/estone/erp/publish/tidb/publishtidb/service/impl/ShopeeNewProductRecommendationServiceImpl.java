package com.estone.erp.publish.tidb.publishtidb.service.impl;

import com.alibaba.cloud.commons.lang.StringUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.nacos.common.utils.CollectionUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.estone.erp.common.model.TidbPageMeta;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.model.api.CQuery;
import com.estone.erp.common.model.api.CQueryResult;
import com.estone.erp.common.mq.PublishQueues;
import com.estone.erp.common.mq.PublishRabbitMqExchange;
import com.estone.erp.common.util.TidbPageMetaUtil;
import com.estone.erp.common.util.WebUtils;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.common.context.DataContextHolder;
import com.estone.erp.publish.component.PermissionsHelper;
import com.estone.erp.publish.shopee.component.download.ShopeeDownloadTypeEnums;
import com.estone.erp.publish.system.excel.enums.ExcelDownloadStatusEnums;
import com.estone.erp.publish.system.excel.model.ExcelDownloadLog;
import com.estone.erp.publish.system.excel.service.ExcelDownloadLogService;
import com.estone.erp.publish.system.newUsermgt.NewUsermgtUtils;
import com.estone.erp.publish.system.product.ProductInfo;
import com.estone.erp.publish.system.product.ProductUtils;
import com.estone.erp.publish.tidb.publishtidb.domain.ShopeeNewProductRecommendationDO;
import com.estone.erp.publish.tidb.publishtidb.domain.vo.ShopeeNewProductRecommendationVO;
import com.estone.erp.publish.tidb.publishtidb.mapper.ShopeeNewProductRecommendationMapper;
import com.estone.erp.publish.tidb.publishtidb.model.ShopeeNewProductRecommendation;
import com.estone.erp.publish.tidb.publishtidb.service.ShopeeNewProductRecommendationService;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 * Shopee 新品推荐列表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-05
 */
@Slf4j
@Service
public class ShopeeNewProductRecommendationServiceImpl extends ServiceImpl<ShopeeNewProductRecommendationMapper, ShopeeNewProductRecommendation> implements ShopeeNewProductRecommendationService {

    @Resource
    private PermissionsHelper permissionsHelper;

    @Resource
    private ShopeeNewProductRecommendationMapper shopeeNewProductRecommendationMapper;

    @Resource
    private ExcelDownloadLogService excelDownloadLogService;

    @Override
    public CQueryResult<ShopeeNewProductRecommendationVO> queryPage(CQuery<ShopeeNewProductRecommendationDO> cQuery) {
        ShopeeNewProductRecommendationDO search = cQuery.getSearch();

        // 判断销售为空，则不获取当前权限下的销售人员，直接查询当前分配主管下的数据
        isPermissionProcessing(search);

        // 查询数据
        Page<ShopeeNewProductRecommendationVO> page = new Page<>(cQuery.getPage(), cQuery.getLimit());
        IPage<ShopeeNewProductRecommendationVO> pageResult = shopeeNewProductRecommendationMapper.queryProductWithSites(page, search);
        CQueryResult<ShopeeNewProductRecommendationVO> result = new CQueryResult<>();
        result.setTotal(pageResult.getTotal());
        result.setTotalPages(Long.valueOf(pageResult.getPages()).intValue());
        result.setRows(pageResult.getRecords());
        result.setSuccess(true);
        return result;
    }


    @Override
    public ApiResult<String> download(CQuery<ShopeeNewProductRecommendationDO> cQuery) {
        try {
            ShopeeNewProductRecommendationDO search = cQuery.getSearch();

            // 判断销售为空，则不获取当前权限下的销售人员，直接查询当前分配主管下的数据
            isPermissionProcessing(search);

            Page<ShopeeNewProductRecommendationVO> page = new Page<>(0, 1);
            IPage<ShopeeNewProductRecommendationVO> pageResult = shopeeNewProductRecommendationMapper.queryProductWithSites(page, search);
            Long count = pageResult.getTotal();
            if (count == 0) {
                return ApiResult.newError("没有数据，无法下载");
            }
            if (count > 500000) {
                return ApiResult.newError("超出最大下载数量限制，无法下载");
            }

            // 构造导出日志
            ExcelDownloadLog downloadLog = new ExcelDownloadLog();
            downloadLog.setType(ShopeeDownloadTypeEnums.NEW_PRODUCT_RECOMMENDATION.getType());
            downloadLog.setQueryCondition(JSON.toJSONString(search));
            downloadLog.setDownloadCount(count.intValue());
            downloadLog.setStatus(ExcelDownloadStatusEnums.WAIT.getCode());
            downloadLog.setCreateTime(Timestamp.valueOf(LocalDateTime.now()));
            downloadLog.setCreateBy(StringUtils.isBlank(WebUtils.getUserName()) ? DataContextHolder.getUsername() : WebUtils.getUserName());
            // 发送队列
            excelDownloadLogService.addAndPushLog(downloadLog, SaleChannel.CHANNEL_SHOPEE, PublishRabbitMqExchange.SHOPEE_API_DIRECT_EXCHANGE, PublishQueues.SHOPEE_DOWNLOAD_QUEUE_KEY);
            return ApiResult.newSuccess("导出成功，稍后前往导出日志查看！");
        } catch (Exception e) {
            return ApiResult.newError(e.getMessage());
        }
    }

    /**
     * 权限处理
     *
     * @param search
     */
    private void isPermissionProcessing(ShopeeNewProductRecommendationDO search) {
        // 判断是否有权限
        ApiResult<Boolean> superAdminOrEquivalent = NewUsermgtUtils.isSuperAdminOrEquivalent(SaleChannel.CHANNEL_SHOPEE);
        if (!superAdminOrEquivalent.isSuccess()) {
            throw new RuntimeException(superAdminOrEquivalent.getErrorMsg());
        }

        String currentUser = WebUtils.getUserName();
        if (!superAdminOrEquivalent.getResult()) {
            List<String> groupEmployeeNoList = PermissionsHelper.getGroupEmployeeNoList(SaleChannel.CHANNEL_SHOPEE, WebUtils.getUserName());
            boolean isInGroup = groupEmployeeNoList.contains(currentUser);

            if (isInGroup && CollectionUtils.isEmpty(search.getSaleLeaderList())) {
                search.setSaleLeaderList(List.of(currentUser));
                return;
            }

            if (!isInGroup) {
                if (CollectionUtils.isEmpty(groupEmployeeNoList)) {
                    if (CollectionUtils.isEmpty(search.getSaleLeaderList())) {
                        search.setSaleList(List.of(currentUser));
                    }
                } else {
                    // 此时 groupEmployeeNoList 一定不为空
                    if (CollectionUtils.isEmpty(search.getSaleLeaderList())) {
                        search.setSaleLeaderList(groupEmployeeNoList);
                    }
                }
            }
        }
    }

    /**
     * 获取分页信息
     *
     * @return 分页信息
     */
    @Override
    public List<TidbPageMeta<Long>> getTidbPageMetaMap(LambdaQueryWrapper<ShopeeNewProductRecommendation> wrapper) {
        List<Map<Object, Object>> tidbPageMetaMap = shopeeNewProductRecommendationMapper.getTidbPageMetaMap(wrapper);
        return TidbPageMetaUtil.getPageMetaList(tidbPageMetaMap);
    }

    @Override
    public void updatePublishStatus(Long id, boolean isAllSitesPublished, LocalDateTime success) {
        LocalDateTime now = LocalDateTime.now();
        shopeeNewProductRecommendationMapper.updatePublishStatus(id, isAllSitesPublished, success, now);
    }

    @Override
    public int getAllSitesPublishedSuccessSpuCount(String sale, String saleLeader, List<String> spuList) {
        List<List<String>> partition = Lists.partition(spuList, 200);
        int count = 0;
        for (List<String> spuGroup : partition) {
            count += shopeeNewProductRecommendationMapper.getAllSitesPublishedSuccessSpuCount(sale, saleLeader, spuGroup);
        }
        return count;
    }

    @Override
    public int getTotalAssignedSpuCount(String sale, String saleLeader, List<String> spuList) {
        List<List<String>> partition = Lists.partition(spuList, 200);
        int count = 0;
        for (List<String> spuGroup : partition) {
            count += shopeeNewProductRecommendationMapper.getTotalAssignedSpuCount(sale, saleLeader, spuGroup);
        }
        return count;
    }

    @Override
    public int getSitePublishedSuccessSpuCount(String sale, String saleLeader, String site, List<String> spuList) {
        List<List<String>> partition = Lists.partition(spuList, 200);
        int count = 0;
        for (List<String> spuGroup : partition) {
            count += shopeeNewProductRecommendationMapper.getSitePublishedSuccessSpuCount(sale, saleLeader, site, spuGroup);
        }
        return count;
    }

    @Override
    public List<ShopeeNewProductRecommendation> getNotCompletedSpuCount(List<String> spuList) {
        List<List<String>> partition = Lists.partition(spuList, 200);
        List<ShopeeNewProductRecommendation> result = Lists.newArrayList();
        for (List<String> spuGroup : partition) {
            result.addAll(shopeeNewProductRecommendationMapper.getNotCompletedSpuCount(spuGroup));
        }
        return result;
    }

    @Override
    public void updateTitleToProductName() {
        List<ShopeeNewProductRecommendation> shopeeNewProductRecommendations = list();
        List<String> spuList = shopeeNewProductRecommendations.stream().map(ShopeeNewProductRecommendation::getSpu).collect(Collectors.toList());
        List<List<String>> partitions = Lists.partition(spuList, 100);
        Map<String, ProductInfo> productMap = partitions.stream()
                .map(ProductUtils::findProductInfos)
                .filter(CollectionUtils::isNotEmpty)
                .flatMap(List::stream)
                .collect(Collectors.toMap(
                        ProductInfo::getMainSku,
                        Function.identity(),
                        (p1, p2) -> p1
                ));
        for (ShopeeNewProductRecommendation recommendation : shopeeNewProductRecommendations) {
            ProductInfo productInfo = productMap.get(recommendation.getSpu());
            if (Objects.isNull(productInfo)) {
                continue;
            }
            recommendation.setTitle(productInfo.getName());
        }
        updateBatchById(shopeeNewProductRecommendations, 200);
    }

    @Override
    public ApiResult<String> refreshImgList(List<String> spuList) {
        LambdaQueryWrapper<ShopeeNewProductRecommendation> productRecommendationLambdaQueryWrapper = new LambdaQueryWrapper<>();
        productRecommendationLambdaQueryWrapper.in(CollectionUtils.isNotEmpty(spuList), ShopeeNewProductRecommendation::getSpu, spuList);
        productRecommendationLambdaQueryWrapper.isNull(CollectionUtils.isEmpty(spuList), ShopeeNewProductRecommendation::getImages);
        List<ShopeeNewProductRecommendation> shopeeNewProductRecommendations = baseMapper.selectList(productRecommendationLambdaQueryWrapper);
        if (CollectionUtils.isEmpty(shopeeNewProductRecommendations)) {
            return ApiResult.newSuccess("暂无数据处理");
        }

        spuList = shopeeNewProductRecommendations.stream().map(ShopeeNewProductRecommendation::getSpu).collect(Collectors.toList());
        List<List<String>> partitions = Lists.partition(spuList, 100);
        Map<String, List<ProductInfo>> productMap = partitions.stream()
                .map(ProductUtils::findProductInfos)
                .filter(CollectionUtils::isNotEmpty)
                .flatMap(List::stream)
                .collect(Collectors.groupingBy(
                        ProductInfo::getMainSku,
                        Collectors.toList()
                ));
        if (MapUtils.isEmpty(productMap)) {
            return ApiResult.newSuccess("暂无数据处理");
        }

        List<ShopeeNewProductRecommendation> newProductRecommendationList = shopeeNewProductRecommendations.stream()
                .map(product -> {
                    List<ProductInfo> productInfos = productMap.get(product.getSpu());
                    if (CollectionUtils.isNotEmpty(productInfos)) {
                        ProductInfo productInfo = productInfos.get(0);
                        product.setImages(productInfo.getFirstImage());
                    }
                    return product;
                })
                .collect(Collectors.toList());
        this.updateBatchById(newProductRecommendationList, 200);
        return ApiResult.newSuccess("处理成功");
    }

}

package com.estone.erp.publish.shopee.enums;

public enum ShopeeStopstatusEnum {
    WAIT(1, "提交中"),
    SUCCESS(2, "结束成功"),
    FAIL(3, "结束失败");;

    private int code;

    private String name;

    ShopeeStopstatusEnum(int code, String name) {
        this.name = name;
        this.code = code;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public static ShopeeStopstatusEnum build(int code) {
        ShopeeStopstatusEnum[] values = values();
        for (ShopeeStopstatusEnum type : values) {
            if (type.code == code) {
                return type;
            }
        }
        return null;
    }

    public static String getNameByCode(int code) {
        ShopeeStopstatusEnum[] values = values();
        for (ShopeeStopstatusEnum type : values) {
            if (type.code == code) {
                return type.getName();
            }
        }
        return null;
    }

    public int intCode() {
        return this.code;
    }
}

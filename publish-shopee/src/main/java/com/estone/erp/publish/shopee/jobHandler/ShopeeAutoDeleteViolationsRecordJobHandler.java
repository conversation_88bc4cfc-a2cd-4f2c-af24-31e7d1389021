package com.estone.erp.publish.shopee.jobHandler;

import com.alibaba.fastjson.JSON;
import com.alibaba.nacos.common.utils.CollectionUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.estone.erp.common.jobHandler.AbstractJobHandler;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.elasticsearch.model.SaleAccount;
import com.estone.erp.publish.elasticsearch.model.beanrequest.EsSaleAccountRequest;
import com.estone.erp.publish.elasticsearch.service.SaleAccountService;
import com.estone.erp.publish.shopee.model.ShopeeAccountConfig;
import com.estone.erp.publish.shopee.model.ShopeeAccountConfigExample;
import com.estone.erp.publish.shopee.service.ShopeeAccountConfigService;
import com.estone.erp.publish.system.account.ChannelEnum.SaleAccountStastusEnum;
import com.estone.erp.publish.tidb.publishtidb.model.ShopeeViolationsRecord;
import com.estone.erp.publish.tidb.publishtidb.service.IShopeeViolationsRecordService;
import com.google.common.collect.Lists;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Description:【shopee】自动删除违规/删除商品列表已禁卖数据定时任务
 * <AUTHOR>
 * @Date 2024/9/10 下午6:20
 */
@Slf4j
@Component
public class ShopeeAutoDeleteViolationsRecordJobHandler extends AbstractJobHandler {

    @Resource
    private SaleAccountService saleAccountService;

    @Resource
    private ShopeeAccountConfigService shopeeAccountConfigService;

    @Resource
    private IShopeeViolationsRecordService shopeeViolationsRecordService;

    public ShopeeAutoDeleteViolationsRecordJobHandler() {
        super(ShopeeAutoDeleteViolationsRecordJobHandler.class.getName());
    }

    @Data
    public static class InnerParam {
        private List<String> accountList;
        private List<String> productIdList;
    }

    @XxlJob("ShopeeAutoDeleteViolationsRecordJobHandler")
    @Override
    public ReturnT<String> run(String param) throws Exception {
        ShopeeAutoDeleteViolationsRecordJobHandler.InnerParam innerParam = null;
        if (StringUtils.isNotBlank(param)) {
            try {
                innerParam = JSON.parseObject(param, ShopeeAutoDeleteViolationsRecordJobHandler.InnerParam.class);
            } catch (Exception e) {
                XxlJobLogger.log("-------解析param出错：--------", e);
                return ReturnT.FAIL;
            }
        }
        if (innerParam == null) {
            innerParam = new ShopeeAutoDeleteViolationsRecordJobHandler.InnerParam();
        }
        XxlJobLogger.log("-------开始执行任务--------");
        List<String> productIdList = innerParam.getProductIdList();

        // 获取所有符合条件的账号
        String[] withFields = {"accountNumber", "colBool2", "accountStatus"};
        EsSaleAccountRequest request = new EsSaleAccountRequest();
        request.setAccountNumberList(innerParam.getAccountList());
        request.setSaleChannel(SaleChannel.CHANNEL_SHOPEE);
        request.setExcludeAccountStatusList(Collections.singletonList(SaleAccountStastusEnum.FROZEN.getCode()));
        List<SaleAccount> saleAccounts = saleAccountService.getSaleAccountsEs(request, withFields);
        if (CollectionUtils.isNotEmpty(saleAccounts)) {
            // 分批查询
            List<List<SaleAccount>> partition = Lists.partition(saleAccounts, 50);
            for (List<SaleAccount> saleAccountList : partition) {
                ShopeeAccountConfigExample example = new ShopeeAccountConfigExample();
                ShopeeAccountConfigExample.Criteria criteria = example.createCriteria();
                criteria.andAccountIn(saleAccountList.stream().map(SaleAccount::getAccountNumber).collect(Collectors.toList())).andAutomaticDeleteViolationProductEqualTo(true);
                example.setColumns("account, automatic_delete_violation_product");
                List<ShopeeAccountConfig> shopeeAccountConfigs = shopeeAccountConfigService.selectCustomColumnByExample(example);
                if (CollectionUtils.isEmpty(shopeeAccountConfigs)) {
                    continue;
                }

                // 自动删除违规商品
                LambdaQueryWrapper<ShopeeViolationsRecord> queryWrapper = new LambdaQueryWrapper<ShopeeViolationsRecord>()
                        .in(ShopeeViolationsRecord::getAccountNumber, shopeeAccountConfigs.stream().map(ShopeeAccountConfig::getAccount).collect(Collectors.toList()))
                        .in(CollectionUtils.isNotEmpty(productIdList), ShopeeViolationsRecord::getItemId, productIdList)
                        .eq(ShopeeViolationsRecord::getDeleteStatus, 2)
                        .eq(ShopeeViolationsRecord::getItemType, 1);
                List<ShopeeViolationsRecord> shopeeViolationsRecordList = shopeeViolationsRecordService.list(queryWrapper);
                shopeeViolationsRecordService.batchDeleteByViolationsRecordList(shopeeViolationsRecordList, "admin");
            }

        }
        XxlJobLogger.log("-------任务执行完毕--------");
        return ReturnT.SUCCESS;
    }
}

package com.estone.erp.publish.shopee.enums;

/**
 * 刊登步骤节点
 */
public enum ShopeeGlobalPublishStepEnum {
    PUBLISH_GLOBAL_ITEM( "PUBLISH_GLOBAL_ITEM","发布全球产品"),
    GLOBAL_ITEM_PUBLISH_TO_SHOP( "GLOBAL_ITEM_PUBLISH_TO_SHOP","全球产品发布到店铺"),
    UPLOAD_VIDEO( "UPLOAD_VIDEO","上传视频"),
    ITEM_PUBLISH_SUCCESS( "ITEM_PUBLISH_SUCCESS","发布成功"),
    ;

    private String code;
    private String name;

    ShopeeGlobalPublishStepEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getName() {
        return name;
    }

    public String getCode() {
        return code;
    }

    public static String getNameByCode(String code){
        for (ShopeeGlobalPublishStepEnum stepEnum : values()) {
            if(stepEnum.getCode().equals(code)){
                return stepEnum.getName();
            }
        }
        return null;
    }

}

package com.estone.erp.publish.shopee.jobHandler.marketing;

import com.estone.erp.common.jobHandler.AbstractJobHandler;
import com.estone.erp.common.mq.PublishQueues;
import com.estone.erp.common.mq.PublishRabbitMqExchange;
import com.estone.erp.common.mq.RabbitMqSender;
import com.estone.erp.common.util.DateUtils;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.shopee.component.marking.DiscountConfigParam;
import com.estone.erp.publish.shopee.dto.MarketingTaskDto;
import com.estone.erp.publish.shopee.enums.PublishOperatorStatusEnum;
import com.estone.erp.publish.shopee.enums.ShopeeConfigTypeEnum;
import com.estone.erp.publish.shopee.enums.ShopeeMarketingConfigTypeEnum;
import com.estone.erp.publish.shopee.model.ShopeeConfigTask;
import com.estone.erp.publish.shopee.model.ShopeeMarketingConfig;
import com.estone.erp.publish.shopee.service.ShopeeConfigTaskService;
import com.estone.erp.publish.shopee.service.ShopeeLogisticHandleService;
import com.estone.erp.publish.shopee.service.ShopeeMarketingConfigService;
import com.estone.erp.publish.system.account.AccountUtils;
import com.estone.erp.publish.system.account.ChannelEnum.SaleAccountStastusEnum;
import com.estone.erp.publish.system.account.SaleAccountAndBusinessResponse;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * shopee 市场营销 折扣配置任务
 * 要求
 *
 * @see com.estone.erp.publish.shopee.jobHandler.ShopeeItemAutoDiscountJob
 */
@Slf4j
@Component
public class ShopeeMarketingDiscountJob extends AbstractJobHandler {
    @Resource
    private ShopeeConfigTaskService shopeeConfigTaskService;

    @Resource
    private ShopeeMarketingConfigService shopeeMarketingConfigService;

    @Resource
    private RabbitMqSender rabbitMqSender;

    @Resource
    private ShopeeLogisticHandleService shopeeLogisticHandleService;

    public ShopeeMarketingDiscountJob() {
        super(ShopeeMarketingDiscountJob.class.getName());
    }

    @Data
    public static class InnerParam {
        private List<String> accountNumberList;

        private List<Integer> configIdList;

        // 开始统计刊登成功日期  此日期之前 使用item总数 之后日志使用item成功总数 来比较是否超过1000
        private Date beginCountSuccessDate;

        private Integer onePromotionMaximunItems = 1000;

        private List<String> productIdList;
    }

    @Override
    @XxlJob("shopeeMarketingDiscountJob")
    public ReturnT<String> run(String param) throws Exception {
        InnerParam innerParam = Optional.ofNullable(passParam(param, InnerParam.class)).orElseGet(InnerParam::new);
        Date currentDate = new Date();

        Timestamp timestamp = new Timestamp(currentDate.getTime());

        String toDay = DateUtils.format(currentDate, "yyyy-MM-dd");
        List<Integer> marketingIdList = innerParam.getConfigIdList();
        List<String> accountList = innerParam.getAccountNumberList();

        List<ShopeeMarketingConfig> runningConfigByDateHours = shopeeMarketingConfigService.getRunningConfigByDateHours(currentDate, ShopeeMarketingConfigTypeEnum.DISCOUNT);
        if (CollectionUtils.isNotEmpty(marketingIdList)) {
            runningConfigByDateHours = runningConfigByDateHours.stream().filter(a -> marketingIdList.contains(a.getId())).collect(Collectors.toList());
        }

        Map<String, SaleAccountAndBusinessResponse> accountMap = new HashMap<>();

        XxlJobLogger.log("开始执行折扣配置任务");
        for (ShopeeMarketingConfig config : runningConfigByDateHours) {
            String accounts = config.getAccounts();
            String execTime = config.getExecTime();
            Date execTimeDate = DateUtils.parseDate(toDay + " " + execTime, "yyyy-MM-dd HH:mm");
            Timestamp execTimeStamp = new Timestamp(execTimeDate.getTime());
            DiscountConfigParam configParam = (DiscountConfigParam) config.getMarketingConfigParam();

            if (StringUtils.isBlank(accounts)) {
                XxlJobLogger.log("配置[{}]-[{}]-[{}]，未配置账号，跳过", config.getId(), config.getName(), configParam);
                continue;
            }

            String[] accountArr = accounts.split(",");
            List<String> collect = Stream.of(accountArr).filter(StringUtils::isNotBlank).map(String::trim).distinct().collect(Collectors.toList());
            if (CollectionUtils.isEmpty(collect)) {
                XxlJobLogger.log("配置[{}]-[{}]-[{}]，未配置有效账号，跳过", config.getId(), config.getName(), configParam);
                continue;
            }

            if (CollectionUtils.isNotEmpty(accountList)) {
                collect = collect.stream().filter(accountList::contains).collect(Collectors.toList());
            }

            // 错误账号
            Set<String> errorAccountSet = new HashSet<>();
            // 有效账号
            Set<String> successAccountSet = new HashSet<>();
            Set<String> errorLogisticSet = new HashSet<>();
            Map<String, String> accountLogisticMap = new HashMap<>();
            for (String account : collect) {
                SaleAccountAndBusinessResponse saleAccount = accountMap.computeIfAbsent(account,
                        acc -> AccountUtils.getSaleAccountByAccountNumber(SaleChannel.CHANNEL_SHOPEE, acc));
                if (saleAccount == null || BooleanUtils.isTrue(saleAccount.getColBool2()) || SaleAccountStastusEnum.FROZEN.getCode().equals(saleAccount.getAccountStatus())) {
                    errorAccountSet.add(account);
                } else {
                    // 查询物流信息
                    Map<String, String> siteLogisticMap = shopeeLogisticHandleService.selectLogistic(account);
                    String logisticCode = siteLogisticMap.get(saleAccount.getAccountSite());
                    if (StringUtils.isBlank(logisticCode)) {
                        errorLogisticSet.add(account);
                    } else {
                        successAccountSet.add(account);
                        accountLogisticMap.put(account, logisticCode);
                    }
                }
            }
            if (CollectionUtils.isNotEmpty(errorAccountSet)) {
                XxlJobLogger.log("配置[{}]-[{}]-[{}]，冻结账号或sip账号不执行，无效账号{}", config.getId(), config.getName(), configParam, errorAccountSet);
            }
            if (CollectionUtils.isNotEmpty(errorLogisticSet)) {
                XxlJobLogger.log("配置[{}]-[{}]-[{}]，无有效物流{}，跳过", config.getId(), config.getName(), configParam, errorLogisticSet);
            }
            if (CollectionUtils.isEmpty(successAccountSet)) {
                XxlJobLogger.log("配置[{}]-[{}]-[{}]，无正常账号，跳过", config.getId(), config.getName(), configParam);
                continue;
            }
            XxlJobLogger.log("配置[{}]-[{}]-[{}]，正常账号且有物流信息，执行账号{}，", config.getId(), config.getName(), configParam, successAccountSet);

            // 判断店铺是否执行过了
            Integer marketingId = config.getId();
            // 要判断店铺跟配置是否执行过了

            // 判断今天跑过的店铺
            Set<String> isRanAccountSet = shopeeConfigTaskService.isRanTime(successAccountSet, ShopeeConfigTypeEnum.DISCOUNT, marketingId);
            if (CollectionUtils.isNotEmpty(isRanAccountSet)) {
                XxlJobLogger.log("配置[{}]-[{}]，已执行过的店铺，跳过{}，", config.getId(), config.getName(), isRanAccountSet);
            }
            successAccountSet = successAccountSet.stream().filter(a -> !isRanAccountSet.contains(a)).collect(Collectors.toSet());

            if (CollectionUtils.isEmpty(successAccountSet)) {
                XxlJobLogger.log("配置[{}]-[{}]，无未执行的店铺，跳过", config.getId(), config.getName());
                continue;
            }
            XxlJobLogger.log("配置[{}]-[{}]，开始生成任务, 店铺：{}", config.getId(), config.getName(), successAccountSet);

            for (String accountNumber : successAccountSet) {
                ShopeeConfigTask discountTask = new ShopeeConfigTask();
                discountTask.setConfigType(ShopeeConfigTypeEnum.DISCOUNT.getCode());
                discountTask.setConfigId(config.getId());
                discountTask.setConfigRuleJson(config.getRuleJson());
                discountTask.setConfigName(config.getName());
                discountTask.setAccountNumber(accountNumber);
                discountTask.setExecTime(execTimeStamp);
                discountTask.setExecDay(toDay);
                discountTask.setStatus(0);
                discountTask.setCreatedTime(timestamp);
                discountTask.setUpdatedTime(timestamp);
                discountTask.setOperatorTime(timestamp);
                discountTask.setOperatorStatus(PublishOperatorStatusEnum.WAITING.getCode());
                shopeeConfigTaskService.insert(discountTask);

                Integer id = discountTask.getId();
                String logisticCode = accountLogisticMap.get(accountNumber);
                send(id, accountNumber, innerParam.getOnePromotionMaximunItems(), innerParam.getProductIdList(), logisticCode);
            }
            XxlJobLogger.log("配置id[{}]-[{}]-[{}]，生成任务成功, 店铺：[{}]", config.getId(), config.getName(), configParam, successAccountSet);
        }
        XxlJobLogger.log("全部配置执行完毕");
        return ReturnT.SUCCESS;
    }

    private void send(Integer id, String accountNumber, Integer max, List<String> productIdList, String logisticCode) {
        MarketingTaskDto marketingTaskDto = new MarketingTaskDto();
        marketingTaskDto.setMarketingTaskId(id);
        marketingTaskDto.setAccountNumber(accountNumber);
        marketingTaskDto.setOnePromotionMaximunItems(max);
        marketingTaskDto.setProductIdList(productIdList);
        marketingTaskDto.setLogisticCode(logisticCode);
        rabbitMqSender.publishShopeeVHostRabbitTemplateSend(PublishRabbitMqExchange.SHOPEE_API_DIRECT_EXCHANGE, PublishQueues.SHOPEE_MARKETING_DISCOUNT_KEY, marketingTaskDto);
    }

}

package com.estone.erp.publish.shopee.model;

import java.io.Serializable;
import java.sql.Timestamp;
import lombok.Data;

@Data
public class ShopeeUploadInfo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     *  database column shopee_upload_info.id
     */
    private Integer id;

    /**
     *  database column shopee_upload_info.template
     */
    private String template;

    /**
     *  database column shopee_upload_info.skus
     */
    private String skus;

    /**
     *  database column shopee_upload_info.site
     */
    private String site;

    /**
     *  database column shopee_upload_info.account
     */
    private String account;

    /**
     *  database column shopee_upload_info.params
     */
    private String params;

    /**
     *  database column shopee_upload_info.result
     */
    private String result;

    /**
     *  database column shopee_upload_info.success
     */
    private Boolean success;

    /**
     *  database column shopee_upload_info.upload_user
     */
    private String uploadUser;

    /**
     *  database column shopee_upload_info.req_id
     */
    private String reqId;

    /**
     *  database column shopee_upload_info.create_time
     */
    private Timestamp createTime;
}
package com.estone.erp.publish.shopee.controller;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.estone.erp.common.model.api.ApiRequestParam;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.model.api.CQuery;
import com.estone.erp.common.model.api.CQueryResult;
import com.estone.erp.common.util.Asserts;
import com.estone.erp.common.util.POIUtils;
import com.estone.erp.publish.common.ErrorCode;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.shopee.model.*;
import com.estone.erp.publish.shopee.service.ShopeeAdminTemplateService;
import com.estone.erp.publish.shopee.service.ShopeeAutoPublishStatisticsService;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.estone.erp.publish.shopee.service.ShopeeSkuOrderService;
import com.estone.erp.publish.shopee.service.ShopeeTemplateService;
import com.estone.erp.publish.system.account.AccountUtils;
import com.estone.erp.publish.system.account.SaleAccountAndBusinessResponse;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.OutputStream;
import java.util.*;

/**
 * <AUTHOR> shopee_auto_publish_statistics
 * 2021-02-27 14:43:50
 */
@RestController
@RequestMapping("shopeeAutoPublishStatistics")
public class ShopeeAutoPublishStatisticsController {

    @Resource
    private ShopeeAutoPublishStatisticsService shopeeAutoPublishStatisticsService;

    @Resource
    private ShopeeAdminTemplateService shopeeAdminTemplateService;

    @Resource
    private ShopeeTemplateService shopeeTemplateService;

    @Resource
    private ShopeeSkuOrderService shopeeSkuOrderService;

    @PostMapping
    public ApiResult<?> postShopeeAutoPublishStatistics(@RequestBody(required = true) ApiRequestParam<String> requestParam, HttpServletResponse response) {
        String method = requestParam.getMethod();
        if (StringUtils.isNotBlank(method)) {
            switch (method) {
                case "searchShopeeAutoPublishStatistics": // 查询列表
                    CQuery<ShopeeAutoPublishStatisticsCriteria> cquery = requestParam.getArgsValue(new TypeReference<CQuery<ShopeeAutoPublishStatisticsCriteria>>() {});
                    Asserts.isTrue(cquery != null, ErrorCode.PARAM_EMPTY_ERROR);
                    CQueryResult<ShopeeAutoPublishStatistics> results = shopeeAutoPublishStatisticsService.search(cquery);
                    return results;
                case "addShopeeAutoPublishStatistics": // 添加
                    ShopeeAutoPublishStatistics shopeeAutoPublishStatistics = requestParam.getArgsValue(new TypeReference<ShopeeAutoPublishStatistics>() {});
                    shopeeAutoPublishStatisticsService.insert(shopeeAutoPublishStatistics);
                    return ApiResult.newSuccess(shopeeAutoPublishStatistics);
                case "getSkuAdminTemplate":     //获取spu的admin范本数据
                    String shopeeAdminTemplateIds = requestParam.getArgs();
                    List<Integer> shopeeAdminTemplateIdList = new ArrayList<>();
                    for (String idStr : shopeeAdminTemplateIds.split(",")) {
                        shopeeAdminTemplateIdList.add(Integer.valueOf(idStr));
                    }
                    ShopeeAdminTemplateExample shopeeAdminTemplateExample = new ShopeeAdminTemplateExample();
                    ShopeeAdminTemplateExample.Criteria shopeeAdminTemplateCriteria = shopeeAdminTemplateExample.createCriteria();
                    shopeeAdminTemplateCriteria.andIdIn(shopeeAdminTemplateIdList);
                    List<ShopeeAdminTemplate> shopeeAdminTemplateList = shopeeAdminTemplateService.selectByExample(shopeeAdminTemplateExample);
                    List<JSONObject> adminTemplateObjectList = new ArrayList<>();
                    for (ShopeeAdminTemplate shopeeAdminTemplate : shopeeAdminTemplateList) {
                        JSONObject adminTemplateObject = new JSONObject();
                        adminTemplateObject.put("id",shopeeAdminTemplate.getId());
                        //站点先写死"MY"
                        adminTemplateObject.put("site","MY");
                        adminTemplateObject.put("createDate",shopeeAdminTemplate.getCreateDate());
                        adminTemplateObjectList.add(adminTemplateObject);
                    }
                    return ApiResult.newSuccess(adminTemplateObjectList);
                case "getSkuTemplate":    //获取spu的模板数据，待刊登的数据不会出现在这里
                    JSONObject paramObject = requestParam.getArgsValue(new TypeReference<JSONObject>() {});
                    //1.全部   2.成功    3.失败    4.刊登中
                    Integer publishStatus = paramObject.getInteger("publishStatus");
                    String shopeeTemplateIds = paramObject.getString("shopeeTemplateIds");
                    List<Integer> shopeeTemplateIdList = new ArrayList<>();
                    for (String idStr : shopeeTemplateIds.split(",")) {
                        shopeeTemplateIdList.add(Integer.valueOf(idStr));
                    }
                    ShopeeTemplateExample shopeeTemplateExample = new ShopeeTemplateExample();
                    ShopeeTemplateExample.Criteria shopeeTemplateCriteria = shopeeTemplateExample.createCriteria();
                    shopeeTemplateCriteria.andIdIn(shopeeTemplateIdList);
                    switch(publishStatus){
                        case 1 :
                            break;
                        case 2 :
                            shopeeTemplateCriteria.andPublishStatusEqualTo(4);
                            break;
                        case 3 :
                            List<Integer> publishStatusList = new ArrayList<>();
                            publishStatusList.add(3);
                            publishStatusList.add(5);
                            shopeeTemplateCriteria.andPublishStatusIn(publishStatusList);
                            break;
                        case 4 :
                            shopeeTemplateCriteria.andPublishStatusEqualTo(2);
                            break;
                    }
                    List<ShopeeTemplate> shopeeTemplateList = shopeeTemplateService.selectByExample(shopeeTemplateExample);
                    List<JSONObject> templateObjectList = new ArrayList<>();
                    //List<JSONObject> allTemplateObjectList = new ArrayList<>();    //存放全部模板
                    //List<JSONObject> successTemplateObjectList = new ArrayList<>();    //存放成功模板
                    //List<JSONObject> failTemplateObjectList = new ArrayList<>();    //存放失败模板
                    //List<JSONObject> publishingTemplateObjectList = new ArrayList<>();    //存放刊登中模板
                    int successNum = 0;        //刊登成功数量
                    for (ShopeeTemplate shopeeTemplate : shopeeTemplateList) {
                        if(shopeeTemplate.getPublishStatus().equals(4)) {
                            successNum ++;
                        }
                        JSONObject templateObject = new JSONObject();
                        templateObject.put("id",shopeeTemplate.getId());
                        templateObject.put("sku",shopeeTemplate.getSku());
                        templateObject.put("createBy",shopeeTemplate.getCreatedBy());
                        templateObject.put("createDate",shopeeTemplate.getCreateDate());
                        templateObject.put("lastUpdateDate",shopeeTemplate.getLastUpdateDate());
                        switch(shopeeTemplate.getPublishStatus()){
                            case 2 :
                                templateObject.put("publishStatus",4);
                                break;
                            case 3 :
                                templateObject.put("publishStatus",3);
                                break;
                            case 4 :
                                templateObject.put("publishStatus",2);
                                break;
                            case 5 :
                                templateObject.put("publishStatus",3);
                                break;
                        }
                        templateObjectList.add(templateObject);
                        List<String> accountList = JSONArray.parseArray(shopeeTemplate.getAccountsStr(), String.class);
                        String accountNumber = "";
                        for (String account : accountList) {
                            accountNumber = accountNumber + account + ",";
                            /*SaleAccountAndBusinessResponse shopeeAccount = AccountUtils.getSaleAccountByAccountNumber(SaleChannel.CHANNEL_SHOPEE, account, false);
                            JSONObject templateObject = new JSONObject();
                            templateObject.put("id",shopeeTemplate.getId());
                            templateObject.put("account",account);
                            templateObject.put("sku",shopeeTemplate.getSku());
                            templateObject.put("createBy",shopeeTemplate.getCreatedBy());
                            templateObject.put("createDate",shopeeTemplate.getCreateDate());
                            templateObject.put("lastUpdateDate",shopeeTemplate.getLastUpdateDate());
                            allTemplateObjectList.add(templateObject);
                            Integer templatePublishStatus = shopeeTemplate.getPublishStatus();
                            //刊登状态，1.待刊登 2.刊登中 3.部分成功 4.全部成功 5.全部失败，待刊登的数据不会出现在这里
                            switch(templatePublishStatus){
                                case 2 :
                                    templateObject.put("publishStatus",4);
                                    publishingTemplateObjectList.add(templateObject);
                                    break;
                                case 3 :
                                    boolean isPublishSuccess = shopeeTemplateService.checkSiteHasPublish(shopeeAccount.getAccountSite(), shopeeTemplate);
                                    if(isPublishSuccess) {
                                        templateObject.put("publishStatus",2);
                                        successNum ++;
                                        successTemplateObjectList.add(templateObject);
                                    } else {
                                        templateObject.put("publishStatus",3);
                                        failTemplateObjectList.add(templateObject);
                                    }
                                    break;
                                case 4 :
                                    templateObject.put("publishStatus",2);
                                    successNum ++;
                                    successTemplateObjectList.add(templateObject);
                                    break;
                                case 5 :
                                    templateObject.put("publishStatus",3);
                                    failTemplateObjectList.add(templateObject);
                                    break;
                            }*/
                        }
                        templateObject.put("account",accountNumber.substring(0,accountNumber.length()-1));
                    }
                    JSONObject statDataObject = new JSONObject();
                    //statDataObject.put("templateNum",allTemplateObjectList.size());
                    //statDataObject.put("templateNum",templateObjectList.size());
                    //statDataObject.put("successNum",successNum);
                    statDataObject.put("successNum",templateObjectList.size());
                    //1.全部   2.成功    3.失败    4.刊登中
                    /*switch(publishStatus){
                        case 1 :
                            statDataObject.put("templateList",allTemplateObjectList);
                            break;
                        case 2 :
                            statDataObject.put("templateList",successTemplateObjectList);
                            break;
                        case 3 :
                            statDataObject.put("templateList",failTemplateObjectList);
                            break;
                        case 4 :
                            statDataObject.put("templateList",publishingTemplateObjectList);
                            break;
                    }*/
                    statDataObject.put("templateList",templateObjectList);
                    return ApiResult.newSuccess(statDataObject);
                case "getOrderListBySku":
                    String sku = requestParam.getArgs();
                    ShopeeSkuOrderExample shopeeSkuOrderExample = new ShopeeSkuOrderExample();
                    ShopeeSkuOrderExample.Criteria shopeeSkuOrderCriteria = shopeeSkuOrderExample.createCriteria();
                    shopeeSkuOrderCriteria.andMainSkuEqualTo(sku);
                    List<ShopeeSkuOrder> shopeeSkuOrderList = shopeeSkuOrderService.selectByExample(shopeeSkuOrderExample);
                    return ApiResult.newSuccess(shopeeSkuOrderList);
                case "exportStatisticsData":
                    CQuery<ShopeeAutoPublishStatisticsCriteria> exportCquery = requestParam.getArgsValue(new TypeReference<CQuery<ShopeeAutoPublishStatisticsCriteria>>() {});
                    List<ShopeeAutoPublishStatistics> exportStatisticsDataList = shopeeAutoPublishStatisticsService.selectByExample(exportCquery.getSearch().getExample());
                    OutputStream os = null;
                    try{
                        String fileName = "shopeeAutoPublishData-" + POIUtils.PATH_DATE_FORMAT.format(new Date()) + ".xlsx";
                        response.setHeader("Content-Disposition", "attachment;filename=" + java.net.URLEncoder.encode(fileName, "UTF-8"));
                        os = response.getOutputStream();
                        if(CollectionUtils.isNotEmpty(exportStatisticsDataList)) {
                            final String[] headers = { "id", "图片", "SPU", "标题", "分类","自动刊登范本数量","关联模板数量","刊登成功模板数量","刊登成功率","出单数量","出单转化率"};
                            final List<List<String>> awLists = new ArrayList<>();
                            POIUtils.createExcel(headers, exportStatisticsDataList, shopeeStatisticsData -> {
                                awLists.clear();
                                List<String> awList = new ArrayList<>(headers.length);
                                awList.add(POIUtils.transferObj2Str(shopeeStatisticsData.getId()));
                                awList.add(POIUtils.transferObj2Str(shopeeStatisticsData.getMainImage()));
                                awList.add(POIUtils.transferObj2Str(shopeeStatisticsData.getSpu()));
                                awList.add(POIUtils.transferObj2Str(shopeeStatisticsData.getTitle()));
                                awList.add(POIUtils.transferObj2Str(shopeeStatisticsData.getSysCategoryName()));
                                awList.add(POIUtils.transferObj2Str(shopeeStatisticsData.getAdminTemplateNum()));
                                awList.add(POIUtils.transferObj2Str(shopeeStatisticsData.getAssociateTemplateNum()));
                                awList.add(POIUtils.transferObj2Str(shopeeStatisticsData.getSuccessNum()));
                                awList.add(POIUtils.transferObj2Str(shopeeStatisticsData.getSuccessRate()));
                                awList.add(POIUtils.transferObj2Str(shopeeStatisticsData.getOrderNum()));
                                awList.add(POIUtils.transferObj2Str(shopeeStatisticsData.getOrderRate()));
                                awLists.add(awList);
                                return awLists;
                            }, true, os);
                        }
                    }catch(Exception e){
                        e.printStackTrace();
                    }finally {
                        IOUtils.closeQuietly(os);
                    }
                    return ApiResult.newSuccess();
            }
        }
        return ApiResult.newSuccess();
    }

    @GetMapping(value = "/{id}")
    public ApiResult<?> getShopeeAutoPublishStatistics(@PathVariable(value = "id", required = true) Integer id) {
        ShopeeAutoPublishStatistics shopeeAutoPublishStatistics = shopeeAutoPublishStatisticsService.selectByPrimaryKey(id);
        return ApiResult.newSuccess(shopeeAutoPublishStatistics);
    }

    @PutMapping(value = "/{id}")
    public ApiResult<?> putShopeeAutoPublishStatistics(@PathVariable(value = "id", required = true) Integer id, @RequestBody ApiRequestParam<String> requestParam) {
        String method = requestParam.getMethod();
        if (StringUtils.isNotBlank(method)) {
            switch (method) {
                case "updateShopeeAutoPublishStatistics": // 单个修改
                    ShopeeAutoPublishStatistics shopeeAutoPublishStatistics = requestParam.getArgsValue(new TypeReference<ShopeeAutoPublishStatistics>() {});
                    shopeeAutoPublishStatisticsService.updateByPrimaryKeySelective(shopeeAutoPublishStatistics);
                    return ApiResult.newSuccess(shopeeAutoPublishStatistics);
                }
        }
        return ApiResult.newSuccess();
    }
}
package com.estone.erp.publish.shopee.call.v2;

import com.estone.erp.publish.shopee.api.v2.common.ShopeeResponse;
import com.estone.erp.publish.shopee.api.v2.param.sizeChart.GetSizeChartDetailV2;
import com.estone.erp.publish.shopee.api.v2.param.sizeChart.GetSizeChartListParamV2;
import com.estone.erp.publish.shopee.api.v2.param.sizeChart.SupportSizeChartV2;
import com.estone.erp.publish.shopee.util.ShopeeHttpUtils;
import com.estone.erp.publish.system.account.SaleAccountAndBusinessResponse;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> @date 2024/10/15 16:00
 * @description 尺码表相关接口调用
 */
@Slf4j
public class ShopeeSizeChartCallV2 {

    /**
     * 获取尺码表列表
     *
     * @param saleAccountAndBusinessResponse
     * @param categoryId
     * @return
     */
    public static ShopeeResponse getSizeChartList(SaleAccountAndBusinessResponse saleAccountAndBusinessResponse, Integer categoryId, String cursor, Integer pageSize) {
        GetSizeChartListParamV2 paramV2 = new GetSizeChartListParamV2();
        paramV2.setCategoryId(categoryId);
        paramV2.setPageSize(pageSize);
        paramV2.setCursor(cursor);
        return ShopeeHttpUtils.doGetV2(saleAccountAndBusinessResponse, paramV2);
    }

    /**
     * 获取尺码表详情
     *
     * @param saleAccountAndBusinessResponse
     * @param sizeChartId
     * @return
     */
    public static ShopeeResponse getSizeChartDetail(SaleAccountAndBusinessResponse saleAccountAndBusinessResponse, Integer sizeChartId) {
        GetSizeChartDetailV2 paramV2 = new GetSizeChartDetailV2();
        paramV2.setSizeChartId(sizeChartId);
        return ShopeeHttpUtils.doGetV2(saleAccountAndBusinessResponse, paramV2);
    }


    /**
     * 获取类别支持大小图表
     * 只针对中国大陆卖家和韩国卖家。
     */
    public static ShopeeResponse supportSizeChart(SaleAccountAndBusinessResponse saleAccountAndBusinessResponse, Integer categoryId) {
        SupportSizeChartV2 paramV2 = new SupportSizeChartV2();
        paramV2.setCategoryId(categoryId);
        return ShopeeHttpUtils.doGetV2(saleAccountAndBusinessResponse, paramV2);
    }


}

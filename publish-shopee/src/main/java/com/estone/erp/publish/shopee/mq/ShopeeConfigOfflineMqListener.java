package com.estone.erp.publish.shopee.mq;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.estone.erp.common.elasticsearch.util.ElasticSearchHelper;
import com.estone.erp.common.util.*;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.common.component.StatusCode;
import com.estone.erp.publish.common.enums.FeedTaskResultStatusEnum;
import com.estone.erp.publish.common.enums.FeedTaskStatusEnum;
import com.estone.erp.publish.common.executors.ShopeeExecutors;
import com.estone.erp.publish.common.json.ResponseError;
import com.estone.erp.publish.common.json.ResponseJson;
import com.estone.erp.publish.elasticsearch.util.CheckOrderSalesTimeUtils;
import com.estone.erp.publish.elasticsearch2.model.EsShopeeItem;
import com.estone.erp.publish.elasticsearch2.service.EsShopeeItemService;
import com.estone.erp.publish.platform.enums.SingleItemEnum;
import com.estone.erp.publish.shopee.api.v2.common.ShopeeResponse;
import com.estone.erp.publish.shopee.call.v2.ShopeeDeleteItemCallV2;
import com.estone.erp.publish.shopee.call.v2.ShopeeUnlistItemCallV2;
import com.estone.erp.publish.shopee.dto.ShopeeListingOfflineConfigDTO;
import com.estone.erp.publish.shopee.dto.ShoppeListingStatusConfigInnerParamDTO;
import com.estone.erp.publish.shopee.enums.*;
import com.estone.erp.publish.shopee.model.ShopeeConfigTask;
import com.estone.erp.publish.shopee.model.ShopeeListingStatusConfig;
import com.estone.erp.publish.shopee.service.ShopeeConfigTaskService;
import com.estone.erp.publish.shopee.service.ShopeeItemOfflineEsService;
import com.estone.erp.publish.shopee.util.ShopeeFeedTaskHandleUtil;
import com.estone.erp.publish.shopee.util.ShopeeItemUtils;
import com.estone.erp.publish.system.account.AccountUtils;
import com.estone.erp.publish.system.account.SaleAccountAndBusinessResponse;
import com.estone.erp.publish.system.feedTask.feedTaskEnum.ResultStatusEnum;
import com.estone.erp.publish.system.feedTask.model.FeedTask;
import com.estone.erp.publish.system.product.bean.forbidden.SalesProhibitionsVo;
import com.estone.erp.publish.system.product.bean.forbidden.Sites;
import com.estone.erp.publish.system.product.esProduct.bean.SingleItemEs;
import com.estone.erp.publish.system.product.esProduct.bean.SingleItemEsRequest;
import com.estone.erp.publish.system.product.esProduct.service.SingleItemEsService;
import com.estone.erp.publish.tidb.publishtidb.model.ShopeeOfflineRecord;
import com.estone.erp.publish.tidb.publishtidb.service.IShopeeOfflineRecordService;
import com.rabbitmq.client.Channel;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.listener.api.ChannelAwareMessageListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.elasticsearch.core.ElasticsearchRestTemplate;
import org.springframework.data.elasticsearch.core.mapping.IndexCoordinates;
import org.springframework.data.elasticsearch.core.query.FetchSourceFilter;
import org.springframework.data.elasticsearch.core.query.NativeSearchQuery;
import org.springframework.data.elasticsearch.core.query.NativeSearchQueryBuilder;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.sql.Timestamp;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Description:
 * <AUTHOR>
 * @Date 2024/9/19 下午5:22
 */
@Slf4j
public class ShopeeConfigOfflineMqListener implements ChannelAwareMessageListener {

    private final IndexCoordinates INDEXCOORDINATES = IndexCoordinates.of("shopee_item");

    private final String KEY = "SHOPEE-UNLIST-ITEM-CURRENT-LIMITER-KEY";

    @Resource
    private ShopeeItemOfflineEsService shopeeItemOfflineEsService;

    @Autowired
    private ElasticsearchRestTemplate elasticsearchRestTemplate2;

    @Autowired
    private IShopeeOfflineRecordService shopeeOfflineRecordService;

    @Resource
    private EsShopeeItemService esShopeeItemService;

    @Resource
    private SingleItemEsService singleItemEsService;

    @Resource
    private ShopeeConfigTaskService shopeeConfigTaskService;

    @Override
    public void onMessage(Message message, Channel channel) throws Exception {
        String body = new String(message.getBody(), StandardCharsets.UTF_8);
        ShoppeListingStatusConfigInnerParamDTO innerParamDTO = JSON.parseObject(body, new TypeReference<ShoppeListingStatusConfigInnerParamDTO>() {
        });
        ShopeeListingStatusConfig shopeeListingStatusConfig = innerParamDTO.getStatusConfig();

        try {
            // JOSN转换读取配置项目
            ShopeeListingOfflineConfigDTO.RuleConfigJson ruleConfigJson = ShopeeListingOfflineConfigDTO.reconvert(shopeeListingStatusConfig).getRuleConfigJson();

            // 店铺维度下架
            List<String> accounts = Arrays.stream(StrUtil.strDeldComma(shopeeListingStatusConfig.getAccounts()).split(",")).collect(Collectors.toList());
            for (String account : accounts) {
                ShopeeExecutors.executeListingItem(() -> {
                    // 保存任务记录
                    ShopeeConfigTask shopeeConfigTask = this.saveShopeeConfigTask(account, shopeeListingStatusConfig);

                    // 下架店铺商品数据
                    this.offlineProduct(account, innerParamDTO, shopeeListingStatusConfig, ruleConfigJson, accounts);

                    shopeeConfigTaskService.successTask(shopeeConfigTask.getId(), "执行成功");
                });
            }
            channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
        } catch (Exception e) {
            channel.basicNack(message.getMessageProperties().getDeliveryTag(), false, false);
        }
    }

    private void offlineProduct(String account, ShoppeListingStatusConfigInnerParamDTO innerParamDTO, ShopeeListingStatusConfig shopeeListingStatusConfig, ShopeeListingOfflineConfigDTO.RuleConfigJson ruleConfigJson, List<String> accounts) {
        // 获取店铺下架商品
        Map<String, List<EsShopeeItem>> esShopeeProductItemListMap = new HashMap<>();
        int pageIndex = 0;
        int pageSize = 200;
        int total = 0;
        while (true) {
            try {
                // 分页获取父体数据
                BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
                if (CollectionUtils.isNotEmpty(innerParamDTO.getProductIdList())) {
                    boolQueryBuilder.must(QueryBuilders.termsQuery("itemId", innerParamDTO.getProductIdList()));
                }
                boolQueryBuilder.must(QueryBuilders.termQuery("itemSeller", account));
                boolQueryBuilder.must(QueryBuilders.termQuery("isFather", true));
                NativeSearchQueryBuilder queryBuilder = new NativeSearchQueryBuilder();
                NativeSearchQuery build = queryBuilder.withQuery(boolQueryBuilder).withSourceFilter(new FetchSourceFilter(new String[]{"itemId"}, null)).build();
                build.setPageable(PageRequest.of(pageIndex++, pageSize));

                List<EsShopeeItem> esShopeeProductItemList = ElasticSearchHelper.queryList(elasticsearchRestTemplate2, build, EsShopeeItem.class);
                if (CollectionUtils.isEmpty(esShopeeProductItemList) || total >= shopeeListingStatusConfig.getMaxOfflineNumber()) {
                    break;
                }

                for (EsShopeeItem esShopeeProductItem : esShopeeProductItemList) {
                    // 判断是否已下架
                    if (esShopeeProductItemListMap.containsKey(esShopeeProductItem.getItemId())) {
                        continue;
                    }

                    // 判断是否超过最大数据或者已无更多数据
                    if (total >= shopeeListingStatusConfig.getMaxOfflineNumber()) {
                        break;
                    }

                    // 根据itemId查询sku维度数据
                    NativeSearchQueryBuilder searchQueryBuilder = new NativeSearchQueryBuilder();
                    BoolQueryBuilder newBoolQueryBuilder = getBoolQueryBuilder(account, esShopeeProductItem.getItemId(), ruleConfigJson);
                    NativeSearchQuery nativeSearchQuery = searchQueryBuilder.withQuery(newBoolQueryBuilder)
                            .withSourceFilter(new FetchSourceFilter(null, new String[]{"logistics", "description", "variationOptions"}))
                            .build();
                    List<EsShopeeItem> esShopeeItemList = ElasticSearchHelper.queryList(elasticsearchRestTemplate2, nativeSearchQuery, EsShopeeItem.class);
                    if (CollectionUtils.isEmpty(esShopeeItemList)) {
                        continue;
                    }

                    // 查询产品系统基础信息
                    List<String> skuList = esShopeeItemList.stream().map(EsShopeeItem::getArticleNumber).collect(Collectors.toList());
                    SingleItemEsRequest singleItemEsRequest = new SingleItemEsRequest();
                    singleItemEsRequest.setSkuList(skuList);
                    List<SingleItemEs> singleItemEsList = singleItemEsService.getSingleItemEsList(singleItemEsRequest);
                    if (CollectionUtils.isEmpty(singleItemEsList)) {
                        continue;
                    }

                    Map<String, SingleItemEs> sonSkuMap = singleItemEsList.stream().filter(t -> StringUtils.isNotBlank(t.getSonSku())).collect(Collectors.toMap(SingleItemEs::getSonSku, v -> v, (k1, k2) -> k1));

                    // 按照itemId分组方便数据聚合匹配
                    Map<String, List<EsShopeeItem>> esShopeeItemMap = esShopeeItemList.stream().collect(Collectors.groupingBy(EsShopeeItem::getItemId));

                    // 匹配条件数据集
                    List<EsShopeeItem> shopeeItemList = filterEsShopeeItems(shopeeListingStatusConfig, ruleConfigJson, esShopeeItemMap, sonSkuMap);
                    if (CollectionUtils.isEmpty(shopeeItemList)) {
                        continue;
                    }

                    // 维护总数
                    total++;

                    esShopeeProductItemListMap.put(esShopeeProductItem.getItemId(), shopeeItemList);
                }
            } catch (Exception e) {
                log.error("同步定时任务异常,店铺账号：{},原因：{}", accounts, e);
            }
        }

        // 判断执行方式
        if (ShopeeOfflineMethodEnum.DELETE.getCode().equals(ruleConfigJson.getOfflineMethod())) {
            this.executeDeleteAndRecordOffline(shopeeListingStatusConfig, account, esShopeeProductItemListMap.values());
            XxlJobLogger.log("执行完毕{}配置,当前店铺为：{}，,共删除{}条数据", shopeeListingStatusConfig.getName(), account, total);
        } else {
            // 执行下级并记录下架记录
            this.executeOfflineAndRecordOffline(shopeeListingStatusConfig, account, esShopeeProductItemListMap);
            XxlJobLogger.log("执行完毕{}配置,当前店铺为：{}，,共下架{}条数据", shopeeListingStatusConfig.getName(), account, total);
        }
    }

    /**
     * 过滤掉不符合条件的数据
     *
     * @param shopeeListingStatusConfig
     * @param ruleConfigJson
     * @param esShopeeItemMap
     * @param sonSkuMap
     * @return
     */
    private static List<EsShopeeItem> filterEsShopeeItems(ShopeeListingStatusConfig shopeeListingStatusConfig, ShopeeListingOfflineConfigDTO.RuleConfigJson ruleConfigJson, Map<String, List<EsShopeeItem>> esShopeeItemMap, Map<String, SingleItemEs> sonSkuMap) {
        List<EsShopeeItem> shopeeItemList = esShopeeItemMap.values().stream()
                .filter(esShopeeItems -> {
                    // 过滤销量
                    Integer sum = getItemSalesTimePeriod(esShopeeItems, ruleConfigJson);
                    if (Objects.isNull(sum)) {
                        return false;
                    }
                    return ruleConfigJson.getSalesRange().get(0) <= sum && sum < ruleConfigJson.getSalesRange().get(1);
                })
                .filter(esShopeeItems -> {
                    // items中需全部满足
                    boolean match = esShopeeItems.stream().allMatch(esShopeeItem -> {
                        SingleItemEs singleItemEs = sonSkuMap.get(esShopeeItem.getArticleNumber());
                        if (Objects.isNull(singleItemEs)) {
                            return false;
                        }

                        // 获取指定日期到当前时间的天数
                        if (Objects.nonNull(ruleConfigJson.getOnlineShelfDuration())) {
                            LocalDateTime uploadDate = LocalDateTime.ofInstant(esShopeeItem.getUploadDate().toInstant(), ZoneId.systemDefault());
                            long days = ChronoUnit.DAYS.between(uploadDate.toLocalDate(), LocalDate.now());
                            if (ruleConfigJson.getOnlineShelfDuration() >= days) {
                                return false;
                            }
                        }

                        // 过滤产品标签
                        if (CollectionUtils.isNotEmpty(ruleConfigJson.getProductTags())) {
                            String tagCode = singleItemEs.getTagCode();
                            List<String> tagCodeList = CommonUtils.splitList(tagCode, ",");
                            List<String> codes = tagCodeList.stream().filter(code -> ruleConfigJson.getProductTags().contains(code)).collect(Collectors.toList());
                            if (CollectionUtils.isEmpty(codes)) {
                                return false;
                            }
                        }

                        // 过滤单品状态
                        if (CollectionUtils.isNotEmpty(ruleConfigJson.getItemStatus())) {
                            String enNameByCode = SingleItemEnum.getEnNameByCode(singleItemEs.getItemStatus());
                            if (StringUtils.isBlank(enNameByCode)) {
                                return false;
                            }
                            if (!ruleConfigJson.getItemStatus().contains(enNameByCode)) {
                                return false;
                            }
                        }

                        // 过滤禁售信息
                        List<ShopeeListingOfflineConfigDTO.RuleConfigJson.ProhibitedInfo> prohibitedInfoList = ruleConfigJson.getProhibitedInfo().stream()
                                .filter(prohibitedInfo -> StringUtils.isNotBlank(prohibitedInfo.getPlatform())).collect(Collectors.toList());
                        if (CollectionUtils.isNotEmpty(prohibitedInfoList)) {
                            String salesProhibition = singleItemEs.getSalesProhibition();
                            if (StringUtils.isNotBlank(salesProhibition) && !salesProhibition.equals("[]")) {
                                // 产品系统禁售平台信息
                                List<SalesProhibitionsVo> prohibitionsVoList = JSON.parseObject(salesProhibition, new TypeReference<List<SalesProhibitionsVo>>() {
                                });

                                // 转换成Map
                                Map<String, List<Sites>> prohibitionsVoMap = prohibitionsVoList.stream().collect(Collectors.toMap(SalesProhibitionsVo::getPlat, SalesProhibitionsVo::getSites));

                                boolean anyMatch = prohibitedInfoList.stream().anyMatch(prohibitedInfo -> {
                                    // 判断禁售平台是否存在
                                    List<Sites> sitesList = prohibitionsVoMap.get(prohibitedInfo.getPlatform());
                                    if (CollectionUtils.isEmpty(sitesList)) {
                                        return false;
                                    }

                                    // 兼容产品系统站点空数据
                                    List<Sites> newSitesList = sitesList.stream().filter(sites -> StringUtils.isNotBlank(sites.getSite())).collect(Collectors.toList());
                                    if (CollectionUtils.isEmpty(newSitesList)) {
                                        return false;
                                    }

                                    // 判断禁售站点
                                    if (CollectionUtils.isNotEmpty(prohibitedInfo.getSiteList())) {
                                        boolean anied = prohibitedInfo.getSiteList().stream().anyMatch(site -> {
                                            List<String> sites = newSitesList.stream().map(Sites::getSite).collect(Collectors.toList());
                                            return sites.contains(site);
                                        });
                                        return anied;
                                    }

                                    return true;
                                });

                                if (!anyMatch) {
                                    return false;
                                }
                            } else {
                                return false;
                            }
                        }

                        // 校验总销量写入时间<Shopee_accountNumber_sellerSku_productId>
                        String id = SaleChannel.CHANNEL_SHOPEE + "_" + esShopeeItem.getItemSeller() + "_" + esShopeeItem.getItemSku() + "_" + esShopeeItem.getItemId();
                        return CheckOrderSalesTimeUtils.checkSaleTotalCountWriteTime(id);
                    });

                    return match;
                })
                .flatMap(esShopeeItems -> esShopeeItems.stream()).map(esShopeeItem -> {
                    // 设置下架原因
                    esShopeeItem.setLastUpdatedBy("admin");
                    esShopeeItem.setLastUpdateDate(new Date());
                    esShopeeItem.setDownRemark(String.format("由【%s】触下架", shopeeListingStatusConfig.getName()));
                    return esShopeeItem;
                })
                .collect(Collectors.toList());
        return shopeeItemList;
    }

    /**
     * 保存任务
     *
     * @param account
     * @param shopeeListingStatusConfig
     */
    private ShopeeConfigTask saveShopeeConfigTask(String account, ShopeeListingStatusConfig shopeeListingStatusConfig) {
        ShopeeConfigTask configTask = new ShopeeConfigTask();
        configTask.setConfigType(ShopeeConfigTypeEnum.PRODUCT_OFFLINE.getCode());
        configTask.setConfigId(shopeeListingStatusConfig.getId());
        configTask.setConfigName(shopeeListingStatusConfig.getName());
        configTask.setConfigRuleJson(shopeeListingStatusConfig.getRuleConfigJson());
        configTask.setAccountNumber(account);
        configTask.setOperatorStatus(ShopeeConfigOperatorStatusEnum.WAITING.getCode());
        configTask.setOperatorTime(new Timestamp(System.currentTimeMillis()));
        configTask.setCreatedTime(new Timestamp(System.currentTimeMillis()));
        configTask.setUpdatedTime(new Timestamp(System.currentTimeMillis()));
        shopeeConfigTaskService.insert(configTask);

        return configTask;
    }

    /**
     * 获取销售量
     *
     * @param esShopeeItems
     * @param ruleConfigJson
     * @return
     */
    private static Integer getItemSalesTimePeriod(List<EsShopeeItem> esShopeeItems, ShopeeListingOfflineConfigDTO.RuleConfigJson ruleConfigJson) {
        Integer sum = null;
        switch (ruleConfigJson.getItemSalesTimePeriod()) {
            case 0:
                // 总出单,允许等于0
                sum = esShopeeItems.stream().filter(item -> Objects.nonNull(item.getOrderNumTotal())).mapToInt(EsShopeeItem::getOrderNumTotal).sum();
                break;
            case 1:
                // 24小时出单
                sum = esShopeeItems.stream().filter(item -> Objects.nonNull(item.getOrder24HCount())).mapToInt(EsShopeeItem::getOrder24HCount).sum();
                break;
            case 2:
                // 7天出单
                sum = esShopeeItems.stream().filter(item -> Objects.nonNull(item.getOrderLast7dCount())).mapToInt(EsShopeeItem::getOrderLast7dCount).sum();
                break;
            case 3:
                // 14天出单
                sum = esShopeeItems.stream().filter(item -> Objects.nonNull(item.getOrderLast14dCount())).mapToInt(EsShopeeItem::getOrderLast14dCount).sum();
                break;
            case 4:
                // 30天出单
                sum = esShopeeItems.stream().filter(item -> Objects.nonNull(item.getOrderLast30dCount())).mapToInt(EsShopeeItem::getOrderLast30dCount).sum();
                break;
            case 5:
                // 60天出单
                sum = esShopeeItems.stream().filter(item -> Objects.nonNull(item.getOrderLast60dCount())).mapToInt(EsShopeeItem::getOrderLast60dCount).sum();
                break;
            case 6:
                // 90天出单
                sum = esShopeeItems.stream().filter(item -> Objects.nonNull(item.getOrderLast90dCount())).mapToInt(EsShopeeItem::getOrderLast90dCount).sum();
                break;
            default:
                break;
        }
        return sum;
    }

    /**
     * 执行下级并记录下架记录
     *
     * @param shopeeListingStatusConfig
     * @param account
     * @param esShopeeProductItemListMap
     */
    private void executeOfflineAndRecordOffline(ShopeeListingStatusConfig shopeeListingStatusConfig, String account, Map<String, List<EsShopeeItem>> esShopeeProductItemListMap) {
        // 获取账号信息并执行下架
        SaleAccountAndBusinessResponse saleAccountByAccountNumber = AccountUtils.getSaleAccountByAccountNumber(SaleChannel.CHANNEL_SHOPEE, account, true);

        // 根据key分区
        List<List<String>> partitionList = esShopeeProductItemListMap.keySet()
                .stream()
                .collect(Collectors.collectingAndThen(Collectors.toList(), keys -> PagingUtils.pagingList(keys, 50)));

        // 执行下架
        partitionList.forEach(itemIdList -> {
            // 接口限流处理
            Double limitValue;
            String time = PublishRedisClusterUtils.get(KEY);
            if (StringUtils.isEmpty(time)) {
                limitValue = 10d;
                PublishRedisClusterUtils.set(KEY, String.valueOf(limitValue));
            } else {
                limitValue = Double.parseDouble(time);
            }
            DynamicLimiter.getInstance(String.format("SHOPEE-UNLIST-ITEM-%s", Optional.ofNullable(saleAccountByAccountNumber.getColStr1()).orElse("empty")), limitValue).acquire();

            // 按照associationId转成map
            Map<String, FeedTask> feedTaskMap = itemIdList.stream()
                    .map(itemId -> {
                        List<EsShopeeItem> esShopeeItems = esShopeeProductItemListMap.get(itemId);
                        if (CollectionUtils.isEmpty(esShopeeItems)) {
                            return null;
                        }
                        // 创建处理报告信息
                        return ShopeeFeedTaskHandleUtil.initFeedTask((feedTasktemp) -> {
                            feedTasktemp.setAssociationId(itemId);
                            feedTasktemp.setArticleNumber(esShopeeItems.get(0).getSpu());
                            feedTasktemp.setTaskType(ShopeeFeedTaskEnum.END_ITEM.getValue());
                            feedTasktemp.setAccountNumber(account);
                            feedTasktemp.setTaskStatus(FeedTaskStatusEnum.RUNNING.getTaskStatus());
                            feedTasktemp.setAttribute1(shopeeListingStatusConfig.getName());
                            feedTasktemp.setResultMsg(String.format("由【%s】触发下架", shopeeListingStatusConfig.getName()));
                            feedTasktemp.setCreatedBy("admin");
                        });
                    })
                    .filter(Objects::nonNull)
                    .collect(Collectors.toMap(FeedTask::getAssociationId, Function.identity()));
            try {
                List<EsShopeeItem> esShopeeItemList = itemIdList.stream().map(esShopeeProductItemListMap::get).filter(CollectionUtils::isNotEmpty).flatMap(List::stream).collect(Collectors.toList());
                ResponseJson responseJson = ShopeeUnlistItemCallV2.unlistItem(saleAccountByAccountNumber, true, esShopeeItemList);

                // 判定是否成功
                Map<String, List<ResponseError>> responseErrorMap = responseJson.getErrors().stream().collect(Collectors.groupingBy(ResponseError::getStatus));
                if (responseErrorMap.containsKey(StatusCode.FAIL)) {
                    List<ResponseError> failList = responseErrorMap.get(StatusCode.FAIL);
                    for (ResponseError responseError : failList) {
                        // 下架记录
                        List<EsShopeeItem> removeList = esShopeeItemList.stream().filter(item -> item.getItemId().equals(responseError.getField())).collect(Collectors.toList());
                        List<ShopeeOfflineRecord> shopeeOfflineRecordList = getShopeeOfflineRecords(shopeeListingStatusConfig, removeList, ShopeeStatusEnum.FAILURE.getCode(), responseError.getMessage());
                        shopeeOfflineRecordService.saveBatch(shopeeOfflineRecordList);

                        // 更新处理报告
                        ShopeeFeedTaskHandleUtil.updateFeedTaskToFinish(feedTaskMap.get(responseError.getField()), ResultStatusEnum.RESULT_FAIL.getStatusCode(), responseError.getMessage());
                    }
                }

                if (responseErrorMap.containsKey(StatusCode.SUCCESS)) {
                    List<ResponseError> successList = responseErrorMap.get(StatusCode.SUCCESS);
                    for (ResponseError responseError : successList) {
                        List<EsShopeeItem> saveList = esShopeeItemList.stream()
                                .filter(item -> item.getItemId().equals(responseError.getField()))
                                .peek(item -> {
                                    item.setDownType("SALES_OFFLINE");
                                    item.setItemStatus(ShopeeItemStatusEnum.UNLIST.getCode());
                                })
                                .collect(Collectors.toList());

                        // 更新至下架listing
                        esShopeeItemService.saveAll(saveList);

                        // 下架记录
                        List<ShopeeOfflineRecord> shopeeOfflineRecordList = getShopeeOfflineRecords(shopeeListingStatusConfig, saveList, ShopeeStatusEnum.SUCCESS.getCode(), null);
                        shopeeOfflineRecordService.saveBatch(shopeeOfflineRecordList);

                        ShopeeFeedTaskHandleUtil.updateFeedTaskToFinish(feedTaskMap.get(responseError.getField()), ResultStatusEnum.RESULT_SUCCESS.getStatusCode(), "");
                    }
                }
            } catch (Exception e) {
                for (FeedTask feedTask : feedTaskMap.values()) {
                    ShopeeFeedTaskHandleUtil.updateFeedTaskToFinish(feedTask, ResultStatusEnum.RESULT_FAIL.getStatusCode(), e.getMessage());
                }
            }
        });

    }

    /**
     * 执行删除并记录下架记录
     * 1-对应shopee平台站点禁售
     * 2-刊登时间大于25天且销量=0
     * 3-系统状态为停产或存档的单属性spu，所有子sku都为停产或存档的多属性spu
     *
     * @param shopeeListingStatusConfig
     * @param account
     * @param shopeeItemList
     */
    private void executeDeleteAndRecordOffline(ShopeeListingStatusConfig shopeeListingStatusConfig, String account, Collection<List<EsShopeeItem>> shopeeItemList) {
        shopeeItemList.forEach(esShopeeItemList -> {
            // item维度下架数据
            ShopeeExecutors.executeOfflineItem(() -> {
                Set<String> deleteItems = ShopeeItemUtils.filterDeleteItem(esShopeeItemList);
                if (CollectionUtils.isEmpty(deleteItems)) {
                    return;
                }

                EsShopeeItem esShopeeItem = esShopeeItemList.get(0);
                // 创建处理报告信息
                FeedTask feedTask = ShopeeFeedTaskHandleUtil.initFeedTask((feedTasktemp) -> {
                    feedTasktemp.setAssociationId(esShopeeItem.getItemId());
                    feedTasktemp.setArticleNumber(esShopeeItem.getSpu());
                    feedTasktemp.setTaskType(ShopeeFeedTaskEnum.DELETE_ITEM.getValue());
                    feedTasktemp.setAccountNumber(account);
                    feedTasktemp.setTaskStatus(FeedTaskStatusEnum.RUNNING.getTaskStatus());
                    feedTasktemp.setAttribute1(shopeeListingStatusConfig.getName());
                    feedTasktemp.setResultMsg(String.format("由【%s】触发删除", shopeeListingStatusConfig.getName()));
                    feedTasktemp.setCreatedBy("admin");
                });

                try {
                    // 获取账号信息并执行删除
                    SaleAccountAndBusinessResponse saleAccountByAccountNumber = AccountUtils.getSaleAccountByAccountNumber(SaleChannel.CHANNEL_SHOPEE, account, true);

                    // 获取存在的商品
                    List<EsShopeeItem> deleteItemList = esShopeeItemList.stream().filter(item -> deleteItems.contains(item.getItemId())).collect(Collectors.toList());

                    // 删除产品
                    ShopeeResponse response = ShopeeDeleteItemCallV2.deleteItem(esShopeeItem, saleAccountByAccountNumber);
                    if (StringUtils.isNotBlank(response.getError())) {
                        // 下架记录
                        List<ShopeeOfflineRecord> shopeeOfflineRecordList = getShopeeOfflineRecords(shopeeListingStatusConfig, deleteItemList, ShopeeStatusEnum.FAILURE.getCode(), response.getError());
                        shopeeOfflineRecordService.saveBatch(shopeeOfflineRecordList);

                        ShopeeFeedTaskHandleUtil.updateFeedTaskToFinish(feedTask, FeedTaskResultStatusEnum.FAIL.getResultStatus(), JSON.toJSONString(response));
                        return;
                    }

                    // 保存下架ES数据
                    shopeeItemOfflineEsService.saveByItemId(esShopeeItem.getItemId(), ShopeeItemStatusEnum.DELETED.getCode());

                    // 删除本地ES数据
                    NativeSearchQuery deleteQuery = new NativeSearchQueryBuilder()
                            .withQuery(QueryBuilders.boolQuery().must(QueryBuilders.matchQuery("itemId", esShopeeItem.getItemId())))
                            .build();
                    elasticsearchRestTemplate2.delete(deleteQuery, EsShopeeItem.class, INDEXCOORDINATES);

                    // 删除记录
                    List<ShopeeOfflineRecord> shopeeOfflineRecordList = getShopeeOfflineRecords(shopeeListingStatusConfig, deleteItemList, ShopeeStatusEnum.SUCCESS.getCode(), null);
                    shopeeOfflineRecordService.saveBatch(shopeeOfflineRecordList);

                    ShopeeFeedTaskHandleUtil.updateFeedTaskToFinish(feedTask, ResultStatusEnum.RESULT_SUCCESS.getStatusCode(), "");
                } catch (Exception e) {
                    ShopeeFeedTaskHandleUtil.updateFeedTaskToFinish(feedTask, ResultStatusEnum.RESULT_FAIL.getStatusCode(), e.getMessage());
                }
            });
        });
    }

    /**
     * 获取下架记录
     *
     * @param shopeeListingStatusConfig
     * @param esShopeeItemList
     * @param status
     * @param errorMsg
     * @return
     */
    private static List<ShopeeOfflineRecord> getShopeeOfflineRecords(ShopeeListingStatusConfig shopeeListingStatusConfig, List<EsShopeeItem> esShopeeItemList, Integer status, String errorMsg) {
        List<ShopeeOfflineRecord> shopeeOfflineRecordList = esShopeeItemList.stream().map(esShopeeItem -> {
            // 基础信息
            ShopeeOfflineRecord shopeeOfflineRecord = BeanUtil.copyProperties(esShopeeItem, ShopeeOfflineRecord.class);
            shopeeOfflineRecord.setRelationId(esShopeeItem.getId());
            Integer view7dCount = Optional.ofNullable(esShopeeItem.getView_7d_count()).orElseGet(() -> 0);
            shopeeOfflineRecord.setView7dCount(view7dCount);
            Integer view14dCount = Optional.ofNullable(esShopeeItem.getView_14d_count()).orElseGet(() -> 0);
            shopeeOfflineRecord.setView14dCount(view14dCount);
            Integer view30dCount = Optional.ofNullable(esShopeeItem.getView_30d_count()).orElseGet(() -> 0);
            shopeeOfflineRecord.setView30dCount(view30dCount);

            Integer order24HCount = Optional.ofNullable(esShopeeItem.getOrder24HCount()).orElseGet(() -> 0);
            shopeeOfflineRecord.setOrder24hCount(order24HCount);
            Integer orderLast7dCount = Optional.ofNullable(esShopeeItem.getOrderLast7dCount()).orElseGet(() -> 0);
            shopeeOfflineRecord.setOrderLast7dCount(orderLast7dCount);
            Integer orderLast14dCount = Optional.ofNullable(esShopeeItem.getOrderLast14dCount()).orElseGet(() -> 0);
            shopeeOfflineRecord.setOrderLast14dCount(orderLast14dCount);
            Integer orderLast30dCount = Optional.ofNullable(esShopeeItem.getOrderLast30dCount()).orElseGet(() -> 0);
            shopeeOfflineRecord.setOrderLast30dCount(orderLast30dCount);
            Integer orderLast60dCount = Optional.ofNullable(esShopeeItem.getOrderLast60dCount()).orElseGet(() -> 0);
            shopeeOfflineRecord.setOrderLast60dCount(orderLast60dCount);
            Integer orderLast90dCount = Optional.ofNullable(esShopeeItem.getOrderLast90dCount()).orElseGet(() -> 0);
            shopeeOfflineRecord.setOrderLast90dCount(orderLast90dCount);
            Integer orderNumTotal = Optional.ofNullable(esShopeeItem.getOrderNumTotal()).orElseGet(() -> 0);
            shopeeOfflineRecord.setOrderNumTotal(orderNumTotal);

            // 规则信息
            shopeeOfflineRecord.setRuleId(shopeeListingStatusConfig.getId());
            shopeeOfflineRecord.setRuleName(shopeeListingStatusConfig.getName());
            shopeeOfflineRecord.setRuleDate(LocalDateTime.now());

            // 状态和原因
            shopeeOfflineRecord.setStatus(status);
            if (ShopeeStatusEnum.SUCCESS.getCode().equals(status)) {
                shopeeOfflineRecord.setDownDate(LocalDateTime.now());
            } else {
                shopeeOfflineRecord.setDownFailureRemark(errorMsg);
            }

            // 其他信息
            // 在售状态 itemStatus
            Optional.ofNullable(esShopeeItem.getItemStatus()).ifPresent(shopeeOfflineRecord::setPlatformStatus);
            // 上架时间 uploadDate
            Optional.ofNullable(esShopeeItem.getUploadDate()).ifPresent(date -> {
                // Date转LocalDateTime
                LocalDateTime uploadDate = LocalDateTime.ofInstant(date.toInstant(), ZoneId.systemDefault());
                shopeeOfflineRecord.setUploadDate(uploadDate);
            });
            // 商品标签 skuStatus
            Optional.ofNullable(esShopeeItem.getSkuStatus()).ifPresent(shopeeOfflineRecord::setProductTag);
            // 单品状态 tagCodes
            Optional.ofNullable(esShopeeItem.getTagNames()).ifPresent(shopeeOfflineRecord::setItemStatus);
            // 禁售消息 forbidChannel、prohibitionSites
            Optional.ofNullable(esShopeeItem.getProhibitionSites()).ifPresent(value -> {
                shopeeOfflineRecord.setProhibitionSites(JSON.toJSONString(value));
            });
            shopeeOfflineRecord.setCreatedBy("admin");
            shopeeOfflineRecord.setCreatedTime(LocalDateTime.now());
            return shopeeOfflineRecord;
        }).collect(Collectors.toList());
        return shopeeOfflineRecordList;
    }

    /**
     * 构造查询条件
     *
     * @param account
     * @param itemId
     * @param ruleConfigJson
     * @return
     */
    private BoolQueryBuilder getBoolQueryBuilder(String account, String itemId, ShopeeListingOfflineConfigDTO.RuleConfigJson ruleConfigJson) {

        // 构造查询条件
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();

        boolQueryBuilder.must(QueryBuilders.termQuery("itemId", itemId));

        // 判断是否是商品 父sku且有变体 则本身不是商品
        boolQueryBuilder.must(QueryBuilders.termQuery("isGoods", true));

        // 店铺账号
        boolQueryBuilder.must(QueryBuilders.termsQuery("itemSeller", account));

        // 平台状态
        Optional.ofNullable(ruleConfigJson.getPlatformStatus()).ifPresent(status -> boolQueryBuilder.must(QueryBuilders.termQuery("itemStatus", status)));

        // 单品状态列表
//        if (CollectionUtils.isNotEmpty(ruleConfigJson.getItemStatus())) {
//            boolQueryBuilder.must(QueryBuilders.termsQuery("skuStatus", ruleConfigJson.getItemStatus()));
//        }

        // 产品标签列表,满足一个即可
//        Optional.ofNullable(ruleConfigJson.getProductTags()).ifPresent(productTags -> {
//            BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
//            for (String string : productTags) {
//                boolQuery.should(QueryBuilders.wildcardQuery("tagCodes", "*," + string + ",*"));
//            }
//            boolQuery.minimumShouldMatch(1);
//            boolQueryBuilder.must(boolQuery);
//        });

        // 禁售信息 forbidChannel、prohibitionSites
//        List<ShopeeListingOfflineConfigDTO.RuleConfigJson.ProhibitedInfo> prohibitedInfo = ruleConfigJson.getProhibitedInfo();
//        if (CollectionUtils.isNotEmpty(prohibitedInfo)) {
//            // 将key转成list
//            List<String> prohibitedInfoList = prohibitedInfo.stream().map(ShopeeListingOfflineConfigDTO.RuleConfigJson.ProhibitedInfo::getPlatform).filter(Objects::nonNull).collect(Collectors.toList());
//            if (CollectionUtils.isNotEmpty(prohibitedInfoList)) {
//                BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
//                for (String bidChannel : prohibitedInfoList) {
//                    boolQuery.should(QueryBuilders.wildcardQuery("forbidChannel", "*," + bidChannel + ",*"));
//                }
//                boolQuery.minimumShouldMatch(1);
//                boolQueryBuilder.must(boolQuery);
//
//                // 将prohibitedInfo的数据转成key1_value1,key1_value2,key2_value2的集合数据
//                List<String> channelSites = prohibitedInfo.stream()
//                        .flatMap(map -> map.getSiteList().stream()
//                                .map(value -> map.getPlatform() + "_" + value)
//                                .collect(Collectors.toList()).stream())
//                        .collect(Collectors.toList());
//                if (CollectionUtils.isNotEmpty(channelSites)) {
//                    boolQueryBuilder.must(QueryBuilders.termsQuery("prohibitionSites", channelSites));
//                }
//            }
//        }

        // 超过上架时长(不包含当天)
//        Optional.ofNullable(ruleConfigJson.getOnlineShelfDuration()).ifPresent(onlineShelfDuration -> {
//            // 获取当前日期的起始时间
//            LocalDate currentDate = LocalDate.now();
//            // 计算结束时间
//            LocalDateTime startTime = LocalDateTime.of(currentDate.minusDays(onlineShelfDuration), LocalTime.MIN);
//            boolQueryBuilder.must(QueryBuilders.rangeQuery("uploadDate").to(startTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))));
//        });

        // 页面访问周期
        Optional.ofNullable(ruleConfigJson.getPageViews()).ifPresent(pageViews -> {
            // 页面访问区间
            List<Integer> viewsRange = ruleConfigJson.getViewsRange();
            if (CollectionUtils.isNotEmpty(viewsRange)) {
                switch (pageViews) {
                    case 7:
                        // 7天内访问且view_7d_count不能null
                        boolQueryBuilder.must(QueryBuilders.rangeQuery("view_7d_count").from(viewsRange.get(0), true).to(viewsRange.get(1), false));
                        break;
                    case 14:
                        // 14天内访问
                        boolQueryBuilder.must(QueryBuilders.rangeQuery("view_14d_count").from(viewsRange.get(0), true).to(viewsRange.get(1), false));
                        break;
                    case 30:
                        // 30天内访问
                        boolQueryBuilder.must(QueryBuilders.rangeQuery("view_30d_count").from(viewsRange.get(0), true).to(viewsRange.get(1), false));
                        break;
                    default:
                        break;
                }
            }
        });
        return boolQueryBuilder;
    }
}

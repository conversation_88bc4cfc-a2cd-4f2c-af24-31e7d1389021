package com.estone.erp.publish.shopee.model;

import com.alibaba.nacos.common.utils.CollectionUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * 2024-10-15 18:32:54
 */
@Data
public class ShopeeTaskExecutionDetailsCriteria extends ShopeeTaskExecutionDetails {
    private static final long serialVersionUID = 1L;

    /**
     * id集合
     */
    private List<Long> idList;

    /**
     * 下载字段
     */
    private List<String> downloadFieldList;

    /**
     * 板块，1-营销活动 2-上架配置 3-下架配置 4-链接管理
     */
    private List<Integer> plates;

    /**
     * 配置类型 1-优惠券 2-店内秒杀 3-关注礼 4-折扣促销 5-优惠套装 6-短视频 7-上架 8-下架 9-调价
     */
    private List<Integer> configTypes;

    /**
     * 操作类型，1-添加 2-创建item 3-创建整体 4-同步item 5-同步整体
     */
    private List<Integer> operationTypes;

    /**
     * 规则名称
     */
    private List<String> ruleNames;

    /**
     * 店铺
     */
    private List<String> accountNumberList;

    /**
     * 店铺分组ID
     */
    private List<Integer> accountGroupIds;

    /**
     * 销售
     */
    private List<String> sellers;

    /**
     * 统计时间开始(T-1)
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Timestamp countTimeStart;

    /**
     * 统计时间结束(T-1)
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Timestamp countTimeEnd;

    /**
     * 执行时间开始
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Timestamp execTimeStart;

    /**
     * 执行时间结束
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Timestamp execTimeEnd;


    public ShopeeTaskExecutionDetailsExample getExample() {
        ShopeeTaskExecutionDetailsExample example = new ShopeeTaskExecutionDetailsExample();
        ShopeeTaskExecutionDetailsExample.Criteria criteria = example.createCriteria();
        if (CollectionUtils.isNotEmpty(this.getIdList())) {
            criteria.andIdIn(this.getIdList());
            return example;
        }
        if (CollectionUtils.isNotEmpty(this.getPlates())) {
            criteria.andPlateIn(this.getPlates());
        }
        if (CollectionUtils.isNotEmpty(this.getConfigTypes())) {
            criteria.andConfigTypeIn(this.getConfigTypes());
        }
        if (CollectionUtils.isNotEmpty(this.getOperationTypes())) {
            criteria.andOperationTypeIn(this.getOperationTypes());
        }
        if (CollectionUtils.isNotEmpty(this.getRuleNames())) {
            boolean contains = this.getRuleNames().contains("empty");
            if (contains) {
                criteria.andRuleNameInAndIsNull(this.getRuleNames());
            } else {
                criteria.andRuleNameIn(this.getRuleNames());
            }
        }
        if (CollectionUtils.isNotEmpty(this.getSellers())) {
            criteria.andSaleIn(this.getSellers());
        }
        if (CollectionUtils.isNotEmpty(this.getAccountNumberList())) {
            criteria.andAccountIn(this.getAccountNumberList());
        }
        if (CollectionUtils.isNotEmpty(this.getAccountGroupIds())) {
            boolean contains = this.getAccountGroupIds().contains(-1);
            if (contains) {
                criteria.andAccountGroupIdAndIsNull(this.getAccountGroupIds());
            } else {
                criteria.andAccountGroupIdIn(this.getAccountGroupIds());
            }
        }
        if (this.getCountTimeStart() != null) {
            criteria.andCountTimeGreaterThanOrEqualTo(this.getCountTimeStart());
        }
        if (this.getCountTimeEnd() != null) {
            criteria.andCountTimeLessThan(this.getCountTimeEnd());
        }
        if (this.getExecTimeStart() != null) {
            criteria.andExecTimeGreaterThanOrEqualTo(this.getExecTimeStart());
        }
        if (this.getExecTimeEnd() != null) {
            criteria.andExecTimeLessThan(this.getExecTimeEnd());
        }
        if (this.getPlate() != null) {
            criteria.andPlateEqualTo(this.getPlate());
        }
        if (this.getConfigType() != null) {
            criteria.andConfigTypeEqualTo(this.getConfigType());
        }
        if (this.getOperationType() != null) {
            criteria.andOperationTypeEqualTo(this.getOperationType());
        }
        if (StringUtils.isNotBlank(this.getRuleName())) {
            criteria.andRuleNameEqualTo(this.getRuleName());
        }
        if (StringUtils.isNotBlank(this.getAccount())) {
            criteria.andAccountEqualTo(this.getAccount());
        }
        if (this.getTotalNum() != null) {
            criteria.andTotalNumEqualTo(this.getTotalNum());
        }
        if (this.getSuccessNum() != null) {
            criteria.andSuccessNumEqualTo(this.getSuccessNum());
        }
        if (this.getFailNum() != null) {
            criteria.andFailNumEqualTo(this.getFailNum());
        }
        if (this.getCountTime() != null) {
            criteria.andCountTimeEqualTo(this.getCountTime());
        }
        if (this.getExecTime() != null) {
            criteria.andExecTimeEqualTo(this.getExecTime());
        }
        if (this.getAccountGroupId() != null) {
            criteria.andAccountGroupIdEqualTo(this.getAccountGroupId());
        }
        if (StringUtils.isNotBlank(this.getSale())) {
            criteria.andSaleEqualTo(this.getSale());
        }
        if (StringUtils.isNotBlank(this.getSaleLeader())) {
            criteria.andSaleLeaderEqualTo(this.getSaleLeader());
        }
        if (StringUtils.isNotBlank(this.getSaleSupervisor())) {
            criteria.andSaleSupervisorEqualTo(this.getSaleSupervisor());
        }
        if (this.getCreatedAt() != null) {
            criteria.andCreatedAtEqualTo(this.getCreatedAt());
        }
        return example;
    }
}
package com.estone.erp.publish.shopee.util;

import com.alibaba.nacos.common.utils.CollectionUtils;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.system.product.ProductInfo;
import com.estone.erp.publish.system.product.bean.forbidden.SalesProhibitionsVo;
import com.estone.erp.publish.system.product.bean.forbidden.Sites;
import org.apache.commons.lang.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description: 新品推送工具类
 * <AUTHOR>
 * @Date 2025/3/13 10:45
 */
public class NewProductUtils {

    /**
     * 获取所有SKU对应的禁止销售站点
     *
     * @param productInfoList
     * @return
     */
    public static Map<String, Set<String>> getAllSkuSalesProhibitions(List<ProductInfo> productInfoList) {
        Map<String, SalesProhibitionsVo> salesProhibitionsVoMap = new HashMap<>();
        for (ProductInfo productInfo : productInfoList) {
            List<SalesProhibitionsVo> salesProhibitionsVos = productInfo.getSalesProhibitionsVos();
            if (CollectionUtils.isEmpty(salesProhibitionsVos)) {
                SalesProhibitionsVo salesProhibitionsVo = new SalesProhibitionsVo();
                salesProhibitionsVo.setPlat(SaleChannel.CHANNEL_SHOPEE);
                salesProhibitionsVo.setSites(List.of());
                salesProhibitionsVoMap.put(productInfo.getSonSku(), salesProhibitionsVo);
                continue;
            }
            Optional<SalesProhibitionsVo> first1 = salesProhibitionsVos.stream().filter(a -> SaleChannel.CHANNEL_SHOPEE.equalsIgnoreCase(a.getPlat())).findFirst();
            if (first1.isPresent()) {
                salesProhibitionsVoMap.put(productInfo.getSonSku(), first1.get());
            } else {
                SalesProhibitionsVo salesProhibitionsVo = new SalesProhibitionsVo();
                salesProhibitionsVo.setPlat(SaleChannel.CHANNEL_SHOPEE);
                salesProhibitionsVo.setSites(List.of());
                salesProhibitionsVoMap.put(productInfo.getSonSku(), salesProhibitionsVo);
            }
        }
        Map<String, Set<String>> skuAndSitesMap = new HashMap<>();
        for (Map.Entry<String, SalesProhibitionsVo> skuAndSalesProhibitionsVoEntry : salesProhibitionsVoMap.entrySet()) {
            SalesProhibitionsVo value = skuAndSalesProhibitionsVoEntry.getValue();
            List<Sites> sites = value.getSites();
            String sku = skuAndSalesProhibitionsVoEntry.getKey();
            if (CollectionUtils.isEmpty(sites)) {
                skuAndSitesMap.put(sku, Set.of());
                continue;
            }
            Set<String> collect = sites.stream().map(Sites::getSite).filter(StringUtils::isNotBlank).map(String::toUpperCase).collect(Collectors.toSet());
            skuAndSitesMap.put(sku, collect);
        }

        return skuAndSitesMap;
    }
}

package com.estone.erp.publish.shopee.jobHandler;

import com.alibaba.fastjson.JSON;
import com.estone.erp.common.jobHandler.AbstractJobHandler;
import com.estone.erp.common.mq.PublishQueues;
import com.estone.erp.common.mq.PublishRabbitMqExchange;
import com.estone.erp.common.mq.RabbitMqSender;
import com.estone.erp.publish.base.pms.enums.SkuStatusEnum;
import com.estone.erp.publish.common.StockTypeEnum;
import com.estone.erp.publish.platform.model.PmsSku;
import com.estone.erp.publish.platform.model.PmsSkuExample;
import com.estone.erp.publish.platform.service.PmsSkuService;
import com.estone.erp.publish.platform.util.Platform;
import com.estone.erp.publish.shopee.dto.ShopeeHolidayJobMqBean;
import com.estone.erp.publish.shopee.enums.ShopeeHolidayJobMqTypeEnum;
import com.estone.erp.publish.system.account.AccountUtils;
import com.estone.erp.publish.system.account.SaleAccountAndBusinessResponse;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

//过年调库存任务
@Component
public class HolidayModifyStockJobHandler extends AbstractJobHandler {

    @Resource
    private RabbitMqSender rabbitMqSender;
    @Resource
    private PmsSkuService pmsSkuService;

    public HolidayModifyStockJobHandler() {
        super("HolidayModifyStockJobHandler");
    }

    @Getter
    @Setter
    static class InnerParam{
        private String accountNumber;

        /**
         * 库存类型
         * @see StockTypeEnum
         */
        private Integer stockType = StockTypeEnum.SYSTEM_STOCK.getType();

        /**
         * 南宁仓库库存类型
         */
        private Integer nnStockType = StockTypeEnum.SZ_AND_NN_SYSTEM_STOCK.getType();
    }

    @Override
    @XxlJob(("HolidayModifyStockJobHandler"))
    public ReturnT<String> run(String param) throws Exception {
        InnerParam innerParam = null;
        if(StringUtils.isNotBlank(param)){
            try {
                innerParam = JSON.parseObject(param, InnerParam.class);
            }catch (Exception e){
                XxlJobLogger.log("-------解析param出错：--------", e);
                return ReturnT.FAIL;
            }
        }
        if(innerParam == null){
            innerParam = new InnerParam();
        }
        // 输入账号参数
        String accountNumber = innerParam.getAccountNumber();
        // http://172.16.2.103:8080/browse/ES-7487
        // 假期在线数量改0 2024-02-18号 业务要求该规则年后暂停，保留规则2
       // holidayModifyStockToZero(accountNumber);
        Integer nnStockType = innerParam.getNnStockType();
        Integer stockType = innerParam.getStockType();

        // 假期在线数据量改系统库存
        holidayModifyStockToWhInventory(accountNumber, stockType, nnStockType);

        return ReturnT.SUCCESS;
    }

    /**
     * 1-同时满足以下2个条件，则调整库存为0
     * 1）按照库存可卖天数小于等于1
     * 2）单品状态为休假，暂停
     */
    private void holidayModifyStockToZero(String accountNumber) {
        //  南宁仓店铺特殊处理
        Boolean nnAccountNumber = checkAccountNumberNn(accountNumber);
        int availableStockDays = 1;
        BigDecimal one = BigDecimal.valueOf(availableStockDays);
        List<Integer> zeroSkuStatusList = new ArrayList<>();
        zeroSkuStatusList.add(SkuStatusEnum.HOLIDAY.getId());
        zeroSkuStatusList.add(SkuStatusEnum.PENDING.getId());
//        zeroSkuStatusList.add(SkuStatusEnum.NORMAL.getId());
//        zeroSkuStatusList.add(SkuStatusEnum.CLEARANCE.getId());
//        zeroSkuStatusList.add(SkuStatusEnum.REDUCTION.getId());

        int id = 0;
        int limit = 1000;
        while (true) {
            PmsSkuExample example = new PmsSkuExample();
            example.setFiledColumns("id, article_number");
            example.setOrderByClause("id ASC");
            example.setLimit(limit);

           PmsSkuExample.Criteria criteria = example.createCriteria();
            criteria.andStatusIn(zeroSkuStatusList)
                    .andIdGreaterThan(id);
            if (nnAccountNumber){
                criteria.andAvailableStockDaysMergeLessThanOrEqualTo(availableStockDays);
            }else {
                criteria .andAvailableStockDaysLessThanOrEqualTo(one);
            }
            List<PmsSku> pmsSkus = pmsSkuService.selectColumnsByExample(example);
            if(CollectionUtils.isEmpty(pmsSkus)) {
                break;
            }
            id = pmsSkus.get(pmsSkus.size() - 1).getId();

            List<String> skus = pmsSkus.stream().map(PmsSku::getArticleNumber).collect(Collectors.toList());
            ShopeeHolidayJobMqBean bean = new ShopeeHolidayJobMqBean();
            bean.setType(ShopeeHolidayJobMqTypeEnum.MODIFY_STOCK_ZERO.getCode());
            bean.setSkus(skus);
            bean.setAccountNumber(accountNumber);
            send(bean);
        }
    }

    /**
     * 2-同时满足以下三个条件，则调整库存为系统库存
     * 1）按照库存可卖天数小于等于2
     * 2）系统库存小于等于15
     * 3）单品状态为清仓 甩卖
     */
    private void holidayModifyStockToWhInventory(String accountNumber, Integer stockType, Integer nnStockType) {
        Boolean nnAccountNumber = checkAccountNumberNn(accountNumber);
        int availableStockDays = 2;
        BigDecimal two = BigDecimal.valueOf(availableStockDays);
        List<Integer> statusList = new ArrayList<>(2);
        statusList.add(SkuStatusEnum.CLEARANCE.getId());
        statusList.add(SkuStatusEnum.REDUCTION.getId());

        int id = 0;
        int limit = 1000;
        while (true) {
            PmsSkuExample example = new PmsSkuExample();
            example.setFiledColumns("id, article_number");
            example.setOrderByClause("id ASC");
            example.setLimit(limit);
            PmsSkuExample.Criteria criteria = example.createCriteria();
            criteria.andStatusIn(statusList);
            if (id > 0) {
                criteria.andIdGreaterThan(id);
            }
            if (StringUtils.isBlank(accountNumber)) {
                criteria.andCustomSql("( (available_stock_days_merge <= " + availableStockDays + " and system_stock_merge <= 15 )  or (available_stock_days <= "+ two + " and system_stock <= 15 ))" );
            } else {
                if (nnAccountNumber) {
                    criteria.andAvailableStockDaysMergeLessThanOrEqualTo(availableStockDays)
                            .andSystemStockMergeLessThanOrEqualTo(15);
                } else {
                    criteria.andAvailableStockDaysLessThanOrEqualTo(two)
                            .andSystemStockLessThanOrEqualTo(15);
                }
            }

            List<PmsSku> pmsSkus = pmsSkuService.selectColumnsByExample(example);
            if(CollectionUtils.isEmpty(pmsSkus)) {
                break;
            }
            id = pmsSkus.get(pmsSkus.size() - 1).getId();

            List<String> skus = pmsSkus.stream().map(PmsSku::getArticleNumber).collect(Collectors.toList());
            ShopeeHolidayJobMqBean bean = new ShopeeHolidayJobMqBean();
            bean.setType(ShopeeHolidayJobMqTypeEnum.MODIFY_STOCK_WH_INVENTORY.getCode());
            bean.setSkus(skus);
            bean.setAccountNumber(accountNumber);
            bean.setStockType(stockType);
            bean.setNnStockType(nnStockType);
            send(bean);
        }
    }

    public Boolean checkAccountNumberNn(String accountNumber){
        if (StringUtils.isBlank(accountNumber)){
            return false;
        }
        SaleAccountAndBusinessResponse saleAccount = AccountUtils.getSaleAccountByAccountNumber(Platform.Shopee.name(), accountNumber, true);
        if (null == saleAccount){
            return false;
        }
        Boolean isNnWarehouse = saleAccount.getShopeeColBool3();
        return BooleanUtils.isTrue(isNnWarehouse) ? true : false;
    }

    /**
     * 发送数据
     * @param bean
     */
    private void send(ShopeeHolidayJobMqBean bean) {
        if(null == bean || CollectionUtils.isEmpty(bean.getSkus()) || null == bean.getType()) {
            XxlJobLogger.log("数据未发送存在为空数据 " + JSON.toJSONString(bean));
            return;
        }
        try{
            rabbitMqSender.send(PublishRabbitMqExchange.SHOPEE_API_DIRECT_EXCHANGE, PublishQueues.SHOPEE_HOLIDAY_JOB_ROUTE_KEY, JSON.toJSON(bean));
        }catch (Exception e){
            XxlJobLogger.log("发送改库存mq 异常 " + JSON.toJSONString(bean));
        }
    }
}

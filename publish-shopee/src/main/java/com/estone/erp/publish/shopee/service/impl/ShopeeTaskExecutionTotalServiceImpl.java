package com.estone.erp.publish.shopee.service.impl;

import com.alibaba.cloud.commons.lang.StringUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.nacos.common.utils.CollectionUtils;
import com.estone.erp.common.exception.BusinessException;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.model.api.CQuery;
import com.estone.erp.common.model.api.CQueryResult;
import com.estone.erp.common.mq.PublishQueues;
import com.estone.erp.common.mq.PublishRabbitMqExchange;
import com.estone.erp.common.redis.util.WebUtils;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.common.context.DataContextHolder;
import com.estone.erp.publish.component.PermissionsHelper;
import com.estone.erp.publish.shopee.component.download.ShopeeDownloadTypeEnums;
import com.estone.erp.publish.shopee.dto.ShopeeTaskExecutionDetailsRequestDTO;
import com.estone.erp.publish.shopee.mapper.ShopeeTaskExecutionTotalMapper;
import com.estone.erp.publish.shopee.model.ShopeeTaskExecutionTotal;
import com.estone.erp.publish.shopee.model.ShopeeTaskExecutionTotalCriteria;
import com.estone.erp.publish.shopee.model.ShopeeTaskExecutionTotalExample;
import com.estone.erp.publish.shopee.service.ShopeeTaskExecutionDetailsService;
import com.estone.erp.publish.shopee.service.ShopeeTaskExecutionTotalService;
import com.estone.erp.publish.system.excel.enums.ExcelDownloadStatusEnums;
import com.estone.erp.publish.system.excel.model.ExcelDownloadLog;
import com.estone.erp.publish.system.excel.service.ExcelDownloadLogService;
import com.estone.erp.publish.system.newUsermgt.NewUsermgtUtils;
import com.estone.erp.publish.system.newUsermgt.constant.RoleConstant;
import com.estone.erp.publish.system.newUsermgt.model.NewUser;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * 2024-10-28 17:38:42
 */
@Service("shopeeTaskExecutionTotalService")
@Slf4j
public class ShopeeTaskExecutionTotalServiceImpl implements ShopeeTaskExecutionTotalService {

    @Resource
    private PermissionsHelper permissionsHelper;

    @Resource
    private ExcelDownloadLogService excelDownloadLogService;

    @Resource
    private ShopeeTaskExecutionTotalMapper shopeeTaskExecutionTotalMapper;

    @Resource
    private ShopeeTaskExecutionDetailsService shopeeTaskExecutionDetailsService;

    @Override
    public int countByExample(ShopeeTaskExecutionTotalExample example) {
        Assert.notNull(example, "example is null!");
        return shopeeTaskExecutionTotalMapper.countByExample(example);
    }

    @Override
    public CQueryResult<ShopeeTaskExecutionTotal> search(CQuery<ShopeeTaskExecutionTotalCriteria> cquery) {
        Assert.notNull(cquery, "cquery is null!");
        ShopeeTaskExecutionTotalCriteria query = cquery.getSearch();
        // 权限处理
        permission(query);

        // 店铺维度则需要实时查询
        if (CollectionUtils.isNotEmpty(query.getAccountList())) {
            return getShopeeTaskExecutionTotalCQueryResult(cquery, query);
        }

        ShopeeTaskExecutionTotalExample example = query.getExample();
        example.setOrderByClause("id desc");
        long total = 0;
        int totalPages = 0;
        // 是否分页
        if (cquery.isPageReqired()) {
            total = shopeeTaskExecutionTotalMapper.countByExample(example);
            totalPages = (int) Math.ceil((double) total / cquery.getLimit());
            example.setLimit(cquery.getLimit());
            example.setOffset(cquery.getOffset());
        }
        List<ShopeeTaskExecutionTotal> shopeeTaskExecutionTotals = shopeeTaskExecutionTotalMapper.selectByExample(example);
        // 组装结果
        CQueryResult<ShopeeTaskExecutionTotal> result = new CQueryResult<>();
        result.setTotal(total);
        result.setTotalPages(totalPages);
        result.setRows(shopeeTaskExecutionTotals);
        return result;
    }

    private CQueryResult<ShopeeTaskExecutionTotal> getShopeeTaskExecutionTotalCQueryResult(CQuery<ShopeeTaskExecutionTotalCriteria> cquery, ShopeeTaskExecutionTotalCriteria query) {
        // 获取当前登录用户信息
        List<String> currentUserPermission = permissionsHelper.getCurrentUserPermission(query.getAccountList(), null, null, null, SaleChannel.CHANNEL_SHOPEE, true);
        query.setAccountList(currentUserPermission);

        ShopeeTaskExecutionDetailsRequestDTO requestDTO = new ShopeeTaskExecutionDetailsRequestDTO();
        requestDTO.setRuleNameType(query.getRuleNameType());
        requestDTO.setSaleType(query.getSaleType());
        requestDTO.setAccountList(query.getAccountList());
        requestDTO.setSaleList(query.getSaleList());
        requestDTO.setSaleLeaderList(query.getSaleLeaderList());
        requestDTO.setSalesSupervisorList(query.getSalesSupervisorList());
        requestDTO.setOffset(cquery.getOffset());
        requestDTO.setLimit(cquery.getLimit());
        List<ShopeeTaskExecutionTotal> taskExecutionTotals = shopeeTaskExecutionDetailsService.getTaskExecutionDetailPages(requestDTO);

        long total = 0;
        int totalPages = 0;
        // 是否分页
        if (cquery.isPageReqired()) {
            total = shopeeTaskExecutionDetailsService.getTaskExecutionDetailPageCount(requestDTO);
            totalPages = (int) Math.ceil((double) total / cquery.getLimit());
        }

        // 组装结果
        CQueryResult<ShopeeTaskExecutionTotal> result = new CQueryResult<>();
        result.setTotal(total);
        result.setTotalPages(totalPages);
        result.setRows(taskExecutionTotals);
        return result;
    }

    @Override
    public ApiResult<String> download(CQuery<ShopeeTaskExecutionTotalCriteria> cquery) {
        try {
            Assert.notNull(cquery, "cquery is null!");
            ShopeeTaskExecutionTotalCriteria query = cquery.getSearch();

            int count = 0;
            if (CollectionUtils.isEmpty(query.getAccountList())) {
                // 权限处理
                permission(query);

                // 获取数据总数
                ShopeeTaskExecutionTotalExample example = query.getExample();
                example.setOrderByClause("id desc");
                count = shopeeTaskExecutionTotalMapper.countByExample(example);
            } else {
                ShopeeTaskExecutionDetailsRequestDTO requestDTO = new ShopeeTaskExecutionDetailsRequestDTO();
                requestDTO.setRuleNameType(query.getRuleNameType());
                requestDTO.setSaleType(query.getSaleType());
                requestDTO.setAccountList(query.getAccountList());
                requestDTO.setSaleList(query.getSaleList());
                requestDTO.setSaleLeaderList(query.getSaleLeaderList());
                requestDTO.setSalesSupervisorList(query.getSalesSupervisorList());
                count = shopeeTaskExecutionDetailsService.getTaskExecutionDetailPageCount(requestDTO);
            }

            if (count == 0) {
                return ApiResult.newError("没有数据，无法下载");
            }
            if (count > 1000000) {
                return ApiResult.newError("超出最大下载数量限制，无法下载");
            }
            // 构造导出日志
            ExcelDownloadLog downloadLog = new ExcelDownloadLog();
            downloadLog.setType(ShopeeDownloadTypeEnums.TASK_EXECUTION_TOTAL.getType());
            downloadLog.setQueryCondition(JSON.toJSONString(query));
            downloadLog.setDownloadCount(count);
            downloadLog.setStatus(ExcelDownloadStatusEnums.WAIT.getCode());
            downloadLog.setCreateTime(Timestamp.valueOf(LocalDateTime.now()));
            downloadLog.setCreateBy(StringUtils.isBlank(WebUtils.getUserName()) ? DataContextHolder.getUsername() : com.estone.erp.common.util.WebUtils.getUserName());
            // 发送队列
            excelDownloadLogService.addAndPushLog(downloadLog, SaleChannel.CHANNEL_SHOPEE, PublishRabbitMqExchange.SHOPEE_API_DIRECT_EXCHANGE, PublishQueues.SHOPEE_DOWNLOAD_QUEUE_KEY);
            return ApiResult.newSuccess("导出成功，稍后前往导出日志查看！");
        } catch (Exception e) {
            return ApiResult.newError(e.getMessage());
        }
    }

    @Override
    public ShopeeTaskExecutionTotal selectByPrimaryKey(Long id) {
        Assert.notNull(id, "id is null!");
        return shopeeTaskExecutionTotalMapper.selectByPrimaryKey(id);
    }

    @Override
    public List<ShopeeTaskExecutionTotal> selectByExample(ShopeeTaskExecutionTotalExample example) {
        Assert.notNull(example, "example is null!");
        return shopeeTaskExecutionTotalMapper.selectByExample(example);
    }

    @Override
    public int insert(ShopeeTaskExecutionTotal record) {
        Assert.notNull(record, "record is null!");
        // 默认加时间和人
        return shopeeTaskExecutionTotalMapper.insert(record);
    }

    @Override
    public int updateByPrimaryKeySelective(ShopeeTaskExecutionTotal record) {
        Assert.notNull(record, "record is null!");
        // 默认加修改时间和修改人
        return shopeeTaskExecutionTotalMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public int updateByExampleSelective(ShopeeTaskExecutionTotal record, ShopeeTaskExecutionTotalExample example) {
        Assert.notNull(record, "record is null!");
        // 默认加修改时间和修改人
        return shopeeTaskExecutionTotalMapper.updateByExampleSelective(record, example);
    }

    @Override
    public int deleteByPrimaryKey(List<Long> ids) {
        Assert.notNull(ids, "ids is null!");
        return shopeeTaskExecutionTotalMapper.deleteByPrimaryKey(ids);
    }

    @Override
    public void batchInsert(List<ShopeeTaskExecutionTotal> taskExecutionTotalList) {
        Assert.notNull(taskExecutionTotalList, "taskExecutionTotalList is null!");
        shopeeTaskExecutionTotalMapper.batchInsert(taskExecutionTotalList);
    }

    /**
     * 权限处理
     *
     * @param query
     */
    private static void permission(ShopeeTaskExecutionTotalCriteria query) {
        // 获取当前登录用户信息
        String employeeNo = WebUtils.getUserName();
        ApiResult<Boolean> superAdminOrEquivalent = NewUsermgtUtils.isSuperAdminOrEquivalent(SaleChannel.CHANNEL_SHOPEE);
        if (!superAdminOrEquivalent.isSuccess()) {
            throw new BusinessException(superAdminOrEquivalent.getErrorMsg());
        }

        ApiResult<NewUser> newUserApiResult = NewUsermgtUtils.getUserByNo(employeeNo);
        if (!newUserApiResult.isSuccess()) {
            throw new BusinessException(newUserApiResult.getErrorMsg());
        }

        // 非超级管理员或数据支持部（数据分析）查询时，默认查询自己的店铺
        if (query.getSaleType().equals(0) && (CollectionUtils.isEmpty(query.getSaleList()) || CollectionUtils.isEmpty(query.getSaleLeaderList()) || CollectionUtils.isEmpty(query.getSalesSupervisorList()))) {
            // 不等于超级管理员或数据支持部（数据分析）查询所有
            if (!superAdminOrEquivalent.getResult() && !newUserApiResult.getResult().getRsRoleNames().contains(RoleConstant.DATA_SUPPORT_DEPARTMENT)) {
                NewUser result = newUserApiResult.getResult();
                if (result.getPositionName().contains("主管")) {
                    query.setSaleType(3);
                    query.setSalesSupervisorList(Collections.singletonList(result.getEmployeeNo()));
                } else if (result.getPositionName().contains("组长")) {
                    query.setSaleType(2);
                    query.setSaleLeaderList(Collections.singletonList(result.getEmployeeNo()));
                } else {
                    query.setSaleType(1);
                    query.setSaleList(Collections.singletonList(result.getEmployeeNo()));
                }
            }
        } else if (!superAdminOrEquivalent.getResult() && !newUserApiResult.getResult().getRsRoleNames().contains(RoleConstant.DATA_SUPPORT_DEPARTMENT)) {
            List<String> saleList = query.getSaleList();
            saleList.add(employeeNo);
            query.setSaleList(saleList);
        }
    }
}
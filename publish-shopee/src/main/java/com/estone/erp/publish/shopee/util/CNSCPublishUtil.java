package com.estone.erp.publish.shopee.util;

import com.estone.erp.publish.common.executors.ShopeeExecutors;
import com.estone.erp.publish.platform.util.Platform;
import com.estone.erp.publish.shopee.constant.ShopeeConstants;
import com.estone.erp.publish.system.account.AccountUtils;
import com.estone.erp.publish.system.account.SaleAccountAndBusinessResponse;
import com.estone.erp.publish.system.account.ShopeeMerchant;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;

import java.sql.Timestamp;
import java.util.function.Supplier;

/**
 * <AUTHOR>
 * @date 2021/6/28 11:33
 * @description
 */
@Slf4j
public class CNSCPublishUtil {

    /**
     * 普通接口令牌从 token redis重新获取
     * CNSC验证账号是否过期并获取最新token
     * @param account
     */
    public static void checkAccountExpire(SaleAccountAndBusinessResponse account) {
        if(ShopeeConstants.CNSC.equalsIgnoreCase(account.getAccountSite())){
            if (account != null && account.getTokenExpireTime() != null
                && account.getTokenExpireTime().getTime() <= System.currentTimeMillis()) {
                try {
                    //account.getShopId() 是商家id
                    ShopeeMerchant merchant = AccountUtils.getMerchantInfoById(account.getMarketplaceId());
                    if(merchant != null){
                        //token
                        account.setAccessToken(merchant.getAccessToken());
                        account.setRefreshToken(merchant.getRefreshToken());
                        //time
                        account.setTokenExpireTime(new Timestamp(merchant.getTokenExpireTime().getTime()));
                    }
                }catch (Exception e){
                    log.error(String.format("获取商家%s,接口出错", account.getAccountNumber()), e);
                }
            }
        }else{
            try {
                String accessToken = AccountUtils.getAccessTokenByRedis(Platform.Shopee.name(), account.getAccountNumber());
                if(StringUtils.isNotBlank(accessToken)) {
                    account.setAccessToken(accessToken);
                }
            }catch (Exception e){
                log.error(String.format("获取账号%s,接口出错", account.getAccountNumber()), e);
            }
        }
    }

    /**
     * 根据账号获取店铺商家  并把商家授权信息放到店铺对象里
     * @param accountNumber
     * @return
     */
    public static SaleAccountAndBusinessResponse getCnscAccount(String accountNumber) {
        SaleAccountAndBusinessResponse account = AccountUtils.getSaleAccountByAccountNumber(Platform.Shopee.name(), accountNumber, true);
        if(account.getShopeeMerchant() == null && StringUtils.isNotBlank(account.getMerchantId())){
            ShopeeMerchant merchant = AccountUtils.getMerchantInfoById(account.getMerchantId());
            account.setShopeeMerchant(merchant);
        }

        if(account.getShopeeMerchant() == null){
            return null;
        }else{
            SaleAccountAndBusinessResponse shopeeAccount = transCnscBean(account, account.getShopeeMerchant());
            return shopeeAccount;
        }
    }

    /**
     * 转换成cnsc账号
     * @param account
     * @return
     */
    public static SaleAccountAndBusinessResponse transCNSCAccount(SaleAccountAndBusinessResponse account) {
        if(account.getShopeeMerchant() == null){
            return getCnscAccount(account.getAccountNumber());
        }

        ShopeeMerchant shopeeMerchant = account.getShopeeMerchant();
        if(shopeeMerchant.getTokenExpireTime() != null && shopeeMerchant.getTokenExpireTime().getTime() <= System.currentTimeMillis()){
            //过期了
            ShopeeMerchant merchant = AccountUtils.getMerchantInfoById(account.getMerchantId());
            if(merchant != null){
                shopeeMerchant = merchant;
                account.setShopeeMerchant(merchant);
            }
        }

        SaleAccountAndBusinessResponse shopeeAccount = transCnscBean(account, shopeeMerchant);

        return shopeeAccount;
    }

    private static SaleAccountAndBusinessResponse transCnscBean(SaleAccountAndBusinessResponse account, ShopeeMerchant shopeeMerchant) {
        SaleAccountAndBusinessResponse shopeeAccount = new SaleAccountAndBusinessResponse();
        String merchantId = shopeeMerchant.getMerchantId() == null ? account.getAccountNumber() : shopeeMerchant.getMerchantId();
        shopeeAccount.setAccountNumber(merchantId);

        //合作伙伴id
        shopeeAccount.setColStr1(account.getColStr1());

        //商家id 放到店铺id字段
        shopeeAccount.setMerchantId(shopeeMerchant.getMerchantId());
        shopeeAccount.setMarketplaceId(shopeeMerchant.getMerchantId());

        //api key
        shopeeAccount.setClientId(account.getClientId());

        //token
        shopeeAccount.setAccessToken(shopeeMerchant.getAccessToken());
        shopeeAccount.setRefreshToken(shopeeMerchant.getRefreshToken());

        // 海外仓类型
        shopeeAccount.setOverseaWarehouse(account.getOverseaWarehouse());

        //time
        shopeeAccount.setTokenExpireTime(new Timestamp(shopeeMerchant.getTokenExpireTime().getTime()));

        //cnsc 标识商家
        shopeeAccount.setAccountSite(ShopeeConstants.CNSC);
        return shopeeAccount;
    }


    /**
     * 异步刊登全球产品方法
     * @param templateId
     * @param supplier
     */
    public static void asyncPublishGlobalTemplate(Long templateId, Supplier<Long> supplier){
        //异步
        ShopeeExecutors.executePublishCnsc(() -> {
            long st = System.currentTimeMillis();
//            long id = 0;
            try {
                supplier.get();
            }catch (Exception e){
                log.error(String.format("模板:%s publish error", templateId, e));
            }

            log.info("global template:{} publish time {}s", templateId, (System.currentTimeMillis() - st) / 1000D);
        });
    }

    /**
     * 获取随机字符 数字
     * @return
     */
    public static String getRandomChar() {
        return "_" + RandomStringUtils.random(5, true, true).toUpperCase();
    }
}

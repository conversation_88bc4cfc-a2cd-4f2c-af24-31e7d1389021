package com.estone.erp.publish.shopee.controller;

import com.alibaba.fastjson.TypeReference;
import com.estone.erp.common.model.api.ApiRequestParam;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.model.api.CQuery;
import com.estone.erp.common.model.api.CQueryResult;
import com.estone.erp.common.util.Asserts;
import com.estone.erp.publish.common.ErrorCode;
import com.estone.erp.publish.shopee.model.ShopeeSkuOrder;
import com.estone.erp.publish.shopee.model.ShopeeSkuOrderCriteria;
import com.estone.erp.publish.shopee.service.ShopeeSkuOrderService;
import javax.annotation.Resource;
import org.apache.commons.lang.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR> shopee_sku_order
 * 2021-03-01 14:03:44
 */
@RestController
@RequestMapping("shopeeSkuOrder")
public class ShopeeSkuOrderController {
    @Resource
    private ShopeeSkuOrderService shopeeSkuOrderService;

    @PostMapping
    public ApiResult<?> postShopeeSkuOrder(@RequestBody(required = true) ApiRequestParam<String> requestParam) {
        String method = requestParam.getMethod();
        if (StringUtils.isNotBlank(method)) {
            switch (method) {
                case "searchShopeeSkuOrder": // 查询列表
                    CQuery<ShopeeSkuOrderCriteria> cquery = requestParam.getArgsValue(new TypeReference<CQuery<ShopeeSkuOrderCriteria>>() {});
                    Asserts.isTrue(cquery != null, ErrorCode.PARAM_EMPTY_ERROR);
                    CQueryResult<ShopeeSkuOrder> results = shopeeSkuOrderService.search(cquery);
                    return results;
                case "addShopeeSkuOrder": // 添加
                    ShopeeSkuOrder shopeeSkuOrder = requestParam.getArgsValue(new TypeReference<ShopeeSkuOrder>() {});
                    shopeeSkuOrderService.insert(shopeeSkuOrder);
                    return ApiResult.newSuccess(shopeeSkuOrder);
                }
        }
        return ApiResult.newSuccess();
    }

    @GetMapping(value = "/{id}")
    public ApiResult<?> getShopeeSkuOrder(@PathVariable(value = "id", required = true) Integer id) {
        ShopeeSkuOrder shopeeSkuOrder = shopeeSkuOrderService.selectByPrimaryKey(id);
        return ApiResult.newSuccess(shopeeSkuOrder);
    }

    @PutMapping(value = "/{id}")
    public ApiResult<?> putShopeeSkuOrder(@PathVariable(value = "id", required = true) Integer id, @RequestBody ApiRequestParam<String> requestParam) {
        String method = requestParam.getMethod();
        if (StringUtils.isNotBlank(method)) {
            switch (method) {
                case "updateShopeeSkuOrder": // 单个修改
                    ShopeeSkuOrder shopeeSkuOrder = requestParam.getArgsValue(new TypeReference<ShopeeSkuOrder>() {});
                    shopeeSkuOrderService.updateByPrimaryKeySelective(shopeeSkuOrder);
                    return ApiResult.newSuccess(shopeeSkuOrder);
                }
        }
        return ApiResult.newSuccess();
    }
}
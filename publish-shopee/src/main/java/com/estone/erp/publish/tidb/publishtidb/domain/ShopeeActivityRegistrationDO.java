package com.estone.erp.publish.tidb.publishtidb.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * @Description: Shopee跨境卖家活动DO
 * <AUTHOR>
 * @Date 2025/4/15 18:29
 */
@Data
public class ShopeeActivityRegistrationDO {

    /**
     * id集合
     */
    private List<Long> idList;

    /**
     * 规则名称
     */
    private String ruleName;

    /**
     * 店铺账号
     */
    private List<String> accountNumberList;

    /**
     * 站点
     */
    private List<String> siteList;

    /**
     * 店铺分组ID
     */
    private List<Integer> accountGroupIdList;

    /**
     * 报名ID
     */
    private List<Long> registrationIdList;

    /**
     * 活动名称
     */
    private String activityName;

    /**
     * 报名进度开始
     */
    private BigDecimal registrationProgressStart;

    /**
     * 报名进度结束
     */
    private BigDecimal registrationProgressEnd;

    /**
     * 报名方式（0: 商品确认, 1: 商品议价, 2: 自主报名）
     */
    private List<Integer> registrationMethodList;

    /**
     * 报名活动类型（0: Non-SIP, 1: SIP）
     */
    private Integer activityType;

    /**
     * 状态（0: 正在进行, 1: 即将到来, 2: 已结束）
     */
    private List<Integer> statusList;

    /**
     * 优先级（0: 低, 1: 中, 2: 高）
     */
    private List<Integer> priorityList;

    /**
     * 上传状态（0: 待上传, 1: 上传中, 2: 上传成功, 3: 上传失败, 4: 无需上传）
     */
    private Integer uploadStatus;

    /**
     * 销售
     */
    private List<String> saleList;

    /**
     * 销售组长
     */
    private List<String> saleLeaderList;

    /**
     * 销售主管
     */
    private List<String> salesSupervisorList;

    /**
     * 活动时间开始时间范围
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime activityStartTimeStart;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime activityEndTimeStart;

    /**
     * 活动时间结束时间范围
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime activityStartTimeEnd;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime activityEndTimeEnd;


    /**
     * 采集时间范围
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime crawlStartTime;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime crawlEndTime;

    /**
     * 更新时间范围
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updatedStartTime;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updatedEndTime;

    /**
     * 生成报名文件时间范围
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime generationStartTime;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime generationEndTime;

    /**
     * 上传报名文件时间范围
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime uploadStartTime;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime uploadEndTime;

    /**
     * 是否成功生成报名文件
     */
    private Boolean isGeneratedFile;

}

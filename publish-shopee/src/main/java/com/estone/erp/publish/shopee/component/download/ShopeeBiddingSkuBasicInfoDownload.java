package com.estone.erp.publish.shopee.component.download;

import com.alibaba.cloud.commons.lang.StringUtils;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.fastjson.JSON;
import com.alibaba.nacos.common.utils.CollectionUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.estone.erp.common.util.BeanUtil;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.common.context.DataContextHolder;
import com.estone.erp.publish.system.excel.model.ExcelDownloadLog;
import com.estone.erp.publish.system.excel.service.DownloadService;
import com.estone.erp.publish.tidb.publishtidb.domain.ShopeeBiddingSkuBasicInfoDO;
import com.estone.erp.publish.tidb.publishtidb.domain.vo.ShopeeBiddingSkuBasicInfoVO;
import com.estone.erp.publish.tidb.publishtidb.mapper.ShopeeBiddingSkuBasicInfoMapper;
import com.estone.erp.publish.tidb.publishtidb.model.ShopeeBiddingSkuBasicInfo;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.File;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Description: 竞价SKU基本信息导出
 * <AUTHOR>
 * @Date 2025/5/16 18:29
 */
@Component
public class ShopeeBiddingSkuBasicInfoDownload implements DownloadService {

    @Resource
    private ShopeeBiddingSkuBasicInfoMapper shopeeBiddingSkuBasicInfoMapper;

    @Override
    public String platform() {
        return SaleChannel.CHANNEL_SHOPEE;
    }

    @Override
    public String type() {
        return ShopeeDownloadTypeEnums.BIDDING_SKU_BASIC_INFO.getType();
    }

    @Override
    public void download(ExcelDownloadLog downloadLog, File temFile) {
        List<ShopeeBiddingSkuBasicInfoVO> biddingSkuBasicInfoVOList = new ArrayList<>();

        DataContextHolder.setUsername(downloadLog.getCreateBy());
        ShopeeBiddingSkuBasicInfoDO search = JSON.parseObject(downloadLog.getQueryCondition(), ShopeeBiddingSkuBasicInfoDO.class);

        // 导出数据
        ExcelWriter excelWriter = EasyExcel.write(temFile, ShopeeBiddingSkuBasicInfoVO.class).build();
        WriteSheet writeSheet = EasyExcel.writerSheet("sheet").build();

        // 构建参数
        LambdaQueryWrapper<ShopeeBiddingSkuBasicInfo> lambdaQueryWrapper = new LambdaQueryWrapper<ShopeeBiddingSkuBasicInfo>()
                .in(CollectionUtils.isNotEmpty(search.getIdList()), ShopeeBiddingSkuBasicInfo::getId, search.getIdList())
                .in(StringUtils.isNotEmpty(search.getSpu()), ShopeeBiddingSkuBasicInfo::getSpu, search.toList(search.getSpu()))
                .in(StringUtils.isNotEmpty(search.getSku()), ShopeeBiddingSkuBasicInfo::getSku, search.toList(search.getSku()))
                .like(StringUtils.isNotEmpty(search.getBiddingKeyword()), ShopeeBiddingSkuBasicInfo::getBiddingKeyword, search.getBiddingKeyword())
                .ge(ObjectUtils.isNotEmpty(search.getMinGrossMarginRateStart()), ShopeeBiddingSkuBasicInfo::getMinGrossMarginRate, search.getMinGrossMarginRateStart())
                .le(ObjectUtils.isNotEmpty(search.getMinGrossMarginRateEnd()), ShopeeBiddingSkuBasicInfo::getMinGrossMarginRate, search.getMinGrossMarginRateEnd())
                .between(search.getImportTimeStart() != null && search.getImportTimeEnd() != null, ShopeeBiddingSkuBasicInfo::getImportTime, search.getImportTimeStart(), search.getImportTimeEnd())
                .between(search.getCreatedAtStart() != null && search.getCreatedAtEnd() != null, ShopeeBiddingSkuBasicInfo::getCreatedAt, search.getCreatedAtStart(), search.getCreatedAtEnd())
                .between(search.getUpdatedAtStart() != null && search.getUpdatedAtEnd() != null, ShopeeBiddingSkuBasicInfo::getUpdatedAt, search.getUpdatedAtStart(), search.getUpdatedAtEnd())
                .in(CollectionUtils.isNotEmpty(search.getCreatedByList()), ShopeeBiddingSkuBasicInfo::getCreatedBy, search.getCreatedByList())
                .in(CollectionUtils.isNotEmpty(search.getUpdatedByList()), ShopeeBiddingSkuBasicInfo::getUpdatedBy, search.getUpdatedByList())
                .orderByDesc(ShopeeBiddingSkuBasicInfo::getImportTime);

        // 分页查询
        int pageIndex = 1;
        int pageSize = 500;
        while (true) {
            // 分页查询
            IPage<ShopeeBiddingSkuBasicInfo> page = new Page<>(pageIndex, pageSize);
            IPage<ShopeeBiddingSkuBasicInfo> pageResult = shopeeBiddingSkuBasicInfoMapper.selectPage(page, lambdaQueryWrapper);
            pageIndex++;
            if (CollectionUtils.isEmpty(pageResult.getRecords())) {
                break;
            }

            // 转换为VO
            List<ShopeeBiddingSkuBasicInfoVO> voList = pageResult.getRecords().stream()
                    .map(info -> BeanUtil.copyProperties(info, ShopeeBiddingSkuBasicInfoVO.class))
                    .collect(Collectors.toList());
            
            biddingSkuBasicInfoVOList.addAll(voList);
        }

        excelWriter.write(biddingSkuBasicInfoVOList, writeSheet);
        excelWriter.finish();
        downloadLog.setDownloadCount(biddingSkuBasicInfoVOList.size());
    }
}
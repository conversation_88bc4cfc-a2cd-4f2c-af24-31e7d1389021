package com.estone.erp.publish.shopee.component.download;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.fastjson.JSON;
import com.alibaba.nacos.common.utils.CollectionUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.common.context.DataContextHolder;
import com.estone.erp.publish.system.excel.model.ExcelDownloadLog;
import com.estone.erp.publish.system.excel.service.DownloadService;
import com.estone.erp.publish.tidb.publishtidb.domain.ShopeeNewProductTemplateCompletionDO;
import com.estone.erp.publish.tidb.publishtidb.domain.vo.ShopeeNewProductTemplateCompletionVO;
import com.estone.erp.publish.tidb.publishtidb.mapper.ShopeeNewProductTemplateCompletionMapper;
import com.estone.erp.publish.tidb.publishtidb.model.ShopeeNewProductTemplateCompletion;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.File;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;

/**
 * @Description:
 * <AUTHOR>
 * @Date 2025/3/17 17:24
 */
@Slf4j
@Component
public class ShopeeNewProductTemplateCompletionDownload implements DownloadService {

    @Resource
    private ShopeeNewProductTemplateCompletionMapper shopeeNewProductTemplateCompletionMapper;

    @Override
    public String platform() {
        return SaleChannel.CHANNEL_SHOPEE;
    }

    @Override
    public String type() {
        return ShopeeDownloadTypeEnums.NEW_PRODUCT_TEMPLATE_COMPLETION.getType();
    }

    @Override
    public void download(ExcelDownloadLog downloadLog, File temFile) {
        List<ShopeeNewProductTemplateCompletionVO> newShopeeNewProductTemplateCompletionVOList = new ArrayList<>();

        DataContextHolder.setUsername(downloadLog.getCreateBy());
        ShopeeNewProductTemplateCompletionDO search = JSON.parseObject(downloadLog.getQueryCondition(), ShopeeNewProductTemplateCompletionDO.class);

        // 导出数据
        ExcelWriter excelWriter = EasyExcel.write(temFile, ShopeeNewProductTemplateCompletionVO.class).build();
        WriteSheet writeSheet = EasyExcel.writerSheet("sheet").build();

        // 构建参数
        LambdaQueryWrapper<ShopeeNewProductTemplateCompletion> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(com.baomidou.mybatisplus.core.toolkit.ObjectUtils.isNotNull(search.getSaleType()), ShopeeNewProductTemplateCompletion::getSaleType, search.getSaleType());
        lambdaQueryWrapper.ge(com.baomidou.mybatisplus.core.toolkit.ObjectUtils.isNotNull(search.getPushTimeStart()), ShopeeNewProductTemplateCompletion::getPushTime, search.getPushTimeStart());
        lambdaQueryWrapper.le(ObjectUtils.isNotNull(search.getPushTimeEnd()), ShopeeNewProductTemplateCompletion::getPushTime, search.getPushTimeEnd());

        // 分页查询
        int pageIndex = 1;
        int pageSize = 500;
        while (true) {
            IPage<ShopeeNewProductTemplateCompletionVO> pageResult = null;
            Page<ShopeeNewProductTemplateCompletionVO> doPage = new Page<>(pageIndex, pageSize);
            switch (search.getTimeType()) {
                case "day":
                    lambdaQueryWrapper.in(CollectionUtils.isNotEmpty(search.getIdList()), ShopeeNewProductTemplateCompletion::getId, search.getIdList());
                    pageResult = shopeeNewProductTemplateCompletionMapper.selectNewProductTemplateCompletionPage(doPage, lambdaQueryWrapper);
                    break;
                case "week":
                    pageResult = shopeeNewProductTemplateCompletionMapper.getNewProductTemplateCompletionPageByWeek(doPage, lambdaQueryWrapper);
                    break;
                case "month":
                    pageResult = shopeeNewProductTemplateCompletionMapper.getNewProductTemplateCompletionPageByMonth(doPage, lambdaQueryWrapper);
                    break;
                default:
                    throw new IllegalArgumentException("timeType is not valid!");
            }
            pageIndex++;
            if (CollectionUtils.isEmpty(pageResult.getRecords())) {
                break;
            }
            newShopeeNewProductTemplateCompletionVOList.addAll(pageResult.getRecords());
        }

        excelWriter.write(newShopeeNewProductTemplateCompletionVOList, writeSheet);
        excelWriter.finish();
        downloadLog.setDownloadCount(newShopeeNewProductTemplateCompletionVOList.size());
    }

}

package com.estone.erp.publish.shopee.enums;

import com.estone.erp.publish.shopee.component.marking.*;
import lombok.Getter;

/**
 * shopee 营销活动类型
 */
public enum ShopeeMarketingConfigTypeEnum {
    /**
     * 1 店铺活动 2优惠券 3店内秒杀 4关注礼 5折扣促销 6优惠套装 7短视频 8跨境卖家活动 9 竞价活动配置
     */
    CAMPAIGN(1, "店铺活动", CampaignConfigParam.class),
    VOUCHER(2, "优惠券", VoucherConfigParam.class),
    FLASH_SALE(3, "店内秒杀", FlashSaleConfigParam.class),
    FOLLOW_PRIZE(4, "关注礼", FollowPrizeConfigParam.class),
    DISCOUNT(5, "折扣活动", DiscountConfigParam.class),
    BUNDLE_DEAL(6, "优惠套装", BundleDealConfigParam.class),
    SHORT_VIDEO(7, "短视频", ShortVideoConfigParam.class),
    CROSS_BORDER_ACTIVITY(8, "跨境卖家活动配置", CrossBorderActivityParam.class),
    BIDDING_ACTIVITY(9, "竞价活动配置", BiddingActivityParam.class);

    @Getter
    private final Integer code;

    @Getter
    private final String desc;

    @Getter
    private final Class<? extends MarketingConfigParam> jsonClass;

    ShopeeMarketingConfigTypeEnum(Integer code, String desc, Class<? extends MarketingConfigParam> jsonClass) {
        this.code = code;
        this.desc = desc;
        this.jsonClass = jsonClass;
    }

    public static String convert(Integer value) {
        if (value == null) {
            return null;
        }
        for (ShopeeMarketingConfigTypeEnum item : ShopeeMarketingConfigTypeEnum.values()) {
            if (item.getCode().equals(value)) {
                return item.getDesc();
            }
        }
        return null;
    }

    public static ShopeeMarketingConfigTypeEnum convertEnum(Integer value) {
        if (value == null) {
            return null;
        }
        for (ShopeeMarketingConfigTypeEnum item : ShopeeMarketingConfigTypeEnum.values()) {
            if (item.getCode().equals(value)) {
                return item;
            }
        }
        return null;
    }

    public static Class<? extends MarketingConfigParam> getJsonClass(Integer value) {
        if (value == null) {
            return null;
        }
        for (ShopeeMarketingConfigTypeEnum item : ShopeeMarketingConfigTypeEnum.values()) {
            if (item.getCode().equals(value)) {
                return item.getJsonClass();
            }
        }
        return null;
    }

}

package com.estone.erp.publish.tidb.publishtidb.model;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * Shopee 刊登次数组长刊登情况
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-10
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("shopee_new_product_publish_leader")
public class ShopeeNewProductPublishLeader implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 记录唯一标识
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 主表id
     */
    private Long publishId;

    /**
     * 站点
     */
    private String saleLeader;

    /**
     * 总的站点成功率
     */
    private Double percent;

    /**
     * tw的站点成功率
     */
    private Double twPercent;

    /**
     * th的站点成功率
     */
    private Double thPercent;

    /**
     * my的站点成功率
     */
    private Double myPercent;

    /**
     * sg的站点成功率
     */
    private Double sgPercent;

    /**
     * ph的站点成功率
     */
    private Double phPercent;

    /**
     * br的站点成功率
     */
    private Double brPercent;

    /**
     * vn的站点成功率
     */
    private Double vnPercent;

    /**
     * cl的站点成功率
     */
    private Double clPercent;

    /**
     * co的站点成功率
     */
    private Double coPercent;

    /**
     * mx_的站点成功率
     */
    private Double mxPercent;

    /**
     * json 各个站点的刊登情况
     */
    private String json;

    /**
     * 创建时间
     */
    private LocalDateTime createdTime;

    /**
     * 修改时间
     */
    private LocalDateTime updatedTime;


}

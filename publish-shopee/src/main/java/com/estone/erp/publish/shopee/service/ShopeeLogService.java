package com.estone.erp.publish.shopee.service;

import com.estone.erp.common.model.api.CQuery;
import com.estone.erp.common.model.api.CQueryResult;
import com.estone.erp.publish.shopee.model.ShopeeLog;
import com.estone.erp.publish.shopee.model.ShopeeLogCriteria;
import com.estone.erp.publish.shopee.model.ShopeeLogExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR> shopee_log
 * 2019-08-09 16:12:20
 */
public interface ShopeeLogService {
    int countByExample(ShopeeLogExample example);

    CQueryResult<ShopeeLog> search(CQuery<ShopeeLogCriteria> cquery);

    List<ShopeeLog> selectByExample(ShopeeLogExample example);

    ShopeeLog selectByPrimaryKey(Integer id);

    int insert(ShopeeLog record);

    int insertSelective(ShopeeLog record);

    int updateByPrimaryKeySelective(ShopeeLog record);

    int updateByExampleSelective(ShopeeLog record, ShopeeLogExample example);

    int deleteByPrimaryKey(Integer id);

    int deleteByExample(ShopeeLogExample example);
}
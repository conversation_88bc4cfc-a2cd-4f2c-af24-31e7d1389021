package com.estone.erp.publish.tidb.publishtidb.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.estone.erp.publish.shopee.converter.*;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * @Description: Shopee跨境卖家活动VO
 * <AUTHOR>
 * @Date 2025/4/15 18:29
 */
@Data
public class ShopeeActivityRegistrationVO {

    @JsonSerialize(using = ToStringSerializer.class)
    @ExcelIgnore
    private Long id;

    /**
     * 店铺账号
     */
    @ExcelProperty("店铺账号")
    private String accountNumber;

    /**
     * 规则名称
     */
    @ExcelProperty("规则名称")
    private String ruleName;

    /**
     * 子账号，取最新一条子账号
     */
    @ExcelProperty("子账号")
    private String subAccount;

    /**
     * 商家ID
     */
    @ExcelProperty("商家ID")
    private Long merchantId;

    /**
     * 商家
     */
    @ExcelProperty("商家")
    private String merchantName;

    /**
     * 可报名店铺
     */
    @ExcelProperty("可报名店铺")
    private String eligibleShop;

    /**
     * 店铺ID
     */
    @ExcelProperty("店铺ID")
    private Long shopId;

    /**
     * 站点
     */
    @ExcelProperty("站点")
    private String site;

    /**
     * 店铺分组名称
     */
    @ExcelProperty("店铺分组名称")
    private String accountGroupName;

    /**
     * 报名ID
     */
    @ExcelProperty("报名ID")
    private Long registrationId;

    /**
     * 活动名称
     */
    @ExcelProperty("活动名称")
    private String activityName;

    /**
     * 已报名数
     */
    @ExcelProperty("已报名数")
    private Integer registrationRate;

    /**
     * 总报名数
     */
    @ExcelProperty("总报名数")
    private Integer registrationTotal;

    /**
     * 报名进度
     */
    @ExcelProperty("报名进度")
    private BigDecimal registrationProgress;

    /**
     * 报名方式（0: 商品确认, 1: 商品议价, 2: 自主报名）
     * @see com.estone.erp.publish.shopee.enums.ShopeeRegistrationMethodEnum
     */
    @ExcelProperty(value = "报名方式", converter = ShopeeRegistrationMethodConverter.class)
    private Integer registrationMethod;

    /**
     * 报名活动类型（0: Non-SIP, 1: SIP）
     * @see com.estone.erp.publish.shopee.enums.ShopeeActivityTypeEnum
     */
    @ExcelProperty(value = "报名活动类型", converter = ShopeeActivityTypeConverter.class)
    private Integer activityType;

    /**
     * 报名描述
     */
    @ExcelProperty("报名描述")
    private String registrationDescription;

    /**
     * 状态（0: 正在进行, 1: 即将到来, 2: 已结束）
     * @see com.estone.erp.publish.shopee.enums.ShopeeRegistrationStatusEnum
     */
    @ExcelProperty(value = "状态", converter = ShopeeRegistrationStatusConverter.class)
    private Integer status;

    /**
     * 优先级（0: 低, 1: 中, 2: 高）
     * @see com.estone.erp.publish.shopee.enums.ShopeeRegistrationPriorityEnum
     */
    @ExcelProperty(value = "优先级", converter = ShopeeRegistrationPriorityConverter.class)
    private Integer priority;

    /**
     * 活动开始时间
     */
    @ExcelProperty("活动开始时间")
    private LocalDateTime activityStartTime;

    /**
     * 活动结束时间
     */
    @ExcelProperty("活动结束时间")
    private LocalDateTime activityEndTime;

    /**
     * 可报名商品信息，存储下载链接
     */
    @ExcelProperty("可报名商品信息")
    private String eligibleProductInfo;

    /**
     * 生成的报名文件路径或URL
     */
    @ExcelProperty("生成的报名文件")
    private String generatedFile;

    /**
     * 生成报名文件失败原因
     */
    @ExcelProperty("生成报名文件失败原因")
    private String generationFailureReason;

    /**
     * 上传状态（0: 待上传, 1: 上传中, 2: 上传成功, 3: 上传失败, 4: 无需上传, 5: 部分成功）
     * @see com.estone.erp.publish.shopee.enums.ShopeeUploadStatusEnum
     */
    @ExcelProperty(value = "上传状态", converter = ShopeeUploadStatusConverter.class)
    private Integer uploadStatus;

    /**
     * 上传失败备注
     */
    @ExcelProperty("上传失败备注")
    private String failureRemark;

    /**
     * 销售
     */
    @ExcelProperty("销售")
    private String sale;

    /**
     * 销售组长
     */
    @ExcelProperty("销售组长")
    private String saleLeader;

    /**
     * 销售主管
     */
    @ExcelProperty("销售主管")
    private String salesSupervisor;

    /**
     * 采集时间
     */
    @ExcelProperty("采集时间")
    private LocalDateTime crawlTime;

    /**
     * 更新时间
     */
    @ExcelProperty("更新时间")
    private LocalDateTime updatedTime;

    /**
     * 生成报名文件时间
     */
    @ExcelProperty("生成报名文件时间")
    private LocalDateTime generationTime;

    /**
     * 上传报名文件时间
     */
    @ExcelProperty("上传报名文件时间")
    private LocalDateTime uploadTime;

    /**
     * 创建时间
     */
    @ExcelProperty("创建时间")
    private LocalDateTime createdTime;

}

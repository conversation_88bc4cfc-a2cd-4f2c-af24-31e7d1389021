package com.estone.erp.publish.tidb.publishtidb.controller;


import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.publish.tidb.publishtidb.domain.vo.ShopeeOfflineRecordConfigVO;
import com.estone.erp.publish.tidb.publishtidb.service.UnsalableSkuService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

/**
* <p>
*  前端控制器
* </p>
*
* <AUTHOR>
* @since 2025-04-01
*/
@RestController
@RequestMapping("/unsalableSku")
public class UnsalableSkuController {

    @Autowired
    private UnsalableSkuService unsalableSkuService;

    /**
     * 获取单品滞销程度
     */
    @PostMapping("/getSkuUnsalableLevelMap")
    public ApiResult<Map<String, Integer>> getSkuUnsalableLevelMap(@RequestBody List<String> skuList) {
        Map<String, Integer> skuUnsalableLevelMap = unsalableSkuService.getSkuUnsalableLevelMap(skuList);
        return ApiResult.newSuccess(skuUnsalableLevelMap);
    }

}

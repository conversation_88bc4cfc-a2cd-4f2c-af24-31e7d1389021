package com.estone.erp.publish.tidb.publishtidb.service.impl;

import com.alibaba.nacos.common.utils.CollectionUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.estone.erp.publish.shopee.enums.ShopeeNewProductPublishStatusEnum;
import com.estone.erp.publish.shopee.model.ShopeeAccountConfig;
import com.estone.erp.publish.tidb.publishtidb.mapper.ShopeeNewProductSitesMapper;
import com.estone.erp.publish.tidb.publishtidb.model.ShopeeNewProductSites;
import com.estone.erp.publish.tidb.publishtidb.service.ShopeeNewProductSitesService;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 商品站点信息表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-05
 */
@Service
public class ShopeeNewProductSitesServiceImpl extends ServiceImpl<ShopeeNewProductSitesMapper, ShopeeNewProductSites> implements ShopeeNewProductSitesService {

    @Override
    public Map<Long, List<ShopeeNewProductSites>> getProductSitesByMainId(List<Long> mainId) {
        if (mainId == null || mainId.isEmpty()) {
            return Map.of();
        }
        LambdaQueryWrapper<ShopeeNewProductSites> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(ShopeeNewProductSites::getProductId, mainId);
        List<ShopeeNewProductSites> list = list(wrapper);
        return list.stream().collect(Collectors.groupingBy(ShopeeNewProductSites::getProductId, Collectors.toList()));
    }

    @Override
    public void updateProductSites(List<ShopeeNewProductSites> shopeeNewProductSites, Map<String, Set<String>> skuAndProductSitesMap) {
        Collection<Set<String>> values = skuAndProductSitesMap.values();
        for (ShopeeNewProductSites shopeeNewProductSite : shopeeNewProductSites) {
            String siteCode = shopeeNewProductSite.getSiteCode();
            boolean b = values.stream().allMatch(a -> a.contains(siteCode.toUpperCase()));
            shopeeNewProductSite.setIsBanned(b);
        }
        List<Long> bandList = shopeeNewProductSites.stream().filter(ShopeeNewProductSites::getIsBanned).map(ShopeeNewProductSites::getId).collect(Collectors.toList());
        List<Long> noBandList = shopeeNewProductSites.stream().filter(a -> !a.getIsBanned()).map(ShopeeNewProductSites::getId).collect(Collectors.toList());
        LocalDateTime now = LocalDateTime.now();
        if (CollectionUtils.isNotEmpty(bandList)) {
            LambdaQueryWrapper<ShopeeNewProductSites> bandWrapper = new LambdaQueryWrapper<>();
            bandWrapper.in(ShopeeNewProductSites::getId, bandList);
            ShopeeNewProductSites bandUpdateEntity = new ShopeeNewProductSites();
            bandUpdateEntity.setIsBanned(true);
            bandUpdateEntity.setUpdatedTime(now);
            update(bandUpdateEntity, bandWrapper);
        }
        if (CollectionUtils.isNotEmpty(noBandList)) {
            LambdaQueryWrapper<ShopeeNewProductSites> noBandWrapper = new LambdaQueryWrapper<>();
            noBandWrapper.in(ShopeeNewProductSites::getId, noBandList);
            ShopeeNewProductSites bandUpdateEntity = new ShopeeNewProductSites();
            bandUpdateEntity.setIsBanned(false);
            bandUpdateEntity.setUpdatedTime(now);
            update(bandUpdateEntity, noBandWrapper);
        }
    }

    /**
     * 更新新产品模板(一个模版站点数据只会绑定一个新品数据)
     *
     * @param newProductId
     * @param templateId
     * @param accList
     */
    @Override
    public void updateNewProductByGlobalTemplate(Long newProductId, Long templateId, List<ShopeeAccountConfig> accList) {
        if (Objects.nonNull(newProductId) && Objects.nonNull(templateId) && CollectionUtils.isNotEmpty(accList)) {
            String site = accList.get(0).getSite().toUpperCase();
            List<Integer> codes = List.of(ShopeeNewProductPublishStatusEnum.PUBLISH_SUCCESS.getCode(), ShopeeNewProductPublishStatusEnum.NOT_COMPLETED.getCode());

            // 解绑数据
            List<ShopeeNewProductSites> shopeeNewProductSite = list(
                    new LambdaQueryWrapper<ShopeeNewProductSites>().eq(ShopeeNewProductSites::getTemplateId, templateId).notIn(ShopeeNewProductSites::getPublishStatus, codes)
            );
            if (CollectionUtils.isNotEmpty(shopeeNewProductSite)) {
                // 覆盖数据
                List<Long> ids = shopeeNewProductSite.stream().map(ShopeeNewProductSites::getId).collect(Collectors.toList());
                update(
                        new LambdaUpdateWrapper<ShopeeNewProductSites>()
                                .in(ShopeeNewProductSites::getId, ids)
                                .set(ShopeeNewProductSites::getTemplateId, null)
                                .set(ShopeeNewProductSites::getPublishSuccessTime, null)
                                .set(ShopeeNewProductSites::getPublishStatus, ShopeeNewProductPublishStatusEnum.NO_TEMPLATE.getCode())
                );
            }

            // 重新绑定数据
            ShopeeNewProductSites newProductSite = getOne(
                    new LambdaQueryWrapper<ShopeeNewProductSites>()
                            .eq(ShopeeNewProductSites::getProductId, newProductId)
                            .eq(ShopeeNewProductSites::getSiteCode, site)
                            .in(ShopeeNewProductSites::getPublishStatus, codes)
            );
            if (Objects.isNull(newProductSite)) {
                LambdaUpdateWrapper<ShopeeNewProductSites> sitesLambdaUpdateWrapper = new LambdaUpdateWrapper<>();
                sitesLambdaUpdateWrapper.eq(ShopeeNewProductSites::getProductId, newProductId);
                sitesLambdaUpdateWrapper.eq(ShopeeNewProductSites::getSiteCode, site);
                sitesLambdaUpdateWrapper.set(ShopeeNewProductSites::getTemplateId, templateId);
                sitesLambdaUpdateWrapper.set(ShopeeNewProductSites::getPublishSuccessTime, null);
                sitesLambdaUpdateWrapper.set(ShopeeNewProductSites::getPublishStatus, ShopeeNewProductPublishStatusEnum.PUBLISHING.getCode());
                update(sitesLambdaUpdateWrapper);
            }
        }
    }
}

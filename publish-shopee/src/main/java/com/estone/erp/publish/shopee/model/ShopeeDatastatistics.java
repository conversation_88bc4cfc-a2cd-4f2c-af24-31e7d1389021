package com.estone.erp.publish.shopee.model;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class ShopeeDatastatistics implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键 database column shopee_data_statistics.id
     */
    private Integer id;

    /**
     * 类型：1.范本  2.Listing  3.mtsku范本  4.新在线listing  5.mtsku刊登成功率
     */
    private Integer type;

    /**
     * 账号 database column shopee_data_statistics.account
     */
    private String account;

    /**
     * 账号对应站点 database column shopee_data_statistics.site
     */
    private String site;

    /**
     * 账号对应销售人员的id database column shopee_data_statistics.user_id
     */
    private String userId;

    /**
     * 新增范本/Listing数量 database column shopee_data_statistics.amount
     */
    private Integer amount;

    /**
     * 发货时间在两天内的Listing数量 database column shopee_data_statistics.shipping_date_within_two_amount
     */
    private Integer shippingDateWithinTwoAmount;

    /**
     * 状态为"normal"的Listing的数量 database column shopee_data_statistics.normal_amount
     */
    private Integer normalAmount;

    /**
     * 状态为"banned"的Listing的数量 database column shopee_data_statistics.banned_amount
     */
    private Integer bannedAmount;

    /**
     * 状态为"unlist"的Listing的数量 database column shopee_data_statistics.unlist_amount
     */
    private Integer unlistAmount;

    /**
     * 统计日期 database column shopee_data_statistics.statistics_date
     */
    private Date statisticsDate;

    /**
     * 子账号
     */
    private String subAccount;

    /**
     * 刊登率
     */
    private Double publishRate;

    /**
     * 系统刊登数
     */
    private Integer systemAmount;

    /**
     * 销售刊登数
     */
    private Integer saleAmount;
}
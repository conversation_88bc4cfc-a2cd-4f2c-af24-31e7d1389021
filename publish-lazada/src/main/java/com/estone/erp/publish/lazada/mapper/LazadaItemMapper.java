package com.estone.erp.publish.lazada.mapper;

import com.estone.erp.publish.lazada.bo.LazadaItemBo;
import com.estone.erp.publish.lazada.model.LazadaItem;
import com.estone.erp.publish.lazada.model.LazadaItemExample;
import org.apache.ibatis.annotations.Param;

import java.sql.Timestamp;
import java.util.Collection;
import java.util.List;

public interface LazadaItemMapper {
    int countByExample(LazadaItemExample example);

    int deleteByPrimaryKey(List<Long> ids);

    int insert(LazadaItem record);

    LazadaItem selectByPrimaryKey(Long id);

    List<LazadaItem> selectByExample(LazadaItemExample example);

    List<LazadaItem> selectSellerSkuByExample(LazadaItemExample example);

    List<LazadaItemBo> selectInfoByExample(LazadaItemExample example);

    List<LazadaItemBo> selectSimpleInfoByExample(LazadaItemExample example);

    int updateByExampleSelective(@Param("record") LazadaItem record, @Param("example") LazadaItemExample example);

    int updateByPrimaryKeySelective(LazadaItem record);

    int insertBatch(@Param("list") Collection<LazadaItem> list);

    int deleteByExample(LazadaItemExample example);

    int batchUpdateBySelective(@Param("list")List<LazadaItem> list);

    int batchUpdateByUnique(@Param("list")List<LazadaItem> list);

    int selectUnqualifiedSkuCount(@Param("accountNumberList") List<String> accountNumberList,
                                  @Param("articleNumberList") List<String> articleNumberList,
                                  @Param("addSkuStatusTime") Boolean addSkuStatusTime);

    /**
     * 分页查询停产、存档的sku
     * @param accountNumberList
     * @param articleNumberList
     * @param example
     * @return
     */
    List<LazadaItemBo> selectUnqualifiedSkuInfo(@Param("accountNumberList")List<String> accountNumberList,
                                              @Param("articleNumberList") List<String> articleNumberList,
                                              @Param("example") LazadaItemExample example,
                                              @Param("addSkuStatusTime") Boolean addSkuStatusTime);

    List<LazadaItemBo> selectItemInfoByExample(LazadaItemExample itemExample);

    List<LazadaItemBo> selectItemInfoByCustom(@Param("accountNumber") String accountNumber,
                                              @Param("skuStatusList") List<Integer> skuStatusList,
                                              @Param("type") String type,
                                              @Param("ltDay")Timestamp ltDay);

    List<LazadaItemBo> selectItemInfoByCustomV3(@Param("accountNumber") String accountNumber,
                                              @Param("skuStatusList") List<Integer> skuStatusList,
                                              @Param("type") String type,
                                              @Param("ltDay")Timestamp ltDay);


    void bathUpdateGrossByPrimaryKey(@Param("items") List<LazadaItem> items);

    List<LazadaItem> listItemByIdThanNum(@Param("startId") long startId, @Param("size") int size);

    List<LazadaItem> listItemById(@Param("ids") List<Long> ids);
}
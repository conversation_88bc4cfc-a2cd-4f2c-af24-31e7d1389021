package com.estone.erp.publish.lazada.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.estone.erp.publish.lazada.constant.LazadaPublishRouteApi;
import com.estone.erp.publish.lazada.lazop.api.LazopClient;
import com.estone.erp.publish.lazada.lazop.api.LazopRequest;
import com.estone.erp.publish.lazada.lazop.api.LazopResponse;
import com.estone.erp.publish.lazada.lazop.util.Constants;
import com.estone.erp.publish.system.account.SaleAccountAndBusinessResponse;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2021/7/20 18:38
 * @description
 */
@Slf4j
public class LazadaProductItemListUtil {


    public static LazopResponse getItemList(SaleAccountAndBusinessResponse account) {
        LazopClient client = LazadaHttpClientUtil.createClient(account);
        LazopRequest request = new LazopRequest();
        request.setHttpMethod(Constants.METHOD_GET);
        request.setApiName(LazadaPublishRouteApi.getSellerItemLimit);
        return LazadaHttpClientUtil.execute(client, request, account.getAccessToken(), 3);
    }

    public static int parseItemList( LazopResponse lazopResponse) {
        String body = lazopResponse.getBody();
        JSONObject json = JSON.parseObject(body);
        if(json != null){
            JSONObject data = json.getJSONObject("data");
            if(data != null){
                Integer onlineItemCount = data.getInteger("onlineItemCount");
                Integer itemLimit = data.getInteger("itemLimit");
                if(onlineItemCount != null && itemLimit != null){
                    return itemLimit - onlineItemCount;
                }
            }
        }

        return -1;
    }
}

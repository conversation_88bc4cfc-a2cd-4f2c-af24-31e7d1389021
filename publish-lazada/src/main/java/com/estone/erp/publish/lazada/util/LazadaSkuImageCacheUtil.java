package com.estone.erp.publish.lazada.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.util.CommonUtils;
import com.estone.erp.common.util.SpringUtils;
import com.estone.erp.publish.elasticsearch.model.EsLazadaSkuImage;
import com.estone.erp.publish.elasticsearch.service.EsLazadaSkuImageService;
import com.estone.erp.publish.lazada.call.LazadaImageUploadCall;
import com.estone.erp.publish.lazada.lazop.api.LazopResponse;
import com.estone.erp.publish.system.account.SaleAccountAndBusinessResponse;
import lombok.extern.slf4j.Slf4j;

import java.util.*;

/**
 * <AUTHOR>
 * @date 2022/6/15 11:20
 * @description sku图片缓存工具类
 */
@Slf4j
public class LazadaSkuImageCacheUtil {

    private static EsLazadaSkuImageService esLazadaSkuImageService = SpringUtils.getBean(EsLazadaSkuImageService.class);;

    /**
     * 查询sku es图片
     * @param mainSku
     * @return
     */
    public static Map<String, String> queryEsImages(String mainSku) {
        return esLazadaSkuImageService.queryEsImages(CommonUtils.arrayAsList(mainSku));
    }

    public static Map<String, String> queryEsImages(List<String> mainSkuList) {
        return esLazadaSkuImageService.queryEsImages(mainSkuList);
    }

    /**
     * 上传图片并保存到es
     * @param mainSku
     * @param imgUrl
     * @param account
     */
    public static ApiResult<Map<String, String>> uploadImage(String mainSku, String imgUrl, SaleAccountAndBusinessResponse account) {
        return uploadImage(mainSku, CommonUtils.arrayAsList(imgUrl), account);
    }

    /**
     * 上传图片并保存到es
     * @param mainSku
     * @param imgUrlList
     * @param account
     */
    public static ApiResult<Map<String, String>> uploadImage(String mainSku, List<String> imgUrlList, SaleAccountAndBusinessResponse account) {
        List<EsLazadaSkuImage> esList = new ArrayList<>(imgUrlList.size());
        Map<String, String> imgMappingMap = new HashMap<>(imgUrlList.size());

        ApiResult<Map<String, String>> mapApiResult = null;
        for (String imgUrl : imgUrlList) {
            ApiResult<LazopResponse> apiResult = LazadaImageUploadCall.uploadImagePlat(account, imgUrl);
            if(!apiResult.isSuccess()){
                mapApiResult = ApiResult.newError(apiResult.getErrorMsg());
                break;
            }else{
                LazopResponse response = apiResult.getResult();
                String body = response.getBody();
                JSONObject jsonObject = JSON.parseObject(body);
                JSONObject imageJson = jsonObject.getJSONObject("data").getJSONObject("image");
                String platUrl = imageJson.getString("url");
                String hash_code = imageJson.getString("hash_code");

                imgMappingMap.put(imgUrl, platUrl);

                // 构建es对象
                EsLazadaSkuImage esImage = new EsLazadaSkuImage();
                if(mainSku.contains("-")){
                    mainSku = mainSku.substring(0, mainSku.indexOf("-"));
                }
                mainSku= mainSku.toUpperCase();

                String id = imgUrl;
                if(id.contains("/")){
                    id = id.substring(id.lastIndexOf("/") + 1).replace("-", "_").replace(".", "_");
                }
                esImage.setId(id);
                esImage.setMainSku(mainSku);
                esImage.setOriginImage(imgUrl);
                esImage.setPlatUrl(platUrl);
                esImage.setImageHashCode(hash_code);
                esImage.setAccount(account.getAccountNumber());
                esImage.setCreateDate(new Date());

                esList.add(esImage);
            }
        }

        // 保存es
        if(esList.size() > 0){
            try {
                esLazadaSkuImageService.saveAll(esList);
            }catch (Exception e){
                log.error(String.format("sku:%s 图片保存es出错", mainSku), e);
            }
        }

        if(mapApiResult != null){
            mapApiResult.setResult(imgMappingMap);
            return mapApiResult;
        }

        return ApiResult.newSuccess(imgMappingMap);
    }

}

package com.estone.erp.publish.lazada.util;

import com.estone.erp.common.constant.StrConstant;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021/2/24 11:28
 * @description
 */
@Slf4j
public class LazadaAccountUtil {

    /**
     * 截取 - 前面的内容，并拼接 -MY
     *
     * @param accountNumber 原始字符串
     * @return 处理后的字符串
     */
    public static String processAsMyAccount(String accountNumber) {
        if (StringUtils.isBlank(accountNumber)) {
            return accountNumber; // 或者抛异常，看你需求
        }

        String beforeDash = accountNumber.split("-")[0];
        return beforeDash + "-MY";
    }
    /**
     * 根据店铺账号得到关联销售
     * @param accountNumber
     * @param lazadaSalemanMap
     * @return
     */
    public static String getUsercodeByAccount(String accountNumber, Map<String, List<String>> lazadaSalemanMap){
        String usercode = StrConstant.ADMIN;
        if(lazadaSalemanMap != null && lazadaSalemanMap.size() > 0){
            for (Map.Entry<String, List<String>> entry : lazadaSalemanMap.entrySet()) {
                if(entry.getKey().equalsIgnoreCase(accountNumber)){
                    List<String> salemanList = entry.getValue();
                    if(CollectionUtils.isNotEmpty(salemanList)) {
                        String user = salemanList.get(0);
                        String[] idName = user.split("-");
                        usercode = idName[0];
                        break;
                    }
                }
            }
        }

        return usercode;
    }
}

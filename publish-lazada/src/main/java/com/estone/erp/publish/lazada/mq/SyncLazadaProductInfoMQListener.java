package com.estone.erp.publish.lazada.mq;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.estone.erp.common.mq.PublishQueues;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.elasticsearch2.model.EsLazadaItem;
import com.estone.erp.publish.elasticsearch2.model.beanrequest.EsLazadaItemRequest;
import com.estone.erp.publish.lazada.component.LazadaSyncEsItemListingHelper;
import com.estone.erp.publish.lazada.component.SyncProductInfoBulkProcessor;
import com.estone.erp.publish.lazada.service.LazadaItemEsService;
import com.estone.erp.publish.mq.bean.ChangeSku;
import com.estone.erp.publish.mq.util.ChangeSkuConsumerUtils;
import com.estone.erp.publish.platform.model.ChangeSkuLog;
import com.estone.erp.publish.platform.service.ChangeSkuLogService;
import com.estone.erp.publish.system.product.ProductInfoVO;
import com.estone.erp.publish.system.product.ProductUtils;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.StopWatch;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2022-08-21 9:45
 */
@Slf4j
@Component
public class SyncLazadaProductInfoMQListener {

    @Resource
    private ChangeSkuLogService changeSkuLogService;
    @Autowired
    private LazadaItemEsService lazadaItemEsService;
    @Autowired
    private LazadaSyncEsItemListingHelper lazadaSyncEsItemListingHelper;
    @Autowired
    private SyncProductInfoBulkProcessor syncProductInfoBulkProcessor;


    private static final int MAX_RETRY_COUNT = 3;


    @RabbitListener(queues = PublishQueues.LAZADA_SYNC_PRODUCT_INFO_QUEUE, containerFactory = "batchAntifollowConsumeFactory")
    public void syncLazadaProductInfo(Message message, Channel channel) throws IOException {
        StopWatch stopWatch = StopWatch.createStarted();
        boolean consumerFlag = false;
        Exception error = null;
        try {
            // 获取消息体
            String body = new String(message.getBody(), StandardCharsets.UTF_8);
            if (StringUtils.isBlank(body)) {
                return;
            }

            ChangeSku changeSku;
            try {
                changeSku = JSON.parseObject(body, new TypeReference<ChangeSku>() {
                });
            } catch (Exception e) {
                log.error("解析mq消息体异常 -> {}", body);
                return;
            }

            Boolean isSuccess = executeUpdate(changeSku);
            consumerFlag = isSuccess;
            if(isSuccess) {
                // 确认消息并删除redis重试次数
                ChangeSkuConsumerUtils.confirmAndDelete(channel, message);
            } else {
                // 重试
                ChangeSkuConsumerUtils.retry(channel, message, MAX_RETRY_COUNT, SaleChannel.CHANNEL_LAZADA);
            }
        } catch (Exception e) {
            error = e;
            // 重试
            ChangeSkuConsumerUtils.retry(channel, message, MAX_RETRY_COUNT, SaleChannel.CHANNEL_LAZADA);
        }finally {
            log.info("同步产品信息变更完成，消费状态：{}，耗时：{}, error: {}", consumerFlag, stopWatch, error == null ? "" : error.getMessage());
        }
    }

    private Boolean executeUpdate(ChangeSku changeSku) {
        List<String> skuList = changeSku.getSkuList();
        if (CollectionUtils.isEmpty(skuList)) {
            log.error("更新在线列表单品状态信息存在sku为空的数据" + JSON.toJSONString(changeSku));
            return true;
        }

        List<String> accountNumberList = changeSku.getAccountNumberList();
        syncProductInfo(skuList, accountNumberList);

        // 如果日志id不为空 修改日志状态
        Long logId = changeSku.getLogId();
        if (null != logId) {
            // 修改日志状态
            ChangeSkuLog changeSkuLog = new ChangeSkuLog();
            changeSkuLog.setId(logId.intValue());
            changeSkuLog.setStatus(1);
            changeSkuLogService.updateByPrimaryKeySelective(changeSkuLog);
        }

        return true;
    }

    public void syncProductInfo(List<String> skuList, List<String> accountNumberList) {
        if(CollectionUtils.isEmpty(skuList)) {
            return;
        }

        Map<String, ProductInfoVO> map = new HashMap<>();
        EsLazadaItemRequest request = new EsLazadaItemRequest();
        request.setFields(new String[]{"sku","id"});
        request.setSku(StringUtils.join(skuList, ","));
        if (CollectionUtils.isNotEmpty(accountNumberList)) {
            request.setAccounts(accountNumberList);
        }

        List<EsLazadaItem> lazadaItems = new ArrayList<>();
        int pageSize = 1000;
        int pageIndex = 0;
        while (true) {
            Page<EsLazadaItem> page = lazadaItemEsService.pageList(request, pageIndex, pageSize);
            if (CollectionUtils.isEmpty(page.getContent())) {
                break;
            }
            lazadaItems.addAll(page.getContent());
            pageIndex ++;
        }

        if (CollectionUtils.isEmpty(lazadaItems)) {
            return;
        }
        lazadaItems.forEach(esLazadaItem -> {
            // 查询产品信息
            ProductInfoVO productInfoVO = map.get(esLazadaItem.getSku());
            if (ObjectUtils.isEmpty(productInfoVO)) {
                productInfoVO = ProductUtils.getSkuInfo(esLazadaItem.getSku());
                map.put(esLazadaItem.getSku(), productInfoVO);
            }
            if (ObjectUtils.isEmpty(productInfoVO) || StringUtils.isBlank(productInfoVO.getSonSku())) {
                return;
            }
            lazadaSyncEsItemListingHelper.setProductInfo(esLazadaItem, productInfoVO);
            esLazadaItem.setUpdateDate(new Date());
        });
        syncProductInfoBulkProcessor.syncHandler(lazadaItems);
    }


}

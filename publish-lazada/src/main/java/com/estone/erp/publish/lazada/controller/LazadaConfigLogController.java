package com.estone.erp.publish.lazada.controller;

import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.model.api.CQuery;
import com.estone.erp.publish.lazada.model.LazadaConfigLogCriteria;
import com.estone.erp.publish.lazada.service.LazadaConfigLogService;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * 2025-03-26 15:53:16
 */
@RestController
@RequestMapping("lazadaConfigLog")
public class LazadaConfigLogController {

    @Resource
    private LazadaConfigLogService lazadaConfigLogService;

    @GetMapping("/getConfigLog/{logType}/{id}/{offset}/{limit}")
    public ApiResult<?> getConfigLog(
            @PathVariable(value = "logType", required = true) Integer logType,
            @PathVariable(value = "id", required = true) Integer id,
            @PathVariable(value = "offset", required = false) Integer offset,
            @PathVariable(value = "limit", required = false) Integer limit
    ) {
        LazadaConfigLogCriteria configLogCriteria = new LazadaConfigLogCriteria();
        configLogCriteria.setConfigId(id);
        configLogCriteria.setType(logType);
        CQuery<LazadaConfigLogCriteria> cquery = new CQuery<>();
        cquery.setLimit(limit);
        cquery.setPageReqired(true);
        cquery.setOffset(offset);
        cquery.setSearch(configLogCriteria);
        return lazadaConfigLogService.search(cquery);
    }
}
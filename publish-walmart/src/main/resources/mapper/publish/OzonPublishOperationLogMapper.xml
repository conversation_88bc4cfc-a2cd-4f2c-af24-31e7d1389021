<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.estone.erp.publish.ozon.mapper.OzonPublishOperationLogMapper" >
  <resultMap id="BaseResultMap" type="com.estone.erp.publish.ozon.model.OzonPublishOperationLog" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="mod_id" property="modId" jdbcType="VARCHAR" />
    <result column="op_type" property="opType" jdbcType="VARCHAR" />
    <result column="user" property="user" jdbcType="VARCHAR" />
    <result column="ref_key" property="refKey" jdbcType="VARCHAR" />
    <result column="ref_key1" property="refKey1" jdbcType="VARCHAR" />
    <result column="state" property="state" jdbcType="TINYINT" />
    <result column="meta_obj" property="metaObj" jdbcType="CHAR" />
    <result column="before_obj" property="beforeObj" jdbcType="CHAR" />
    <result column="after_obj" property="afterObj" jdbcType="CHAR" />
    <result column="created_time" property="createdTime" jdbcType="TIMESTAMP" />
    <result column="remark" property="remark" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    id, mod_id, op_type, `user`, ref_key, ref_key1, `state`, meta_obj, before_obj, after_obj, 
    created_time, remark
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.estone.erp.publish.ozon.model.OzonPublishOperationLogExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from ozon_publish_operation_log
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
    <if test="limit != null" >
      <if test="offset != null" >
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null" >
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from ozon_publish_operation_log
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" >
    delete from ozon_publish_operation_log
    where id IN 
    <foreach collection="list" item="listItem" open="(" close=")" separator="," >
      #{listItem}
    </foreach>
  </delete>
  <insert id="insert" parameterType="com.estone.erp.publish.ozon.model.OzonPublishOperationLog" >
    <selectKey resultType="java.lang.Long" keyProperty="id" order="AFTER" >
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into ozon_publish_operation_log (mod_id, op_type, `user`, 
      ref_key, ref_key1, `state`, 
      meta_obj, before_obj, after_obj, 
      created_time, remark)
    values (#{modId,jdbcType=VARCHAR}, #{opType,jdbcType=VARCHAR}, #{user,jdbcType=VARCHAR}, 
      #{refKey,jdbcType=VARCHAR}, #{refKey1,jdbcType=VARCHAR}, #{state,jdbcType=TINYINT}, 
      #{metaObj,jdbcType=CHAR}, #{beforeObj,jdbcType=CHAR}, #{afterObj,jdbcType=CHAR}, 
      #{createdTime,jdbcType=TIMESTAMP}, #{remark,jdbcType=VARCHAR})
  </insert>
  <select id="countByExample" parameterType="com.estone.erp.publish.ozon.model.OzonPublishOperationLogExample" resultType="java.lang.Integer" >
    select count(*) from ozon_publish_operation_log
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update ozon_publish_operation_log
    <set >
      <if test="record.id != null" >
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.modId != null" >
        mod_id = #{record.modId,jdbcType=VARCHAR},
      </if>
      <if test="record.opType != null" >
        op_type = #{record.opType,jdbcType=VARCHAR},
      </if>
      <if test="record.user != null" >
        `user` = #{record.user,jdbcType=VARCHAR},
      </if>
      <if test="record.refKey != null" >
        ref_key = #{record.refKey,jdbcType=VARCHAR},
      </if>
      <if test="record.refKey1 != null" >
        ref_key1 = #{record.refKey1,jdbcType=VARCHAR},
      </if>
      <if test="record.state != null" >
        `state` = #{record.state,jdbcType=TINYINT},
      </if>
      <if test="record.metaObj != null" >
        meta_obj = #{record.metaObj,jdbcType=CHAR},
      </if>
      <if test="record.beforeObj != null" >
        before_obj = #{record.beforeObj,jdbcType=CHAR},
      </if>
      <if test="record.afterObj != null" >
        after_obj = #{record.afterObj,jdbcType=CHAR},
      </if>
      <if test="record.createdTime != null" >
        created_time = #{record.createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.remark != null" >
        remark = #{record.remark,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.estone.erp.publish.ozon.model.OzonPublishOperationLog" >
    update ozon_publish_operation_log
    <set >
      <if test="modId != null" >
        mod_id = #{modId,jdbcType=VARCHAR},
      </if>
      <if test="opType != null" >
        op_type = #{opType,jdbcType=VARCHAR},
      </if>
      <if test="user != null" >
        `user` = #{user,jdbcType=VARCHAR},
      </if>
      <if test="refKey != null" >
        ref_key = #{refKey,jdbcType=VARCHAR},
      </if>
      <if test="refKey1 != null" >
        ref_key1 = #{refKey1,jdbcType=VARCHAR},
      </if>
      <if test="state != null" >
        `state` = #{state,jdbcType=TINYINT},
      </if>
      <if test="metaObj != null" >
        meta_obj = #{metaObj,jdbcType=CHAR},
      </if>
      <if test="beforeObj != null" >
        before_obj = #{beforeObj,jdbcType=CHAR},
      </if>
      <if test="afterObj != null" >
        after_obj = #{afterObj,jdbcType=CHAR},
      </if>
      <if test="createdTime != null" >
        created_time = #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="remark != null" >
        remark = #{remark,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>

  <insert id="batchInsert">
    insert into ozon_publish_operation_log (mod_id, op_type, `user`,
    ref_key, ref_key1, `state`,
    meta_obj, before_obj, after_obj,
    created_time, remark)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.modId,jdbcType=VARCHAR}, #{item.opType,jdbcType=VARCHAR}, #{item.user,jdbcType=VARCHAR},
      #{item.refKey,jdbcType=VARCHAR}, #{item.refKey1,jdbcType=VARCHAR}, #{item.state,jdbcType=TINYINT},
      #{item.metaObj,jdbcType=CHAR}, #{item.beforeObj,jdbcType=CHAR}, #{item.afterObj,jdbcType=CHAR},
      #{item.createdTime,jdbcType=TIMESTAMP}, #{item.remark,jdbcType=VARCHAR})
    </foreach>
  </insert>
</mapper>
<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.estone.erp.publish.ozon.mapper.OzonCategoryAttributeMapper" >
  <resultMap id="BaseResultMap" type="com.estone.erp.publish.ozon.model.OzonCategoryAttribute" >
    <id column="id" property="id" jdbcType="INTEGER" />
    <result column="category_id" property="categoryId" jdbcType="INTEGER"/>
    <result column="attribute_id" property="attributeId" jdbcType="INTEGER"/>
    <result column="name" property="name" jdbcType="VARCHAR" />
    <result column="description" property="description" jdbcType="VARCHAR" />
    <result column="type" property="type" jdbcType="VARCHAR" />
    <result column="is_collection" property="isCollection" jdbcType="BIT" />
    <result column="is_required" property="isRequired" jdbcType="BIT" />
    <result column="dictionary_id" property="dictionaryId" jdbcType="INTEGER" />
    <result column="is_aspect" property="isAspect" jdbcType="BIT" />
    <result column="category_dependent" property="categoryDependent" jdbcType="BIT" />
    <result column="created_time" property="createdTime" jdbcType="TIMESTAMP" />
    <result column="updated_time" property="updatedTime" jdbcType="TIMESTAMP" />
    <result column="group_id" property="groupId" jdbcType="INTEGER" />
    <result column="group_name" property="groupName" jdbcType="VARCHAR" />
    <result column="attribute_complex_id" property="attributeComplexId" jdbcType="INTEGER" />
    <result column="max_value_count" property="maxValueCount" jdbcType="INTEGER" />
    <result column="complex_is_collection" property="complexIsCollection" jdbcType="BIT" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    id
    , category_id, attribute_id, `name`, description, `type`, is_collection, is_required, dictionary_id,
    is_aspect, category_dependent, created_time, updated_time, group_id, group_name, attribute_complex_id, max_value_count, complex_is_collection
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.estone.erp.publish.ozon.model.OzonCategoryAttributeExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from ozon_category_attribute_new
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
    <if test="limit != null" >
      <if test="offset != null" >
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null" >
        limit ${limit}
      </if>
    </if>
  </select>

  <select id="selectByExampleCopy" resultMap="BaseResultMap" parameterType="com.estone.erp.publish.ozon.model.OzonCategoryAttributeExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    'true' as QUERYID,
    <include refid="Base_Column_List" />
    from ozon_category_attribute_copy
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
    <if test="limit != null" >
      <if test="offset != null" >
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null" >
        limit ${limit}
      </if>
    </if>
  </select>

  <select id="selectMinimalByExample" resultMap="BaseResultMap" parameterType="com.estone.erp.publish.ozon.model.OzonCategoryAttributeExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    'true' as QUERYID,
    id, category_id, attribute_id, created_time
    from ozon_category_attribute_new
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
    <if test="limit != null" >
      <if test="offset != null" >
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null" >
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    select 
    <include refid="Base_Column_List" />
    from ozon_category_attribute_new
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" >
    delete from ozon_category_attribute_new
    where id IN 
    <foreach collection="list" item="listItem" open="(" close=")" separator="," >
      #{listItem}
    </foreach>
  </delete>
  <insert id="insert" parameterType="com.estone.erp.publish.ozon.model.OzonCategoryAttribute" >
    <selectKey resultType="java.lang.Integer" keyProperty="id" order="AFTER" >
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into ozon_category_attribute_new (category_id, attribute_id, `name`, description,
      `type`, is_collection, is_required, 
      dictionary_id, is_aspect, category_dependent, 
      created_time, updated_time, group_id,
      group_name, attribute_complex_id, max_value_count,
      complex_is_collection)
    values (#{categoryId,jdbcType=INTEGER}, #{attributeId,jdbcType=INTEGER}, #{name,jdbcType=VARCHAR},
    #{description,jdbcType=VARCHAR},
      #{type,jdbcType=VARCHAR}, #{isCollection,jdbcType=BIT}, #{isRequired,jdbcType=BIT}, 
      #{dictionaryId,jdbcType=INTEGER}, #{isAspect,jdbcType=BIT}, #{categoryDependent,jdbcType=BIT}, 
      #{createdTime,jdbcType=TIMESTAMP}, #{updatedTime,jdbcType=TIMESTAMP}, #{groupId,jdbcType=INTEGER},
      #{groupName,jdbcType=VARCHAR}, #{attributeComplexId,jdbcType=INTEGER}, #{maxValueCount,jdbcType=INTEGER},
      #{complexIsCollection,jdbcType=BIT})
  </insert>

  <insert id="insertCopy" parameterType="com.estone.erp.publish.ozon.model.OzonCategoryAttribute" >
    <selectKey resultType="java.lang.Integer" keyProperty="id" order="AFTER" >
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into ozon_category_attribute_copy (category_id, attribute_id, `name`, description,
    `type`, is_collection, is_required,
    dictionary_id, is_aspect, category_dependent,
    created_time, updated_time, group_id,
    group_name, attribute_complex_id, max_value_count,
    complex_is_collection)
    values (#{categoryId,jdbcType=INTEGER}, #{attributeId,jdbcType=INTEGER}, #{name,jdbcType=VARCHAR},
    #{description,jdbcType=VARCHAR},
    #{type,jdbcType=VARCHAR}, #{isCollection,jdbcType=BIT}, #{isRequired,jdbcType=BIT},
    #{dictionaryId,jdbcType=INTEGER}, #{isAspect,jdbcType=BIT}, #{categoryDependent,jdbcType=BIT},
    #{createdTime,jdbcType=TIMESTAMP}, #{updatedTime,jdbcType=TIMESTAMP}, #{groupId,jdbcType=INTEGER},
    #{groupName,jdbcType=VARCHAR}, #{attributeComplexId,jdbcType=INTEGER}, #{maxValueCount,jdbcType=INTEGER},
    #{complexIsCollection,jdbcType=BIT})
  </insert>
  <select id="countByExample" parameterType="com.estone.erp.publish.ozon.model.OzonCategoryAttributeExample" resultType="java.lang.Integer" >
    select count(*) from ozon_category_attribute_new
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update ozon_category_attribute_new
    <set >
      <if test="record.id != null" >
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.categoryId != null">
        category_id = #{record.categoryId,jdbcType=INTEGER},
      </if>
      <if test="record.attributeId != null" >
        attribute_id = #{record.attributeId,jdbcType=INTEGER},
      </if>
      <if test="record.name != null" >
        `name` = #{record.name,jdbcType=VARCHAR},
      </if>
      <if test="record.description != null" >
        description = #{record.description,jdbcType=VARCHAR},
      </if>
      <if test="record.type != null" >
        `type` = #{record.type,jdbcType=VARCHAR},
      </if>
      <if test="record.isCollection != null" >
        is_collection = #{record.isCollection,jdbcType=BIT},
      </if>
      <if test="record.isRequired != null" >
        is_required = #{record.isRequired,jdbcType=BIT},
      </if>
      <if test="record.dictionaryId != null" >
        dictionary_id = #{record.dictionaryId,jdbcType=INTEGER},
      </if>
      <if test="record.isAspect != null" >
        is_aspect = #{record.isAspect,jdbcType=BIT},
      </if>
      <if test="record.categoryDependent != null" >
        category_dependent = #{record.categoryDependent,jdbcType=BIT},
      </if>
      <if test="record.createdTime != null" >
        created_time = #{record.createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updatedTime != null" >
        updated_time = #{record.updatedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.groupId != null" >
        group_id = #{record.groupId,jdbcType=INTEGER},
      </if>
      <if test="record.groupName != null" >
        group_name = #{record.groupName,jdbcType=VARCHAR},
      </if>
      <if test="record.attributeComplexId != null" >
        attribute_complex_id = #{record.attributeComplexId,jdbcType=INTEGER},
      </if>
      <if test="record.maxValueCount != null" >
        max_value_count = #{record.maxValueCount,jdbcType=INTEGER},
      </if>
      <if test="record.complexIsCollection != null" >
        complex_is_collection = #{record.complexIsCollection,jdbcType=BIT},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.estone.erp.publish.ozon.model.OzonCategoryAttribute" >
    update ozon_category_attribute_new
    <set >
      <if test="categoryId != null">
        category_id = #{categoryId,jdbcType=INTEGER},
      </if>
      <if test="attributeId != null" >
        attribute_id = #{attributeId,jdbcType=INTEGER},
      </if>
      <if test="name != null" >
        `name` = #{name,jdbcType=VARCHAR},
      </if>
      <if test="description != null" >
        description = #{description,jdbcType=VARCHAR},
      </if>
      <if test="type != null" >
        `type` = #{type,jdbcType=VARCHAR},
      </if>
      <if test="isCollection != null" >
        is_collection = #{isCollection,jdbcType=BIT},
      </if>
      <if test="isRequired != null" >
        is_required = #{isRequired,jdbcType=BIT},
      </if>
      <if test="dictionaryId != null" >
        dictionary_id = #{dictionaryId,jdbcType=INTEGER},
      </if>
      <if test="isAspect != null" >
        is_aspect = #{isAspect,jdbcType=BIT},
      </if>
      <if test="categoryDependent != null" >
        category_dependent = #{categoryDependent,jdbcType=BIT},
      </if>
      <if test="createdTime != null" >
        created_time = #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedTime != null" >
        updated_time = #{updatedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="groupId != null" >
        group_id = #{groupId,jdbcType=INTEGER},
      </if>
      <if test="groupName != null" >
        group_name = #{groupName,jdbcType=VARCHAR},
      </if>
      <if test="attributeComplexId != null" >
        attribute_complex_id = #{attributeComplexId,jdbcType=INTEGER},
      </if>
      <if test="maxValueCount != null" >
        max_value_count = #{maxValueCount,jdbcType=INTEGER},
      </if>
      <if test="complexIsCollection != null" >
        complex_is_collection = #{complexIsCollection,jdbcType=BIT},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>

  <update id="updateByPrimaryKeySelectiveCopy" parameterType="com.estone.erp.publish.ozon.model.OzonCategoryAttribute" >
    update ozon_category_attribute_copy
    <set >
      <if test="categoryId != null">
        category_id = #{categoryId,jdbcType=INTEGER},
      </if>
      <if test="attributeId != null" >
        attribute_id = #{attributeId,jdbcType=INTEGER},
      </if>
      <if test="name != null" >
        `name` = #{name,jdbcType=VARCHAR},
      </if>
      <if test="description != null" >
        description = #{description,jdbcType=VARCHAR},
      </if>
      <if test="type != null" >
        `type` = #{type,jdbcType=VARCHAR},
      </if>
      <if test="isCollection != null" >
        is_collection = #{isCollection,jdbcType=BIT},
      </if>
      <if test="isRequired != null" >
        is_required = #{isRequired,jdbcType=BIT},
      </if>
      <if test="dictionaryId != null" >
        dictionary_id = #{dictionaryId,jdbcType=INTEGER},
      </if>
      <if test="isAspect != null" >
        is_aspect = #{isAspect,jdbcType=BIT},
      </if>
      <if test="categoryDependent != null" >
        category_dependent = #{categoryDependent,jdbcType=BIT},
      </if>
      <if test="createdTime != null" >
        created_time = #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedTime != null" >
        updated_time = #{updatedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="groupId != null" >
        group_id = #{groupId,jdbcType=INTEGER},
      </if>
      <if test="groupName != null" >
        group_name = #{groupName,jdbcType=VARCHAR},
      </if>
      <if test="attributeComplexId != null" >
        attribute_complex_id = #{attributeComplexId,jdbcType=INTEGER},
      </if>
      <if test="maxValueCount != null" >
        max_value_count = #{maxValueCount,jdbcType=INTEGER},
      </if>
      <if test="complexIsCollection != null" >
        complex_is_collection = #{complexIsCollection,jdbcType=BIT},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>

  <!-- 批量插入记录 -->
  <insert id="batchInsert" parameterType="java.util.List">
    insert into ozon_category_attribute_new (category_id, attribute_id, `name`, description,
      `type`, is_collection, is_required, 
      dictionary_id, is_aspect, category_dependent, 
      created_time, updated_time, group_id,
      group_name, attribute_complex_id, max_value_count,
      complex_is_collection)
    values 
    <foreach collection="list" item="item" separator=",">
      (#{item.categoryId,jdbcType=INTEGER}, #{item.attributeId,jdbcType=INTEGER}, #{item.name,jdbcType=VARCHAR},
      #{item.description,jdbcType=VARCHAR},
      #{item.type,jdbcType=VARCHAR}, #{item.isCollection,jdbcType=BIT}, #{item.isRequired,jdbcType=BIT}, 
      #{item.dictionaryId,jdbcType=INTEGER}, #{item.isAspect,jdbcType=BIT}, #{item.categoryDependent,jdbcType=BIT}, 
      #{item.createdTime,jdbcType=TIMESTAMP}, #{item.updatedTime,jdbcType=TIMESTAMP}, #{item.groupId,jdbcType=INTEGER},
      #{item.groupName,jdbcType=VARCHAR}, #{item.attributeComplexId,jdbcType=INTEGER}, #{item.maxValueCount,jdbcType=INTEGER},
      #{item.complexIsCollection,jdbcType=BIT})
    </foreach>
  </insert>

  <!-- 批量更新记录 -->
  <update id="batchUpdate" parameterType="java.util.List">
    <foreach collection="list" item="item" separator=";">
      update ozon_category_attribute_new
      <set>
        <if test="item.categoryId != null">
          category_id = #{item.categoryId,jdbcType=INTEGER},
        </if>
        <if test="item.attributeId != null">
          attribute_id = #{item.attributeId,jdbcType=INTEGER},
        </if>
        <if test="item.name != null">
          `name` = #{item.name,jdbcType=VARCHAR},
        </if>
        <if test="item.description != null">
          description = #{item.description,jdbcType=VARCHAR},
        </if>
        <if test="item.type != null">
          `type` = #{item.type,jdbcType=VARCHAR},
        </if>
        <if test="item.isCollection != null">
          is_collection = #{item.isCollection,jdbcType=BIT},
        </if>
        <if test="item.isRequired != null">
          is_required = #{item.isRequired,jdbcType=BIT},
        </if>
        <if test="item.dictionaryId != null">
          dictionary_id = #{item.dictionaryId,jdbcType=INTEGER},
        </if>
        <if test="item.isAspect != null">
          is_aspect = #{item.isAspect,jdbcType=BIT},
        </if>
        <if test="item.categoryDependent != null">
          category_dependent = #{item.categoryDependent,jdbcType=BIT},
        </if>
        <if test="item.createdTime != null">
          created_time = #{item.createdTime,jdbcType=TIMESTAMP},
        </if>
        <if test="item.updatedTime != null">
          updated_time = #{item.updatedTime,jdbcType=TIMESTAMP},
        </if>
        <if test="item.groupId != null">
          group_id = #{item.groupId,jdbcType=INTEGER},
        </if>
        <if test="item.groupName != null">
          group_name = #{item.groupName,jdbcType=VARCHAR},
        </if>
        <if test="item.attributeComplexId != null">
          attribute_complex_id = #{item.attributeComplexId,jdbcType=INTEGER},
        </if>
        <if test="item.maxValueCount != null">
          max_value_count = #{item.maxValueCount,jdbcType=INTEGER},
        </if>
        <if test="item.complexIsCollection != null">
          complex_is_collection = #{item.complexIsCollection,jdbcType=BIT},
        </if>
      </set>
      where id = #{item.id,jdbcType=INTEGER}
    </foreach>
  </update>
</mapper>

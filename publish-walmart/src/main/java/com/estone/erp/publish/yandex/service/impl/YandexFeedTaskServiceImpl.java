package com.estone.erp.publish.yandex.service.impl;

import com.alibaba.fastjson.JSON;
import com.estone.erp.common.util.WebUtils;
import com.estone.erp.publish.common.context.DataContextHolder;
import com.estone.erp.publish.common.enums.FeedTaskResultStatusEnum;
import com.estone.erp.publish.common.enums.FeedTaskStatusEnum;
import com.estone.erp.publish.platform.util.Platform;
import com.estone.erp.publish.system.feedTask.model.FeedTask;
import com.estone.erp.publish.system.feedTask.model.FeedTaskExample;
import com.estone.erp.publish.system.feedTask.service.FeedTaskService;
import com.estone.erp.publish.yandex.enums.YandexFeedTaskEnums;
import com.estone.erp.publish.yandex.model.dto.YandexUpdateDO;
import com.estone.erp.publish.yandex.service.YandexFeedTaskService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
public class YandexFeedTaskServiceImpl implements YandexFeedTaskService {
    @Autowired
    private FeedTaskService feedTaskService;

    @Override
    @Transactional(readOnly = false)
    public FeedTask newTask(String accountNumber, String taskType, String sku, String... s) {
        FeedTask feedTask = new FeedTask();
        feedTask.setAssociationId(null);
        feedTask.setAccountNumber(accountNumber);
        feedTask.setArticleNumber(sku);
        feedTask.setTaskType(taskType);
        feedTask.setPlatform(Platform.Yandex.name());
        feedTask.setTaskStatus(FeedTaskStatusEnum.RUNNING.getTaskStatus());
        String currentUser = StringUtils.isBlank(WebUtils.getUserName()) ? DataContextHolder.getUsername() : WebUtils.getUserName();
        String userName = StringUtils.isBlank(currentUser) ? "admin" : currentUser;
        feedTask.setCreatedBy(userName);
        feedTask.setCreateTime(new Timestamp(System.currentTimeMillis()));
        feedTask.setTableIndex();
        getFeed(feedTask, s);
        feedTaskService.insertSelective(feedTask);
        return feedTask;
    }

    @Override
    @Transactional(readOnly = false)
    public void succeedTask(FeedTask feedTask, String msg, String... s) {
        feedTask.setResultMsg(msg);
        feedTask.setFinishTime(new Timestamp(System.currentTimeMillis()));
        feedTask.setTaskStatus(FeedTaskStatusEnum.FINISH.getTaskStatus());
        feedTask.setResultStatus(FeedTaskResultStatusEnum.SUCCESS.getResultStatus());
        feedTask.setPlatform(Platform.Yandex.name());
        feedTask.setTableIndex();
        feedTaskService.updateByPrimaryKeySelective(feedTask);
    }

    @Override
    @Transactional(readOnly = false, propagation = Propagation.REQUIRES_NEW)
    public void failTask(FeedTask feedTask, String msg, String... s) {
        if (feedTask == null) {
            return;
        }

        feedTask.setResultMsg(msg);
        feedTask.setFinishTime(new Timestamp(System.currentTimeMillis()));
        feedTask.setTaskStatus(FeedTaskStatusEnum.FINISH.getTaskStatus());
        feedTask.setResultStatus(FeedTaskResultStatusEnum.FAIL.getResultStatus());
        feedTask.setPlatform(Platform.Yandex.name());
        feedTask.setTableIndex();
        feedTaskService.updateByPrimaryKeySelective(feedTask);
    }

    @Override
    public List<FeedTask> selectByExample(FeedTaskExample feedTaskExample, String name) {
        return feedTaskService.selectByExample(feedTaskExample, name);
    }

    @Override
    @Transactional(readOnly = false, propagation = Propagation.REQUIRES_NEW)
    public void batchNewUpdateStockFeeds(List<YandexUpdateDO> yandexUpdateDOS, String taskType) {
        List<FeedTask> feedTasks = new ArrayList<>();
        for (YandexUpdateDO yandexUpdateDO : yandexUpdateDOS) {
            FeedTask feedTask = new FeedTask();
            // yandex无productId
            feedTask.setAssociationId(null);
            feedTask.setAccountNumber(yandexUpdateDO.getAccountNumber());
            feedTask.setArticleNumber(yandexUpdateDO.getSku());
            feedTask.setTaskType(taskType);
            feedTask.setAttribute1(yandexUpdateDO.getSellerSku());
            // 主要记录调0时修改库存的状态
            feedTask.setAttribute8(yandexUpdateDO.getState());
            feedTask.setAttribute5(JSON.toJSONString(yandexUpdateDO));
            feedTask.setPlatform(Platform.Yandex.name());
            feedTask.setTaskStatus(FeedTaskStatusEnum.RUNNING.getTaskStatus());
            feedTask.setCreatedBy(StringUtils.defaultString(StringUtils.isBlank(WebUtils.getUserName()) ? DataContextHolder.getUsername() : WebUtils.getUserName(), "admin"));
            feedTask.setCreateTime(new Timestamp(System.currentTimeMillis()));
            feedTask.setAttribute10(yandexUpdateDO.getJob());
            String before = String.valueOf(yandexUpdateDO.getUpdateBeforeStock());
            String after = String.valueOf(yandexUpdateDO.getUpdateAfterStock());
            getFeed(feedTask, new String[]{yandexUpdateDO.getSellerSku(), yandexUpdateDO.getSku(), before, after});
            feedTask.setTableIndex();
            feedTasks.add(feedTask);
        }
        feedTaskService.batchInsertSelective(feedTasks, feedTasks.get(0).getTableIndex());
    }

    @Override
    @Transactional(readOnly = false, propagation = Propagation.REQUIRES_NEW)
    public void batchUpdateFail(String accountNumber, List<YandexUpdateDO> yandexUpdateDOS, String taskType, String errorMsg) {
        List<String> sellerSkus = yandexUpdateDOS.stream().map(YandexUpdateDO::getSellerSku).filter(Objects::nonNull).collect(Collectors.toList());
        FeedTaskExample feedTaskExample = new FeedTaskExample();
        FeedTaskExample.Criteria criteria = feedTaskExample.createCriteria();
        criteria.andAccountNumberEqualTo(accountNumber);
        criteria.andTaskStatusEqualTo(FeedTaskStatusEnum.RUNNING.getTaskStatus());
        criteria.andTaskTypeEqualTo(taskType);
        criteria.andAttribute1In(sellerSkus);
        List<FeedTask> feedTasks = feedTaskService.selectByExample(feedTaskExample, Platform.Yandex.name());
        if (CollectionUtils.isNotEmpty(feedTasks)) {
            for (FeedTask feedTask : feedTasks) {
                feedTask.setTaskStatus(FeedTaskStatusEnum.FINISH.getTaskStatus());
                feedTask.setResultStatus(FeedTaskResultStatusEnum.FAIL.getResultStatus());
                feedTask.setResultMsg(StringUtils.defaultString(feedTask.getResultMsg(), "") + errorMsg);
                feedTask.setFinishTime(new Timestamp(System.currentTimeMillis()));
                feedTask.setTableIndex();
            }
            feedTaskService.batchUpdateFeedTask(feedTasks);
        }


    }

    @Override
    @Transactional(readOnly = false)
    public FeedTask newWaitingTask(Long productId, String accountNumber, String taskType, String sku, String... s) {
        FeedTask feedTask = new FeedTask();
        feedTask.setAssociationId(productId != null ? String.valueOf(productId) : null);
        feedTask.setAccountNumber(accountNumber);
        feedTask.setArticleNumber(sku);
        feedTask.setTaskType(taskType);
        feedTask.setPlatform(Platform.Yandex.name());
        feedTask.setTaskStatus(FeedTaskStatusEnum.WAITING.getTaskStatus());
        String currentUser = StringUtils.isBlank(WebUtils.getUserName()) ? DataContextHolder.getUsername() : WebUtils.getUserName();
        String userName = StringUtils.isBlank(currentUser) ? "admin" : currentUser;
        feedTask.setCreatedBy(userName);
        feedTask.setCreateTime(new Timestamp(System.currentTimeMillis()));
        feedTask.setTableIndex();
        getFeed(feedTask, s);
        feedTaskService.insertSelective(feedTask);
        return feedTask;
    }

    @Override
    @Transactional(readOnly = false, propagation = Propagation.REQUIRES_NEW)
    public void batchUpdateFeeds(List<FeedTask> feedTasks) {
        for (FeedTask feedTask : feedTasks) {
            feedTask.setPlatform(Platform.Yandex.name());
            feedTask.setTaskStatus(FeedTaskStatusEnum.FINISH.getTaskStatus());
            feedTask.setTableIndex();
        }
        feedTaskService.batchUpdateFeedTask(feedTasks);
    }

    @Override
    public FeedTask getExecutingSyncItemTask(String accountNumber) {
        FeedTaskExample feedTaskExample = new FeedTaskExample();
        feedTaskExample.createCriteria()
                .andAccountNumberEqualTo(accountNumber)
                .andTaskTypeEqualTo(YandexFeedTaskEnums.TaskType.SYNC_ITEM.name())
                .andTaskStatusEqualTo(FeedTaskStatusEnum.RUNNING.getTaskStatus());
        List<FeedTask> feedTaskList = feedTaskService.selectByExample(feedTaskExample, Platform.Yandex.name());
        if (CollectionUtils.isEmpty(feedTaskList)) {
            return null;
        }
        return feedTaskList.get(0);
    }

    @Override
    public void updateTaskStatus(FeedTask feedTask, Integer status) {
        feedTask.setRunTime(new Timestamp(System.currentTimeMillis()));
        feedTask.setTaskStatus(status);
        feedTask.setPlatform(Platform.Yandex.name());
        feedTask.setTableIndex();
        feedTaskService.updateByPrimaryKeySelective(feedTask);
    }

    @Override
    public void addFailFeedTask(Long productId, String accountNumber, String taskType, String sku, String failMsg, String... s) {
        FeedTask feedTask = new FeedTask();
        feedTask.setAssociationId(productId != null ? String.valueOf(productId) : null);
        feedTask.setAccountNumber(accountNumber);
        feedTask.setArticleNumber(sku);
        feedTask.setTaskType(taskType);
        feedTask.setPlatform(Platform.Yandex.name());
        feedTask.setTaskStatus(FeedTaskStatusEnum.FINISH.getTaskStatus());
        feedTask.setResultStatus(FeedTaskResultStatusEnum.FAIL.getResultStatus());
        feedTask.setResultMsg(failMsg);
        String currentUser = StringUtils.isBlank(WebUtils.getUserName()) ? DataContextHolder.getUsername() : WebUtils.getUserName();
        String userName = StringUtils.isBlank(currentUser) ? "admin" : currentUser;
        feedTask.setCreatedBy(userName);
        feedTask.setCreateTime(Timestamp.valueOf(LocalDateTime.now()));
        feedTask.setFinishTime(Timestamp.valueOf(LocalDateTime.now()));
        feedTask.setTableIndex();
        getFeed(feedTask, s);
        feedTaskService.insertSelective(feedTask);
    }


    /**
     * 处理拓展字段
     *
     * @param feedTask
     * @param s        Attribute1：sellerSku
     *                 Attribute2：sku
     *                 Attribute3：改前值
     *                 Attribute4：改后值
     *                 Attribute5：模板id
     */
    public void getFeed(FeedTask feedTask, String[] s) {
        for (int i = 0; i < s.length; i++) {
            switch (i) {
                case 0:
                    feedTask.setAttribute1(s[0]);
                    break;
                case 1:
                    feedTask.setAttribute2(s[1]);
                    break;
                case 2:
                    feedTask.setAttribute3(s[2]);
                    break;
                case 3:
                    feedTask.setAttribute4(s[3]);
                    break;
                case 4:
                    feedTask.setAttribute5(s[4]);
                    break;
            }
        }
    }
}

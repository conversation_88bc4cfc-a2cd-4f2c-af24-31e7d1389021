package com.estone.erp.publish.walmart.jobHandler;

import com.alibaba.fastjson.JSON;
import com.estone.erp.common.jobHandler.AbstractJobHandler;
import com.estone.erp.common.mq.PublishQueues;
import com.estone.erp.common.mq.RabbitMqSender;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.elasticsearch.util.EsAccountUtils;
import com.estone.erp.publish.mq.PublishMqConfig;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * 校验item侵权词 全量
 * @Auther yucm
 * @Date 2023/2/10
 */
@Slf4j
@Component
public class WalmartCheckItemInfringementWordHandler extends AbstractJobHandler {
    public WalmartCheckItemInfringementWordHandler() {
        super(WalmartCheckItemInfringementWordHandler.class.getName());
    }

    @Resource
    private RabbitMqSender rabbitMqSender;

    @Getter
    @Setter
    static class InnerParam{
        // 账号集合
        private List<String> accountNumbers;
    }

    @Override
    @XxlJob("WalmartCheckItemInfringementWordHandler")
    public ReturnT<String> run(String param) throws Exception {
        InnerParam innerParam = null;
        if(StringUtils.isNotBlank(param)){
            try {
                innerParam = JSON.parseObject(param, InnerParam.class);
            }catch (Exception e){
                XxlJobLogger.log("-------解析param出错：--------", e);
                return ReturnT.FAIL;
            }
        }
        if(innerParam == null){
            innerParam = new InnerParam();
        }
        List<String> accountNumbers = innerParam.getAccountNumbers();
        List<String> walmartAccounts = EsAccountUtils.getPlatformNormaLAccountListByEs(SaleChannel.CHANNEL_WALMART);
        if(CollectionUtils.isEmpty(walmartAccounts)) {
            return ReturnT.FAIL;
        }

        for (String walmartAccount : walmartAccounts) {
            // 输入了则只执行输入的账号
            if (CollectionUtils.isNotEmpty(accountNumbers) && !accountNumbers.contains(walmartAccount)) {
                continue;
            }

            // 发送mq
            try {
                XxlJobLogger.log(walmartAccount + "发送mq");
                rabbitMqSender.send(PublishMqConfig.WALMART_API_DIRECT_EXCHANGE, PublishQueues.WALMART_CHECK_ITEM_INFRINGEMENT_WORD_ROUTING_KEY, walmartAccount);
            }catch (Exception e) {
                XxlJobLogger.log(walmartAccount + "mq发送失败");
            }
        }

        return ReturnT.SUCCESS;
    }
}
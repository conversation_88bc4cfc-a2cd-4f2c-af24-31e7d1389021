package com.estone.erp.publish.walmart.jobHandler;

import com.alibaba.fastjson.JSON;
import com.estone.erp.common.jobHandler.AbstractJobHandler;
import com.estone.erp.common.util.CommonUtils;
import com.estone.erp.common.util.DateUtils;
import com.estone.erp.common.util.StrUtil;
import com.estone.erp.publish.base.pms.enums.SkuStatusEnum;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.common.util.CacheUtils;
import com.estone.erp.publish.common.util.PagingUtils;
import com.estone.erp.publish.platform.model.HolidaySkuModifyLog;
import com.estone.erp.publish.platform.service.HolidaySkuModifyLogService;
import com.estone.erp.publish.system.account.AccountUtils;
import com.estone.erp.publish.system.account.SaleAccountAndBusinessResponse;
import com.estone.erp.publish.system.param.service.SystemParamService;
import com.estone.erp.publish.walmart.enums.ItemLifecycleStatusEnum;
import com.estone.erp.publish.walmart.model.WalmartItem;
import com.estone.erp.publish.walmart.model.WalmartItemExample;
import com.estone.erp.publish.walmart.service.WalmartItemService;
import com.estone.erp.publish.walmart.util.WalmartAccountUtils;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 春节调库存
 *
 * 定时频率：一天2次
 * 调整时间：每天早上五点，下午17:00
 * 时间范围：现在至2022年2月16日
 * 针对休假状态的sku，库存调整为0
 * 注：备份listing最初库存，年后回来恢复库存。
 * <AUTHOR>
 * @date 2022/1/13 14:41
 */
@Slf4j
@Component
public class WalmartNewYearUpdateInventoryJobHandler extends AbstractJobHandler {
    @Getter
    @Setter
    public static class InnerParam {
        // 默认库存改0: ZeroInventory, 还原库存：RestoreInventory
        private String execType = "ZeroInventory";
        // 库存改0 结束日期，默认 2022-02-17
        private Date zeroInventoryStopDate;
        // 店铺账号
        private List<String> accountNumberList;
    }

    /**
     * 店铺&sellerSku : 库存
     */
    private String format = "%s&%s";

    /**
     * 账号状态 启用
     */
    private static String accountStatusEnable = "1";

    @Resource
    private WalmartItemService walmartItemService;

    @Resource
    private HolidaySkuModifyLogService holidaySkuModifyLogService;

    @Resource
    private SystemParamService systemParamService;

    public WalmartNewYearUpdateInventoryJobHandler() {
        super(WalmartNewYearUpdateInventoryJobHandler.class.getName());
    }

    @Override
    @XxlJob("WalmartNewYearUpdateInventoryJobHandler")
    public ReturnT<String> run(String param) throws Exception {
        XxlJobLogger.log("Walmart春节调库存 start");

        InnerParam innerParam = null;
        if (StringUtils.isNotBlank(param)) {
            try {
                innerParam = JSON.parseObject(param, InnerParam.class);
            } catch (Exception e) {
                ReturnT fail = new ReturnT(500, "参数错误: "+ param);
                fail.setContent(e.getMessage());
                return fail;
            }
        }
        if (innerParam == null) {
            innerParam = new InnerParam();
        }

        if ("ZeroInventory".equalsIgnoreCase(innerParam.getExecType())) {
            // 库存改0
            zeroInventory(innerParam);
        }/* else if ("RestoreInventory".equalsIgnoreCase(innerParam.getExecType())) {
            // 库存还原
            restoreInventory(innerParam);
        }*/

        XxlJobLogger.log("Walmart春节调库存 end");
        return ReturnT.SUCCESS;
    }

    private void zeroInventory(InnerParam innerParam) {
        // 确定库存改0结束时间
        if (innerParam.getZeroInventoryStopDate() == null) {
            Date date = DateUtils.parseDate("2023-04-17", DateUtils.YMD_FORMAT);
            innerParam.setZeroInventoryStopDate(date);
        }
        XxlJobLogger.log("春节期间库存改0, 改0结束时间 {}", innerParam.getZeroInventoryStopDate());

        Calendar now = Calendar.getInstance();
        if (now.getTimeInMillis() >= innerParam.getZeroInventoryStopDate().getTime()) {
            return;
        }

        // 获取单批次修改库存数量
        String count = systemParamService.queryParamValue(SaleChannel.CHANNEL_WALMART, "WALMART",
                "BATCH_UPDATE_INVENTORY").getParamValue();

        // 公司自注册的sku不调0
        String systemParamValue = CacheUtils.getSystemParamValue(SaleChannel.CHANNEL_WALMART, "WALMART", "SELF_REGISTERED_SKU", 5);
        List<String> selfRegisteredSkuList = CommonUtils.splitList(systemParamValue, ",");

        // 查询账号
        List<SaleAccountAndBusinessResponse> walmartAccounts = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(innerParam.getAccountNumberList())) {
            for (String accountNumber : innerParam.getAccountNumberList()) {
                SaleAccountAndBusinessResponse walmartAccount = AccountUtils.getSaleAccountByAccountNumber(SaleChannel.CHANNEL_WALMART, accountNumber, true);
                walmartAccounts.add(walmartAccount);
            }
        } else {
            walmartAccounts = AccountUtils.getSaleAccountListBySaleChannel(SaleChannel.CHANNEL_WALMART);
            if(CollectionUtils.isEmpty(walmartAccounts)) {
                return;
            }
            walmartAccounts = walmartAccounts.stream().filter(o -> accountStatusEnable.equals(o.getAccountStatus())
                    && !WalmartAccountUtils.isPublishTemuAccount(o.getAccountNumber())).collect(Collectors.toList());
        }

        // 店铺&sellerSku : 库存
        Map<String, Integer> originMap = new HashMap<>();

        for (SaleAccountAndBusinessResponse walmartAccount : walmartAccounts) {
            int limit = 10000;
            Long id = 0L;
            while (true) {
                WalmartItemExample itemExample = new WalmartItemExample();
                itemExample.setOrderByClause("id ASC");
                itemExample.setOffset(0);
                itemExample.setLimit(limit);
                WalmartItemExample.Criteria criteria = itemExample.createCriteria();
                if (id > 0L) {
                    criteria.andIdGreaterThan(id);
                }
                criteria.andAccountNumberEqualTo(walmartAccount.getAccountNumber())
                        .andLifecycleStatusEqualTo(ItemLifecycleStatusEnum.ACTIVE.getCode())
                        .andSkuStatusEqualTo(SkuStatusEnum.HOLIDAY.getCode())
                        .andInventoryNotEqualZeroOrIsNull();
                if (CollectionUtils.isNotEmpty(selfRegisteredSkuList)) {
                    criteria.andSkuNotIn(selfRegisteredSkuList);
                }
                List<WalmartItem> walmartItems = walmartItemService.selectByExample(itemExample);
                if (CollectionUtils.isEmpty(walmartItems)) {
                    break;
                }
                id = walmartItems.get(walmartItems.size() - 1).getId();
                originMap.clear();

                for (WalmartItem bean : walmartItems) {
                    // 记录原始库存
                    originMap.put(String.format(format, bean.getAccountNumber(), bean.getSellerSku()), bean.getInventory());

                    // 库存设为0
                    bean.setInventory(0);
                }

                // 修改数据
                List<List<WalmartItem>> pagingList = PagingUtils.pagingList(walmartItems, Integer.valueOf(count));
                for (List<WalmartItem> items : pagingList) {
                    String error = walmartItemService.batchUpdateInventory(items, walmartAccount, null);
                    List<String> sellerSkuList = items.stream().map(WalmartItem::getSellerSku).collect(Collectors.toList());
                    if (StringUtils.isBlank(error)) {
                        // 记录修改库存日志
                        insertLog(walmartAccount.getAccountNumber(), items, originMap);
                        XxlJobLogger.log(String.format("店铺{%s},sellerSku%s修改成功", walmartAccount.getAccountNumber(), sellerSkuList));
                    } else {
                        XxlJobLogger.log(String.format("店铺{%s},sellerSku%s修改失败，失败原因%s", walmartAccount.getAccountNumber(), sellerSkuList, error));
                    }
                }
            }
        }
    }

//    private void restoreInventory(InnerParam innerParam) {
//        List<String> accountNumberList;
//        if (CollectionUtils.isNotEmpty(innerParam.getAccountNumberList())) {
//            accountNumberList = innerParam.getAccountNumberList();
//        } else {
//            List<SaleAccountAndBusinessResponse> walmartAccounts = AccountUtils.getSaleAccountListBySaleChannel(SaleChannel.CHANNEL_WALMART);
//            accountNumberList = walmartAccounts.stream().map(SaleAccountAndBusinessResponse::getAccountNumber).distinct().collect(Collectors.toList());
//        }
//
//        for (String accountNumber : accountNumberList) {
//            HolidaySkuModifyLogExample exLog = new HolidaySkuModifyLogExample();
//            exLog.createCriteria()
//                    .andSaleChannelEqualTo(SaleChannel.CHANNEL_WALMART)
//                    .andAccountNumberEqualTo(accountNumber);
//            List<HolidaySkuModifyLog> list = holidaySkuModifyLogMapper.selectByExample(exLog);
//            if (CollectionUtils.isEmpty(list)) {
//                continue;
//            }
//
//            Map<String, HolidaySkuModifyLog> map = list.stream().collect(Collectors.toMap(o ->
//                            String.format(format, o.getAccountNumber(), o.getSku()), o -> o,
//                    (o1, o2) -> {
//                        if (o1.getModifyTime().getTime() < o2.getModifyTime().getTime()) {
//                            return o1;
//                        }
//                        return o2;
//                    }));
//
//            List<String> sellerSkuList = list.stream().map(HolidaySkuModifyLog::getSku).distinct().collect(Collectors.toList());
//            WalmartItemExample itemEx = new WalmartItemExample();
//            itemEx.createCriteria()
//                    .andAccountNumberEqualTo(accountNumber)
//                    .andSellerSkuIn(sellerSkuList);
//            List<WalmartItem> listing = walmartItemService.selectByExample(itemEx);
//            if (CollectionUtils.isEmpty(listing)) {
//                continue;
//            }
//
//            List<WalmartItem> updateList = listing.stream().peek(bean -> {
//                HolidaySkuModifyLog log = map.get(String.format(format, bean.getAccountNumber(), bean.getSellerSku()));
//                if (log != null) {
//                    bean.setInventory(log.getStockBefore());
//                }
//                bean.setUpdateDate(new Timestamp(System.currentTimeMillis()));
//            }).collect(Collectors.toList());
//
//            // 获取账号信息
//            SaleAccountAndBusinessResponse account = AccountUtils.getSaleAccountByAccountNumber(SaleChannel.CHANNEL_WALMART, accountNumber);
//
//            List<List<WalmartItem>> pagingList = PagingUtils.pagingList(updateList, 20);
//            for (List<WalmartItem> items : pagingList) {
//                String error = walmartItemService.batchUpdateInventory(items, account);
//                List<String> sellerSkus = items.stream().map(WalmartItem::getSellerSku).collect(Collectors.toList());
//                if (StringUtils.isBlank(error)) {
//                    XxlJobLogger.log(String.format("店铺{%s},sellerSku%s还原成功", accountNumber, sellerSkus));
//                } else {
//                    XxlJobLogger.log(String.format("店铺{%s},sellerSku%s还原失败，失败原因%s", accountNumber, sellerSkus, error));
//                }
//            }
//        }
//    }

    private void insertLog(String accountNumber, List<WalmartItem> items, Map<String, Integer> originMap) {
        if (CollectionUtils.isEmpty(items)) {
            return;
        }

        List<HolidaySkuModifyLog> logList = new ArrayList<>();
        for (WalmartItem item : items) {
            HolidaySkuModifyLog log = new HolidaySkuModifyLog();
            log.setSaleChannel(SaleChannel.CHANNEL_WALMART);
            log.setProductId(StrUtil.objectToStr(item.getItemId()));
            log.setAccountNumber(accountNumber);
            log.setSku(item.getSellerSku());
            log.setStatus(SkuStatusEnum.HOLIDAY.getCode());
            log.setStockBefore(originMap.get(String.format(format, accountNumber, item.getSellerSku())));
            log.setStockAfter(item.getInventory());
            log.setModifyTime(new Timestamp(System.currentTimeMillis()));

            logList.add(log);
        }
        holidaySkuModifyLogService.batchInsert(logList);
    }
}
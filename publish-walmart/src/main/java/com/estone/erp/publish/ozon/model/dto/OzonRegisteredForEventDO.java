package com.estone.erp.publish.ozon.model.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/7/26 17:49
 */
@Data
public class OzonRegisteredForEventDO {

    private Boolean registeredForEvent;
    /**
     * 更新毛利毛利率时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date registeredForEventDate;

}

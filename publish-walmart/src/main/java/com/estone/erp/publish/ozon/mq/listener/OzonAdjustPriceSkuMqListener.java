package com.estone.erp.publish.ozon.mq.listener;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.publish.common.util.PagingUtils;
import com.estone.erp.publish.elasticsearch4.model.EsOzonItem;
import com.estone.erp.publish.elasticsearch4.model.EsOzonStockInfo;
import com.estone.erp.publish.elasticsearch4.model.beanrequest.EsOzonItemRequest;
import com.estone.erp.publish.elasticsearch4.service.EsOzonItemService;
import com.estone.erp.publish.feginService.modle.BatchPriceCalculatorResponse;
import com.estone.erp.publish.ozon.enums.OzonSiteCurrencyEnum;
import com.estone.erp.publish.ozon.enums.OzonUpdatePriceTypeEnum;
import com.estone.erp.publish.ozon.enums.OzonWarehouseEnum;
import com.estone.erp.publish.ozon.handler.OzonUpdateHandler;
import com.estone.erp.publish.ozon.job.model.OzonAdjustPriceJobParam;
import com.estone.erp.publish.ozon.model.OzonAccountConfig;
import com.estone.erp.publish.ozon.model.OzonEstdaLogisticsRule;
import com.estone.erp.publish.ozon.model.dto.OzonCalcPriceRequest;
import com.estone.erp.publish.ozon.model.dto.OzonCalcPriceResponse;
import com.estone.erp.publish.ozon.model.dto.OzonUpdateDO;
import com.estone.erp.publish.ozon.model.vo.OzonWareHouseInfoVO;
import com.estone.erp.publish.ozon.mq.bean.OzonAdjustPriceContext;
import com.estone.erp.publish.ozon.service.OzonAccountConfigService;
import com.estone.erp.publish.ozon.service.OzonEstdaLogisticsRuleService;
import com.estone.erp.publish.ozon.utils.OzonCalcUtils;
import com.estone.erp.publish.tidb.publishtidb.model.OzonAdjustPriceSku;
import com.estone.erp.publish.tidb.publishtidb.model.OzonAdjustPriceSkuLog;
import com.estone.erp.publish.tidb.publishtidb.service.OzonAdjustPriceSkuLogService;
import com.estone.erp.publish.tidb.publishtidb.service.OzonAdjustPriceSkuService;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.listener.api.ChannelAwareMessageListener;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Description: ozon sku调价
 * <AUTHOR>
 * @Date 2025/3/27 下午5:23
 */
@Slf4j
public class OzonAdjustPriceSkuMqListener implements ChannelAwareMessageListener {

    @Resource
    private OzonAdjustPriceSkuService ozonAdjustPriceSkuService;

    @Resource
    private EsOzonItemService esOzonItemService;
    @Resource
    private OzonAccountConfigService ozonAccountConfigService;
    @Resource
    private OzonAdjustPriceSkuLogService ozonAdjustPriceSkuLogService;
    @Resource
    private OzonEstdaLogisticsRuleService ozonEstdaLogisticsRuleService;

    @Resource
    private OzonUpdateHandler ozonUpdateHandler;

    @Override
    public void onMessage(Message message, Channel channel) throws Exception {
        String body = new String(message.getBody(), StandardCharsets.UTF_8);
        OzonAdjustPriceJobParam ozonOnlineConfig = null;
        try {
            ozonOnlineConfig = JSON.parseObject(body, new TypeReference<>() {
            });
            if (ozonOnlineConfig == null || ozonOnlineConfig.getAccountNumber() == null) {
                log.error("OzonAdjustPriceSkuMqListener --> ozonOnlineConfig is null");
            } else {
                doService(ozonOnlineConfig);
            }
            channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
        } catch (Exception e) {
            log.error("OzonAdjustPriceSkuMqListener --> 处理失败，原因：{}", e.getMessage(), e);
            channel.basicNack(message.getMessageProperties().getDeliveryTag(), false, false);
        }
    }

    private void doService(OzonAdjustPriceJobParam ozonOnlineConfig) {
        log.info("OzonAdjustPriceSkuMqListener --> 开始处理ozonOnlineConfig：{}", ozonOnlineConfig);
        ApiResult<Map<String, List<OzonWareHouseInfoVO>>> accountWareHouseInfo = ozonAccountConfigService.getAccountWareHouseInfo(List.of(ozonOnlineConfig.getAccountNumber()));
        OzonAccountConfig ozonAccountConfig = ozonAccountConfigService.selectByAccountNumber(ozonOnlineConfig.getAccountNumber());
        Map<String, List<OzonWareHouseInfoVO>> result = Optional.ofNullable(accountWareHouseInfo.getResult()).orElse(new HashMap<>());
        List<OzonWareHouseInfoVO> ozonWareHouseInfoVOS = Optional.ofNullable(result.get(ozonOnlineConfig.getAccountNumber())).orElse(new ArrayList<>());
        Map<String, String> wareIdAndNameMap = ozonWareHouseInfoVOS.stream().collect(Collectors.toMap(OzonWareHouseInfoVO::getWarehouseId, OzonWareHouseInfoVO::getName, (oldV, newV) -> newV));

        String accountNumber = ozonOnlineConfig.getAccountNumber();
        List<String> skuList = ozonOnlineConfig.getSkuList();
        if (CollectionUtils.isEmpty(skuList)) {
            skuList = getSkuList();
        }
        // sku 调价
        List<List<String>> lists = PagingUtils.newPagingList(skuList, 300);
        List<OzonAdjustPriceSkuLog> errorList = new ArrayList<>();
        List<OzonAdjustPriceContext> callPriceList = new ArrayList<>();
        for (List<String> list : lists) {
            // 进行调价
            EsOzonItemRequest request = new EsOzonItemRequest();
            request.setSkus(list);
            request.setAccountNumber(accountNumber);
            request.setFields(new String[]{"id", "accountNumber", "productId", "sellerSku", "sku", "priceNumber", "warehouseStockInfos", "currencyCode",
                    "actualWeight", "linkTag", "specialGoodsCode", "skuCount"});
            List<EsOzonItem> esOzonItems = esOzonItemService.listItemByRequest(request);
            // 首先判断是否有库存。库存大于0的才进行调价
            LocalDateTime now = LocalDateTime.now();

            Set<String> existSkuSet = esOzonItems.stream().map(a -> a.getSku()).collect(Collectors.toSet());
//             将没有存在的创建错误信息
            List<String> notExistSkuList = list.stream().filter(a -> !existSkuSet.contains(a)).collect(Collectors.toList());
            for (String sku : notExistSkuList) {
                OzonAdjustPriceSkuLog ozonAdjustPriceSkuLog = createdErrorLog(accountNumber, sku, "sku不存在", now);
//                errorList.add(ozonAdjustPriceSkuLog);
            }
            for (EsOzonItem esOzonItem : esOzonItems) {
                List<EsOzonStockInfo> warehouseStockInfos = Optional.ofNullable(esOzonItem.getWarehouseStockInfos()).orElse(new ArrayList<>())
                        .stream().filter(Objects::nonNull).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(warehouseStockInfos)) {
                    // 记录日志
                    OzonAdjustPriceSkuLog ozonAdjustPriceSkuLog = createdErrorLog(esOzonItem, null, "仓库库存为空", now, wareIdAndNameMap);
                    errorList.add(ozonAdjustPriceSkuLog);
                    continue;
                }
                EsOzonStockInfo maxStockInfo = null;
                for (EsOzonStockInfo warehouseStockInfo : warehouseStockInfos) {
                    if (maxStockInfo == null) {
                        maxStockInfo = warehouseStockInfo;
                        continue;
                    }
                    Long present = maxStockInfo.getPresent();
                    Long present1 = warehouseStockInfo.getPresent();
                    if (present1 != null && present != null && present1 > present) {
                        maxStockInfo = warehouseStockInfo;
                    }
                }
                Long present = maxStockInfo.getPresent();
                if (present == null) {
                    // 记录日志
                    OzonAdjustPriceSkuLog ozonAdjustPriceSkuLog = createdErrorLog(esOzonItem, maxStockInfo, "仓库库存为空", now, wareIdAndNameMap);
                    errorList.add(ozonAdjustPriceSkuLog);
                    continue;
                }
                if (present == 0L) {
                    // 记录日志
                    OzonAdjustPriceSkuLog ozonAdjustPriceSkuLog = createdErrorLog(esOzonItem, maxStockInfo, "仓库库存为0", now, wareIdAndNameMap);
                    errorList.add(ozonAdjustPriceSkuLog);
                    continue;
                }
                OzonAdjustPriceSkuLog ozonAdjustPriceSkuLog = createdDoingLog(esOzonItem, maxStockInfo, now, wareIdAndNameMap);
                String warehouseName = ozonAdjustPriceSkuLog.getWarehouseName();
                if (!OzonWarehouseEnum.allWarehouseLowersContains(warehouseName)) {
                    OzonAdjustPriceSkuLog ozonAdjustPriceSkuLog1 = createdErrorLog(esOzonItem, maxStockInfo, "仓库名称不存在", now, wareIdAndNameMap);
                    errorList.add(ozonAdjustPriceSkuLog1);
                    continue;
                }
                // 调价
                OzonAdjustPriceContext ozonAdjustPriceContext = new OzonAdjustPriceContext();
                ozonAdjustPriceSkuLog = createdDoingLog(esOzonItem, maxStockInfo, now, wareIdAndNameMap);
                ozonAdjustPriceContext.setOzonAdjustPriceSkuLog(ozonAdjustPriceSkuLog);
                ozonAdjustPriceContext.setWarehouseName(ozonAdjustPriceSkuLog.getWarehouseName());
                ozonAdjustPriceContext.setEsOzonItem(esOzonItem);
                ozonAdjustPriceContext.setId(esOzonItem.getId());
                callPriceList.add(ozonAdjustPriceContext);
            }
        }

        // 不为空，进行保存
        if (!CollectionUtils.isEmpty(errorList)) {
            ozonAdjustPriceSkuLogService.saveBatch(errorList, 300);
        }

        doCallPrice(callPriceList, ozonOnlineConfig, ozonAccountConfig);
    }

    /**
     * 判断价格是否合理
     *
     * @param callPriceList list
     * @param param         param
     */
    private void doCallPrice(List<OzonAdjustPriceContext> callPriceList, OzonAdjustPriceJobParam param, OzonAccountConfig ozonAccountConfig) {
        Map<String, OzonAccountConfig> accountConfigMap = new HashMap<>();
        accountConfigMap.put(ozonAccountConfig.getAccountNumber(), ozonAccountConfig);
        // 这里就需要知道是哪个仓库，如果是固定仓库就用固定的算法
        // 区分哪个用哪个算法
        List<OzonAdjustPriceContext> autoCaclList = new ArrayList<>();
        List<OzonAdjustPriceContext> manualCaclList = new ArrayList<>();
        for (OzonAdjustPriceContext ozonAdjustPriceContext : callPriceList) {
            String warehouseName = ozonAdjustPriceContext.getWarehouseName();
            OzonAdjustPriceSkuLog ozonAdjustPriceSkuLog = ozonAdjustPriceContext.getOzonAdjustPriceSkuLog();
            OzonWarehouseEnum warehouseByCode = OzonWarehouseEnum.getWarehouseByCode(warehouseName);
            if (warehouseByCode == null) {
                // 不可能会有这种情况的。上面排查过了
                continue;
            }
            if (OzonWarehouseEnum.allAutoWareHouseLowerCaseContains(warehouseByCode.getCode())) {
                int autoType = OzonWarehouseEnum.estdaWareHouseLowerCaseContains(warehouseByCode.getCode()) ? 1:2;
                ozonAdjustPriceContext.setAutoType(autoType);
                autoCaclList.add(ozonAdjustPriceContext);
                continue;
            }
            String logistics = warehouseByCode.getLogistics();
            ozonAdjustPriceSkuLog.setLogistics(logistics);
            manualCaclList.add(ozonAdjustPriceContext);
        }
        try {
            doManual(param, ozonAccountConfig, manualCaclList, accountConfigMap);
        } catch (Exception e) {
            log.error("手动调价失败：", e);
        }
        try {
            doAuto(param, ozonAccountConfig, autoCaclList);
        } catch (Exception e) {
            log.error("自动调价失败：", e);
        }
        // 开始判断，哪些有改后值了
        List<OzonAdjustPriceSkuLog> resultList = callPriceList.stream().map(OzonAdjustPriceContext::getOzonAdjustPriceSkuLog).collect(Collectors.toList());
        // 开始算价
        List<OzonAdjustPriceSkuLog> updateList = resultList.stream().filter(a -> a.getNewPrice() != null).collect(Collectors.toList());
        List<OzonAdjustPriceSkuLog> errorList = resultList.stream().filter(a -> a.getNewPrice() == null).collect(Collectors.toList());
        for (OzonAdjustPriceSkuLog ozonAdjustPriceSkuLog : errorList) {
            if (ozonAdjustPriceSkuLog.getStatus() == null) {
                ozonAdjustPriceSkuLog.setStatus(0);
                ozonAdjustPriceSkuLog.setRemark("算价失败");
            }
        }
        if (!CollectionUtils.isEmpty(errorList)) {
            ozonAdjustPriceSkuLogService.saveBatch(errorList, 300);
        }

        if (!CollectionUtils.isEmpty(updateList)) {
            doUpdateList(updateList, param, ozonAccountConfig);
        }
    }

    /**
     * 开始算价
     * @param updateList 算价
     */
    private void doUpdateList(List<OzonAdjustPriceSkuLog> updateList, OzonAdjustPriceJobParam param, OzonAccountConfig ozonAccountConfig) {
        for (OzonAdjustPriceSkuLog ozonAdjustPriceSkuLog : updateList) {
            if (ozonAdjustPriceSkuLog.getOldPrice() == null) {
                continue;
            }
            if (Objects.equals(ozonAdjustPriceSkuLog.getOldPrice(), ozonAdjustPriceSkuLog.getNewPrice())) {
                ozonAdjustPriceSkuLog.setStatus(0);
                ozonAdjustPriceSkuLog.setRemark("改前改后价格相同，不做修改");
                continue;
            }
            // 判断改前价格是否大于改后价格
            if (ozonAdjustPriceSkuLog.getOldPrice().compareTo(ozonAdjustPriceSkuLog.getNewPrice()) < 0) {
                ozonAdjustPriceSkuLog.setStatus(0);
                ozonAdjustPriceSkuLog.setRemark("改前价格小于改后价格，不做修改");
            }
        }

        // 价格判断好后，开始修改
        List<OzonAdjustPriceSkuLog> collect = updateList.stream().filter(a -> a.getStatus() == null).collect(Collectors.toList());
        List<OzonAdjustPriceSkuLog> errorList = updateList.stream().filter(a -> a.getStatus() != null).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(errorList)) {
            ozonAdjustPriceSkuLogService.saveBatch(errorList, 300);
        }
        if (CollectionUtils.isEmpty(collect)) {
            return;
        }
        // 如果不提交的线上，就需要直接终端
        if (!param.getIsPushLine()) {
            for (OzonAdjustPriceSkuLog ozonAdjustPriceSkuLog : collect) {
                ozonAdjustPriceSkuLog.setRemark("不提交到线上，等待检查");
                ozonAdjustPriceSkuLog.setStatus(-1);
            }
            ozonAdjustPriceSkuLogService.saveBatch(collect, 300);
            return;
        }

        List<OzonUpdateDO> updateParam = new ArrayList<>();
        for (OzonAdjustPriceSkuLog adjustPriceSkuLog : collect) {
            OzonUpdateDO ozonUpdateDO = new OzonUpdateDO();
            ozonUpdateDO.setProductId(adjustPriceSkuLog.getProductId());
            ozonUpdateDO.setAccountNumber(adjustPriceSkuLog.getAccountNumber());
            ozonUpdateDO.setSellerSku(adjustPriceSkuLog.getSellerSku());
            ozonUpdateDO.setSku(adjustPriceSkuLog.getSku());
            ozonUpdateDO.setUpdateBeforePrice(adjustPriceSkuLog.getOldPrice() != null ? adjustPriceSkuLog.getOldPrice().toString() : null);
            ozonUpdateDO.setUpdateAfterPrice(adjustPriceSkuLog.getNewPrice() != null ? adjustPriceSkuLog.getNewPrice().toString() : null);
            ozonUpdateDO.setJob("adjustPriceSku");
            ozonUpdateDO.setCurrencyCode(ozonAccountConfig.getCurrency());
            updateParam.add(ozonUpdateDO);
        }
        for (List<OzonUpdateDO> ozonUpdateDOS : PagingUtils.newPagingList(updateParam, 500)) {
            if (CollectionUtils.isEmpty(ozonUpdateDOS)) {
                continue;
            }
            ozonUpdateHandler.updatePrice(ozonUpdateDOS.get(0).getAccountNumber(), ozonUpdateDOS, OzonUpdatePriceTypeEnum.UPDATE_PRICE, true);
        }


        // 处理完后，开始处理excel表格，查看情况
        Map<Long, OzonUpdateDO> productIdAndUpdateResultMap = updateParam.stream().collect(Collectors.toMap(OzonUpdateDO::getProductId, Function.identity()));
        for (OzonAdjustPriceSkuLog updateStockExcel : collect) {
            Long productId = updateStockExcel.getProductId();
            OzonUpdateDO ozonUpdateDO = productIdAndUpdateResultMap.get(productId);
            if (ozonUpdateDO == null) {
                updateStockExcel.setStatus(0);
                updateStockExcel.setRemark("执行修改接口后数据丢失");
                continue;
            }
            boolean success = ozonUpdateDO.isSuccess();
            if (success) {
                updateStockExcel.setStatus(1);
            } else {
                updateStockExcel.setStatus(0);
                updateStockExcel.setRemark(ozonUpdateDO.getErrorMsg());
            }
        }
        ozonAdjustPriceSkuLogService.saveBatch(collect, 300);
    }

    private void doAuto(OzonAdjustPriceJobParam param, OzonAccountConfig ozonAccountConfig, List<OzonAdjustPriceContext> autoCaclList) {
        if (CollectionUtils.isEmpty(autoCaclList)) {
            return;
        }
        Map<String, OzonAccountConfig> accountConfigMap = new HashMap<>();
        accountConfigMap.put(ozonAccountConfig.getAccountNumber(), ozonAccountConfig);
        List<OzonEstdaLogisticsRule> allRulesByCache = ozonEstdaLogisticsRuleService.getAllRulesByCache();
        List<OzonCalcPriceRequest> requests = new ArrayList<>();
        for (OzonAdjustPriceContext context : autoCaclList) {
            try {
                OzonCalcPriceRequest request = new OzonCalcPriceRequest();
                EsOzonItem esOzonItem = context.getEsOzonItem();
                request.setBusinessId(esOzonItem.getId());
                request.setSpecialGoodsCode(esOzonItem.getSpecialGoodsCode());
                request.setLinkTag(esOzonItem.getLinkTag());
                request.setGrossProfitRate(param.getGrossProfitRate());
                request.setSku(esOzonItem.getSku());
                request.setSkuCount(esOzonItem.getSkuCount());
                request.setSite(OzonSiteCurrencyEnum.getSiteByCurrency(esOzonItem.getCurrencyCode()));
                request.setAccountNumber(ozonAccountConfig.getAccountNumber());
                request.setAutoType(context.getAutoType());
                Double actualWeight = esOzonItem.getActualWeight();
                if (actualWeight != null) {
                    request.setWeight(actualWeight * 1000);
                }
                OzonCalcUtils.setCalcLogistics(request, allRulesByCache);
                requests.add(request);
            } catch (Exception e) {
                log.error("自动计算价格异常", e);
                OzonAdjustPriceSkuLog ozonAdjustPriceSkuLog = context.getOzonAdjustPriceSkuLog();
                ozonAdjustPriceSkuLog.setRemark(e.getMessage());
                ozonAdjustPriceSkuLog.setStatus(0);
            }
        }

        if (!CollectionUtils.isEmpty(requests)) {
            doCalc(autoCaclList, requests, accountConfigMap);
        }
    }

    private static void doCalc(List<OzonAdjustPriceContext> autoCaclList, List<OzonCalcPriceRequest> requests, Map<String, OzonAccountConfig> accountConfigMap) {
        Map<String, OzonAdjustPriceContext> idAndContextMap = autoCaclList.stream()
                .collect(Collectors.toMap(OzonAdjustPriceContext::getId, a -> a, (odv, newV) -> newV));
        List<List<OzonCalcPriceRequest>> lists = PagingUtils.newPagingList(requests, 100);
        for (List<OzonCalcPriceRequest> list : lists) {
            try {
                ApiResult<List<BatchPriceCalculatorResponse>> apiResult = OzonCalcUtils.multiBatchPriceCalculator(list);
                if (apiResult.isSuccess()) {
                    List<OzonCalcPriceResponse> ozonCalcPriceResponses = OzonCalcUtils.buildResponse(apiResult.getResult(), list, accountConfigMap);
                    for (OzonCalcPriceResponse ozonCalcPriceResponse : ozonCalcPriceResponses) {
                        String businessId = ozonCalcPriceResponse.getBusinessId();
                        OzonAdjustPriceContext ozonAdjustPriceContext = idAndContextMap.get(businessId);
                        OzonAdjustPriceSkuLog ozonAdjustPriceSkuLog = ozonAdjustPriceContext.getOzonAdjustPriceSkuLog();
                        if (ozonCalcPriceResponse.getIsSuccess()) {
                            ozonAdjustPriceSkuLog.setLogistics(ozonCalcPriceResponse.getCalcLogistics());
                            ozonAdjustPriceSkuLog.setNewPrice(BigDecimal.valueOf(ozonCalcPriceResponse.getSalePrice()));
                        } else {
                            ozonAdjustPriceSkuLog.setRemark(ozonCalcPriceResponse.getErrorMsg());
                            ozonAdjustPriceSkuLog.setStatus(0);
                        }
                    }
                } else {
                    for (OzonCalcPriceRequest request : list) {
                        String businessId = request.getBusinessId();
                        OzonAdjustPriceContext ozonAdjustPriceContext = idAndContextMap.get(businessId);
                        OzonAdjustPriceSkuLog ozonAdjustPriceSkuLog = ozonAdjustPriceContext.getOzonAdjustPriceSkuLog();
                        ozonAdjustPriceSkuLog.setRemark(apiResult.getErrorMsg());
                        ozonAdjustPriceSkuLog.setStatus(0);
                    }
                }
            } catch (Exception e) {
                // 异常了。
                for (OzonCalcPriceRequest request : list) {
                    String businessId = request.getBusinessId();
                    OzonAdjustPriceContext ozonAdjustPriceContext = idAndContextMap.get(businessId);
                    OzonAdjustPriceSkuLog ozonAdjustPriceSkuLog = ozonAdjustPriceContext.getOzonAdjustPriceSkuLog();
                    ozonAdjustPriceSkuLog.setRemark(e.getMessage());
                    ozonAdjustPriceSkuLog.setStatus(0);
                }
            }
        }
    }

    private void doManual(OzonAdjustPriceJobParam param, OzonAccountConfig ozonAccountConfig, List<OzonAdjustPriceContext> manualCaclList, Map<String, OzonAccountConfig> accountConfigMap) {
        if (CollectionUtils.isEmpty(manualCaclList)) {
            return;
        }
        List<OzonCalcPriceRequest> requests = new ArrayList<>();
        for (OzonAdjustPriceContext ozonAdjustPriceContext : manualCaclList) {
            try {
                OzonAdjustPriceSkuLog ozonAdjustPriceSkuLog = ozonAdjustPriceContext.getOzonAdjustPriceSkuLog();
                EsOzonItem esOzonItem = ozonAdjustPriceContext.getEsOzonItem();
                OzonCalcPriceRequest request = new OzonCalcPriceRequest();
                request.setSku(esOzonItem.getSku());
                request.setCurrency(esOzonItem.getCurrencyCode());
                request.setGrossProfitRate(param.getGrossProfitRate());
                request.setBusinessId(esOzonItem.getId());
                request.setSkuCount(esOzonItem.getSkuCount());
                request.setCalcLogistics(ozonAdjustPriceSkuLog.getLogistics());
                request.setAccountNumber(ozonAccountConfig.getAccountNumber());
                checkAndBuildManualRequest(request, ozonAccountConfig);
                requests.add(request);
            } catch (Exception e) {
                log.error("自动计算价格异常", e);
                OzonAdjustPriceSkuLog ozonAdjustPriceSkuLog = ozonAdjustPriceContext.getOzonAdjustPriceSkuLog();
                ozonAdjustPriceSkuLog.setRemark(e.getMessage());
                ozonAdjustPriceSkuLog.setStatus(0);
            }
        }

        if (!CollectionUtils.isEmpty(requests)) {
            doCalc(manualCaclList, requests, accountConfigMap);
        }
    }


    private static void checkAndBuildManualRequest(OzonCalcPriceRequest request, OzonAccountConfig ozonAccountConfig) {
        String sku = request.getSku();
        if (StringUtils.isBlank(sku)) {
            throw new RuntimeException("sku不可以为空");
        }

        // 币种
        String currency = request.getCurrency();
        if (StringUtils.isBlank(currency)) {
            currency = ozonAccountConfig.getCurrency();
        }
        if (StringUtils.isBlank(currency)) {
            throw new RuntimeException("币种不可以为空");
        }

        // 站点
        request.setSite(OzonSiteCurrencyEnum.getSiteByCurrency(currency));

        // 毛利率
        Double grossProfitRate = request.getGrossProfitRate();
        if (null == grossProfitRate) {
            throw new RuntimeException("毛利率不可以为空");
        }

        // 算价物流
        String calcLogistics = request.getCalcLogistics();
        if (StringUtils.isBlank(calcLogistics)) {
            throw new RuntimeException("算价物流不可以为空");
        }
    }


    private OzonAdjustPriceSkuLog createdDoingLog(EsOzonItem esOzonItem, EsOzonStockInfo maxStockInfo, LocalDateTime now, Map<String, String> wareIdAndNameMap) {
        OzonAdjustPriceSkuLog ozonAdjustPriceSkuLog = new OzonAdjustPriceSkuLog();
        ozonAdjustPriceSkuLog.setAccountNumber(esOzonItem.getAccountNumber());
        ozonAdjustPriceSkuLog.setSku(esOzonItem.getSku());
        ozonAdjustPriceSkuLog.setOldPrice(esOzonItem.getPriceNumber() != null ? BigDecimal.valueOf(esOzonItem.getPriceNumber()) : null);
        ozonAdjustPriceSkuLog.setWarehouseId(maxStockInfo.getWarehouseId());
        ozonAdjustPriceSkuLog.setWarehouseName(wareIdAndNameMap.get(maxStockInfo.getWarehouseId() != null ? maxStockInfo.getWarehouseId().toString() : ""));
        ozonAdjustPriceSkuLog.setProductId(esOzonItem.getProductId());
        ozonAdjustPriceSkuLog.setSellerSku(esOzonItem.getSellerSku());
        ozonAdjustPriceSkuLog.setCreatedTime(now);
        return ozonAdjustPriceSkuLog;
    }


    private OzonAdjustPriceSkuLog createdErrorLog(EsOzonItem esOzonItem, EsOzonStockInfo maxStockInfo, String msg, LocalDateTime now, Map<String, String> wareIdAndNameMap) {
        OzonAdjustPriceSkuLog ozonAdjustPriceSkuLog = new OzonAdjustPriceSkuLog();
        ozonAdjustPriceSkuLog.setAccountNumber(esOzonItem.getAccountNumber());
        ozonAdjustPriceSkuLog.setSku(esOzonItem.getSku());
        ozonAdjustPriceSkuLog.setOldPrice(esOzonItem.getPriceNumber() != null ? BigDecimal.valueOf(esOzonItem.getPriceNumber()) : null);
        ozonAdjustPriceSkuLog.setStatus(0);
        if (maxStockInfo != null) {
            ozonAdjustPriceSkuLog.setWarehouseId(maxStockInfo.getWarehouseId());
            ozonAdjustPriceSkuLog.setWarehouseName(wareIdAndNameMap.get(maxStockInfo.getWarehouseId() != null ? maxStockInfo.getWarehouseId().toString() : ""));
        }
        ozonAdjustPriceSkuLog.setProductId(esOzonItem.getProductId());
        ozonAdjustPriceSkuLog.setSellerSku(esOzonItem.getSellerSku());
        ozonAdjustPriceSkuLog.setCreatedTime(now);
        ozonAdjustPriceSkuLog.setRemark(msg);
        return ozonAdjustPriceSkuLog;
    }

    private OzonAdjustPriceSkuLog createdErrorLog(String accountNumber, String sku, String msg, LocalDateTime now) {
        OzonAdjustPriceSkuLog ozonAdjustPriceSkuLog = new OzonAdjustPriceSkuLog();
        ozonAdjustPriceSkuLog.setAccountNumber(accountNumber);
        ozonAdjustPriceSkuLog.setSku(sku);
        ozonAdjustPriceSkuLog.setStatus(0);
        ozonAdjustPriceSkuLog.setCreatedTime(now);
        ozonAdjustPriceSkuLog.setRemark(msg);
        return ozonAdjustPriceSkuLog;
    }

    private List<String> getSkuList() {
        List<String> skuList = new ArrayList<>();
        int current = 1;
        while (true) {
            IPage<OzonAdjustPriceSku> page = new Page<>();
            page.setCurrent(current);
            page.setPages(300);
            current++;
            LambdaQueryWrapper<OzonAdjustPriceSku> queryWrapper = new LambdaQueryWrapper<>();
            IPage<OzonAdjustPriceSku> resultPage = ozonAdjustPriceSkuService.page(page, queryWrapper);
            List<OzonAdjustPriceSku> records = resultPage.getRecords();
            if (CollectionUtils.isEmpty(records)) {
                break;
            }
            List<String> collect = records.stream().map(OzonAdjustPriceSku::getSku).collect(Collectors.toList());
            skuList.addAll(collect);
        }
        return skuList;
    }

}

package com.estone.erp.publish.ozon.mq.listener;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.estone.erp.common.util.BeanUtil;
import com.estone.erp.common.util.StrUtil;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.common.json.ResponseJson;
import com.estone.erp.publish.common.util.PagingUtils;
import com.estone.erp.publish.ozon.common.OzonExecutors;
import com.estone.erp.publish.ozon.enums.OzonConfigTypeEnums;
import com.estone.erp.publish.ozon.enums.OzonPublishOpTypeEnum;
import com.estone.erp.publish.ozon.enums.OzonTimePublishEnums;
import com.estone.erp.publish.ozon.enums.QueueConfigSourceEnum;
import com.estone.erp.publish.ozon.model.*;
import com.estone.erp.publish.ozon.model.dto.marketing.OzonConfigOperatorStatusEnum;
import com.estone.erp.publish.ozon.model.dto.online.OnlineConfigRuleJson;
import com.estone.erp.publish.ozon.service.*;
import com.estone.erp.publish.ozon.utils.OnlineConfigUtils;
import com.estone.erp.publish.platform.enums.SkuDataSourceEnum;
import com.estone.erp.publish.platform.service.UnpublishCategoryService;
import com.estone.erp.publish.system.product.ProductInfo;
import com.estone.erp.publish.system.product.ProductUtils;
import com.estone.erp.publish.system.product.bean.SkuListAndCode;
import com.estone.erp.publish.system.product.response.QuerySpuByConditionVo;
import com.estone.erp.publish.system.product.util.SingleItemEsUtils;
import com.rabbitmq.client.Channel;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.listener.api.ChannelAwareMessageListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cglib.beans.BeanCopier;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.nio.charset.StandardCharsets;
import java.sql.Timestamp;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.stream.Collectors;

/**
 * @Description: ozon配置上架消息监听器
 * <AUTHOR>
 * @Date 2025/3/27 下午5:23
 */
@Slf4j
public class OzonConfigPublishMqListener implements ChannelAwareMessageListener {

    // 内存优化：批处理大小常量定义
    private static final int BATCH_SIZE = 500; // SPU批处理大小
    private static final int LOG_BATCH_SIZE = 200; // 日志批处理大小
    private static final int MAX_QUEUE_SIZE = 1000; // 队列最大大小限制

    // 内存优化：重用的时间格式化器，避免重复创建
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    private static final DateTimeFormatter TIME_FORMATTER = DateTimeFormatter.ofPattern("HH:mm");

    @Resource
    private OzonConfigTaskService ozonConfigTaskService;

    @Autowired
    private UnpublishCategoryService unpublishCategoryService;

    @Autowired
    private OzonAccountConfigService ozonAccountConfigService;

    @Autowired
    private OzonTimePublishQueueService ozonTimePublishQueueService;

    @Autowired
    private OzonOnlineConfigService ozonOnlineConfigService;

    @Resource
    private OzonPublishOperationLogService ozonPublishOperationLogService;

    @Override
    public void onMessage(Message message, Channel channel) throws Exception {
        String body = new String(message.getBody(), StandardCharsets.UTF_8);
        OzonOnlineConfig ozonOnlineConfig = JSON.parseObject(body, new TypeReference<>() {
        });
        try {
            // 请求产品spu列表接口数据
            OnlineConfigRuleJson ruleConfigJson = (OnlineConfigRuleJson) OnlineConfigUtils.ruleJson(ozonOnlineConfig);
            List<QuerySpuByConditionVo> querySpuByConditionVoList = OnlineConfigUtils.requestProductSpuList(ruleConfigJson, false);
            if (CollectionUtils.isEmpty(querySpuByConditionVoList)) {
                channel.basicNack(message.getMessageProperties().getDeliveryTag(), false, false);
                return;
            }
            // 转换避免操作共享变量
            BeanCopier copier = BeanUtil.createCopier(QuerySpuByConditionVo.class, QuerySpuByConditionVo.class);

            // 按照店铺的维度刊登数据
            List<String> accounts = Arrays.stream(StrUtil.strDeldComma(ozonOnlineConfig.getAccounts()).split(",")).collect(Collectors.toList());
            // 多线程同步执行
            CompletableFuture.allOf(accounts.stream().map(account -> CompletableFuture.runAsync(() -> {
                // 初始化任务
                OzonConfigTask ozonConfigTask = this.initOzonConfigTask(account, ozonOnlineConfig);
                try {
                    // 0 首先检查商品总限额数和额度使用率
                    OzonAccountConfig ozonAccountConfig = ozonAccountConfigService.selectByAccountNumber(account);
                    checkProductNumber(ozonAccountConfig, ruleConfigJson);
                    checkCreditUsageRate(ozonAccountConfig, ruleConfigJson);
                    // 计算店铺剩余额度（店铺限额数-所有商品数）
                    Integer allProductNumber = ozonAccountConfig.getAllProductNumber();
                    Integer accountLimitNumber = ozonAccountConfig.getAccountLimitNumber();
                    int remainingQuota = 0;
                    if (allProductNumber != null && accountLimitNumber != null) {
                        remainingQuota = accountLimitNumber - allProductNumber;
                        if (remainingQuota < 0) {
                            remainingQuota = 0;
                        }
                    }
                    // 获取店铺当日刊登额度
                    Integer dailyCreateLimit = ozonAccountConfig.getDailyCreateLimit();
                    if (dailyCreateLimit == null) {
                        // 取最大
                        dailyCreateLimit = Integer.MAX_VALUE;
                    }
                    Integer maxNumber = ozonOnlineConfig.getMaxNumber();
                    // 取三者最小数生成队列
                    int minNumber = Math.min(Math.min(dailyCreateLimit, remainingQuota), maxNumber);
                    // 1、获取维度占比率
                    Map<String, Double> inputTimeMap = OnlineConfigUtils.getInputTimeMap(ozonAccountConfig, minNumber, ruleConfigJson);

                    // 2、获取满足条件的产品spu列表
                    List<QuerySpuByConditionVo> querySpuByConditionVos = getQuerySpuByConditionVoList(account, ozonConfigTask.getId(), querySpuByConditionVoList, copier);

                    // 3、根据时间区间取对应比例的产品信息
                    List<String> productInfoByTimeRangeAndRatio = getProductInfoByTimeRangeAndRatio(account, inputTimeMap, querySpuByConditionVos);

                    // 4、保存其余的SPU到等待刊登队列
                    savePublishQueue(ozonAccountConfig, ozonOnlineConfig, productInfoByTimeRangeAndRatio);

                    Map<String, Object> params = new HashMap<>();
                    params.put("accountLimitNumber", Optional.ofNullable(accountLimitNumber).orElse(-1));
                    params.put("dailyCreateLimit", Optional.ofNullable(dailyCreateLimit).orElse(-1));
                    params.put("allProductNumber", Optional.ofNullable(allProductNumber).orElse(-1));
                    params.put("maxNumber", Optional.ofNullable(ozonOnlineConfig.getMaxNumber()).orElse(-1));
                    params.put("minNumber", minNumber);
                    params.put("remainingQuota", remainingQuota);
                    params.put("inputTimeMap", inputTimeMap);
                    params.put("spuSIze", productInfoByTimeRangeAndRatio.size());
                    // 5、更新任务状态为 已执行
                    ozonConfigTaskService.successTask(ozonConfigTask.getId(), "执行成功：" + JSON.toJSONString(params));
                } catch (Exception e) {
                    ozonConfigTaskService.failTask(ozonConfigTask.getId(), e.getMessage());
                }
            }, OzonExecutors.CONFIG_PUBLISH_PRODUCT_POOL)).toArray(CompletableFuture[]::new)).join();
            channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
        } catch (Exception e) {
            log.error("OzonConfigPublishMqListener --> 处理失败， body:{}, 原因：{}", body, e.getMessage(), e);
            channel.basicNack(message.getMessageProperties().getDeliveryTag(), false, false);
        }
    }

    private void savePublishQueue(OzonAccountConfig accountConfig, OzonOnlineConfig ozonOnlineConfig, List<String> spuList) {
        try {
            // 内存优化：分批处理大量 SPU 数据，避免一次性创建大量对象
            if (CollectionUtils.isEmpty(spuList)) {
                return;
            }

            // 添加刊登队列
            Timestamp timestamp = new Timestamp(System.currentTimeMillis());
            String execTime = ozonOnlineConfig.getExecTime();
            // 内存优化：重用静态格式化器
            LocalTime execTimeLocalTime = LocalTime.parse(execTime.trim(), TIME_FORMATTER);
            LocalDateTime execTimeLocalDateTime = LocalDateTime.of(LocalDate.now().plusDays(1), execTimeLocalTime);
            Timestamp publishTime = Timestamp.valueOf(execTimeLocalDateTime);

            // 内存优化：分批处理，避免一次性创建大量对象
            for (int i = 0; i < spuList.size(); i += BATCH_SIZE) {
                int endIndex = Math.min(i + BATCH_SIZE, spuList.size());
                List<String> batchSpuList = spuList.subList(i, endIndex);

                // 内存优化：使用 LinkedHashSet 去重，避免 Stream 的 distinct() 操作
                Set<String> uniqueSpuSet = new LinkedHashSet<>(batchSpuList);
                List<OzonTimePublishQueue> publishQueueList = new ArrayList<>(uniqueSpuSet.size());

                for (String spu : uniqueSpuSet) {
                    OzonTimePublishQueue timePublishQueue = new OzonTimePublishQueue();
                    timePublishQueue.setTemplateId(0);
                    timePublishQueue.setAccountNumber(accountConfig.getAccountNumber());
                    timePublishQueue.setArticleNumber(spu);
                    timePublishQueue.setSkuDataSource(SkuDataSourceEnum.PRODUCT_SYSTEM.getCode());
                    timePublishQueue.setTitle(null);
                    timePublishQueue.setStatus(OzonTimePublishEnums.Status.WAITING.getCode());
                    timePublishQueue.setPublishStatus(null);
                    timePublishQueue.setExtra(null);
                    timePublishQueue.setCreatedBy("admin");
                    timePublishQueue.setCreatedTime(timestamp);
                    timePublishQueue.setPublishTime(publishTime);
                    timePublishQueue.setConfigSource(QueueConfigSourceEnum.ONLINE_CONFIG.getCode());
                    timePublishQueue.setRuleName(ozonOnlineConfig.getName());
                    timePublishQueue.setRuleJson(ozonOnlineConfig.getRuleConfigJson());
                    timePublishQueue.setUpdateTime(timestamp);
                    publishQueueList.add(timePublishQueue);
                }

                // 内存优化：分批插入数据库，避免一次性插入大量数据
                if (!publishQueueList.isEmpty()) {
                    ozonTimePublishQueueService.batchInsert(publishQueueList);
                    publishQueueList.clear(); // 及时清理集合
                }
            }
        } catch (Exception e) {
            throw new RuntimeException("保存刊登队列失败,原因: " + e.getMessage());
        }
    }

    private void checkProductNumber(OzonAccountConfig ozonAccountConfig, OnlineConfigRuleJson ruleConfigJson) {
        Integer productNumberStart = ruleConfigJson.getProductNumberStart();
        Integer productNumberEnd = ruleConfigJson.getProductNumberEnd();
        if (productNumberEnd != null || productNumberStart != null) {

            Integer allProductNumber = ozonAccountConfig.getAllProductNumber();
            Integer accountLimitNumber = ozonAccountConfig.getAccountLimitNumber();
            if (allProductNumber == null || accountLimitNumber == null) {
                throw new RuntimeException("所有商品数或店铺限额数为空");
            }
            int temp = accountLimitNumber - allProductNumber;
            if (productNumberEnd != null) {
                if (temp >= productNumberEnd) {
                    String format = String.format("商品限额数[%s] - 所有商品数[%s] = [%s] >= 商品总限额数结束值[%s]", accountLimitNumber, allProductNumber, temp, productNumberEnd);
                    throw new RuntimeException(format);
                }
            }
            if (productNumberStart != null) {
                if (temp < productNumberStart) {
                    String format = String.format("商品限额数[%s] - 所有商品数[%s] = [%s] < 商品总限额数开始值[%s]", accountLimitNumber, allProductNumber, temp, productNumberStart);
                    throw new RuntimeException(format);
                }
            }
        }
    }

    /**
     * 检查额度使用率
     * 额度使用率=所有商品数/店铺限制数
     *
     * @param ozonAccountConfig 店铺配置
     * @param ruleConfigJson    规则配置
     */
    private void checkCreditUsageRate(OzonAccountConfig ozonAccountConfig, OnlineConfigRuleJson ruleConfigJson) {
        Double creditUsageRateStart = ruleConfigJson.getCreditUsageRateStart();
        Double creditUsageRateEnd = ruleConfigJson.getCreditUsageRateEnd();
        if (creditUsageRateStart != null || creditUsageRateEnd != null) {
            Integer allProductNumber = ozonAccountConfig.getAllProductNumber();
            Integer accountLimitNumber = ozonAccountConfig.getAccountLimitNumber();
            if (allProductNumber == null || accountLimitNumber == null) {
                throw new RuntimeException("所有商品数或店铺限额数为空");
            }
            if (accountLimitNumber == 0) {
                throw new RuntimeException("店铺限额数不能为0");
            }

            // 计算额度使用率 = 所有商品数 / 店铺限制数
            double creditUsageRate = (double) allProductNumber / accountLimitNumber;

            if (creditUsageRateStart != null && creditUsageRate < creditUsageRateStart) {
                String format = String.format("额度使用率[%s] < 额度使用率起始值[%s]", creditUsageRate, creditUsageRateStart);
                throw new RuntimeException(format);
            }

            if (creditUsageRateEnd != null && creditUsageRate >= creditUsageRateEnd) {
                String format = String.format("额度使用率[%s] >= 额度使用率结束值[%s]", creditUsageRate, creditUsageRateEnd);
                throw new RuntimeException(format);
            }
        }
    }

    /**
     * 根据时间区间取对应比例的产品信息
     * 内存优化：使用 ConcurrentLinkedQueue 和分批处理避免大集合内存占用
     *
     * @param account                店铺
     * @param inputTimeMap           时间区间占比率
     * @param querySpuByConditionVos 产品spu列表
     * @return
     */
    private List<String> getProductInfoByTimeRangeAndRatio(String account, Map<String, Double> inputTimeMap, List<QuerySpuByConditionVo> querySpuByConditionVos) {
        // 内存优化：使用线程安全的队列替代 ArrayList，减少内存占用
        ConcurrentLinkedQueue<String> executedSpuQueue = new ConcurrentLinkedQueue<>();
        ConcurrentLinkedQueue<String> resultQueue = new ConcurrentLinkedQueue<>();

        // 内存优化：分批处理，避免一次性处理大量数据
        for (QuerySpuByConditionVo querySpuByConditionVo : querySpuByConditionVos) {
            // 内存优化：重用格式化器，减少对象创建
            String inputTimeStart = formattedDateOptimized(querySpuByConditionVo.getInputTimeStart());
            String inputTimeEnd = formattedDateOptimized(querySpuByConditionVo.getInputTimeEnd());

            // 内存优化：使用 StringBuilder 减少字符串拼接开销
            StringBuilder inputTimeBuilder = new StringBuilder(inputTimeStart.length() + inputTimeEnd.length() + 1);
            String inputTime = inputTimeBuilder.append(inputTimeStart).append("/").append(inputTimeEnd).toString();

            Double availableStockRatio = inputTimeMap.getOrDefault(inputTime, 0.0);
            BigDecimal availableStockRatioDecimal = new BigDecimal(availableStockRatio).setScale(2, RoundingMode.DOWN);

            // 内存优化：直接处理集合，避免创建中间 Stream 集合
            Collection<String> spuList = querySpuByConditionVo.getSpuList();
            if (CollectionUtils.isEmpty(spuList)) {
                continue;
            }

            // 内存优化：分批过滤和处理，控制内存使用
            int processedCount = 0;
            int targetCount = availableStockRatioDecimal.intValue();

            for (String spu : spuList) {
                if (processedCount >= targetCount) {
                    break;
                }

                // 内存优化：使用队列的 contains 方法，避免转换为 List
                if (!executedSpuQueue.contains(spu)) {
                    resultQueue.offer(spu);
                    executedSpuQueue.offer(spu);
                    processedCount++;

                    // 内存优化：控制队列大小，防止内存溢出
                    if (executedSpuQueue.size() > MAX_QUEUE_SIZE) {
                        // 清理部分旧数据，保持队列大小在合理范围内
                        for (int i = 0; i < BATCH_SIZE && !executedSpuQueue.isEmpty(); i++) {
                            executedSpuQueue.poll();
                        }
                    }
                }
            }

            // 记录日志
            log.info("OzonConfigPublishMqListener --> 店铺{},当前区间：{}，占比：{}，SPU数量：{}，区间过滤后待发布数：{}",
                    account, inputTime, availableStockRatioDecimal, spuList.size(), processedCount);
        }

        // 内存优化：转换为 ArrayList 并立即清理队列
        List<String> result = new ArrayList<>(resultQueue);
        resultQueue.clear();
        executedSpuQueue.clear();

        return result;
    }

    /**
     * 获取满足条件的产品spu列表
     * 内存优化：分批处理和减少中间集合创建
     *
     * @param account                   店铺
     * @param querySpuByConditionVoList 符合条件
     * @param copier                    复制器
     * @return 筛选结果
     */
    private List<QuerySpuByConditionVo> getQuerySpuByConditionVoList(String account, Integer taskId, List<QuerySpuByConditionVo> querySpuByConditionVoList, BeanCopier copier) {
        // 内存优化：使用预分配大小的 ArrayList，减少扩容开销
        List<QuerySpuByConditionVo> result = new ArrayList<>(querySpuByConditionVoList.size());

        // 内存优化：分批处理，避免一次性处理大量数据
        for (int i = 0; i < querySpuByConditionVoList.size(); i += BATCH_SIZE) {
            int endIndex = Math.min(i + BATCH_SIZE, querySpuByConditionVoList.size());
            List<QuerySpuByConditionVo> batch = querySpuByConditionVoList.subList(i, endIndex);

            for (QuerySpuByConditionVo querySpuByConditionVo : batch) {
                Collection<String> spuList = querySpuByConditionVo.getSpuList();
                if (CollectionUtils.isEmpty(spuList)) {
                    continue;
                }

                // 内存优化：直接使用原集合，避免创建新的 ArrayList
                List<String> spuListToProcess = (spuList instanceof List) ?
                        (List<String>) spuList : new ArrayList<>(spuList);

                // 1-过滤不刊登类目的产品
                List<List<String>> lists = PagingUtils.newPagingList(spuListToProcess, 1000);
                Set<String> allSpuSet = new HashSet<>();
                for (List<String> list : lists) {
                    Map<String, SkuListAndCode> skuListAndCodeMap = filterProductNotPublishCategory(list);

                    if (MapUtils.isEmpty(skuListAndCodeMap)) {
                        continue;
                    }
                    // 2-过滤ozon禁售的SPU（产品接口过滤）

                    // 3-过滤单品状态为停产,存档,废弃的SKU（产品接口过滤）

                    // 4-过滤重复,店铺在线列表已在线的SKU和等待刊登的队列中的SPU，结束状态的队列才允许重新生成
                    Set<String> execSpuSet = filterRepeatSpu(account, taskId, skuListAndCodeMap);
                    if (!execSpuSet.isEmpty()) {
                        allSpuSet.addAll(execSpuSet);
                    }
                    // 内存优化：及时清理不再使用的 Map
                    skuListAndCodeMap.clear();
                }

                QuerySpuByConditionVo conditionVo = BeanUtil.beanCopierProperties(copier, querySpuByConditionVo, QuerySpuByConditionVo.class);
                // 返回区间可执行的 SPU 集合
                conditionVo.setSpuList(allSpuSet);
                result.add(conditionVo);
            }
        }

        return result;
    }


    /**
     * 过滤重复,店铺在线列表已在线的SKU和等待刊登的队列中的SPU，结束状态的队列才允许重新生成
     *
     * @param account
     * @param skuListAndCodeMap
     * @return 满足条件的 SPU 集合
     */
    private Set<String> filterRepeatSpu(String account, Integer taskId, Map<String, SkuListAndCode> skuListAndCodeMap) {
        Set<String> spuSet = new HashSet<>(skuListAndCodeMap.keySet());
        if (CollectionUtils.isEmpty(spuSet)) {
            return Set.of();
        }
        Set<String> listingExistSpu = ozonOnlineConfigService.getListingExistSpu(account, new ArrayList<>(spuSet));
        batchInsertPublishOperationLog(account, taskId, listingExistSpu, "SPU已存在在线列表中了");
        spuSet.removeAll(listingExistSpu);

        Set<String> existQueue = ozonOnlineConfigService.getExistQueue(account, new ArrayList<>(spuSet));
        batchInsertPublishOperationLog(account, taskId, existQueue, "SPU已存在等待刊登队列中");
        spuSet.removeAll(existQueue);

        return new HashSet<>(spuSet);
    }

    /**
     * 过滤不刊登类目的产品
     *
     * @param spuList spu列表
     * @return
     */
    private Map<String, SkuListAndCode> filterProductNotPublishCategory(List<String> spuList) {
        // 查询sku信息
        ResponseJson responseJson = ProductUtils.findSkuInfos(spuList);
        if (!responseJson.isSuccess()) {
            XxlJobLogger.log("查询sku信息失败：{}", responseJson.getMessage());
            return null;
        }

        List<ProductInfo> productInfos = (List<ProductInfo>) responseJson.getBody().get(ProductUtils.resultKey);
        if (CollectionUtils.isEmpty(productInfos)) {
            XxlJobLogger.log("没有查询到产品信息");
            return null;
        }

        Map<String, SkuListAndCode> spuToCodeMap = SingleItemEsUtils.getSpuToCodeMap(productInfos);
        if (MapUtils.isEmpty(spuToCodeMap)) {
            XxlJobLogger.log("查询到产品信息：{}", spuToCodeMap.keySet());
            return null;
        }

        return unpublishCategoryService.filterUnpublishCategory(spuToCodeMap, SaleChannel.CHANNEL_OZON);

    }

    /**
     * 初始化任务
     *
     * @param account
     * @param ozonListingStatusConfig
     */
    private OzonConfigTask initOzonConfigTask(String account, OzonOnlineConfig ozonListingStatusConfig) {
        Timestamp timestamp = new Timestamp(System.currentTimeMillis());
        OzonConfigTask configTask = new OzonConfigTask();
        configTask.setConfigType(OzonConfigTypeEnums.getOnlineTaskCode(ozonListingStatusConfig.getType()));
        configTask.setConfigId(ozonListingStatusConfig.getId());
        configTask.setConfigName(ozonListingStatusConfig.getName());
        configTask.setConfigRuleJson(ozonListingStatusConfig.getRuleConfigJson());
        configTask.setAccountNumber(account);
        configTask.setOperatorStatus(OzonConfigOperatorStatusEnum.WAITING.getCode());
        configTask.setOperatorTime(timestamp);
        configTask.setCreatedTime(timestamp);
        configTask.setUpdatedTime(timestamp);
        ozonConfigTaskService.insert(configTask);
        return configTask;
    }

    /**
     * 格式化日期
     * 内存优化：重用静态格式化器，减少对象创建开销
     *
     * @param date
     * @return
     */
    private String formattedDate(Date date) {
        LocalDateTime localDateTime = date.toInstant()
                .atZone(ZoneId.systemDefault())
                .toLocalDateTime();

        // 将 LocalDateTime 转换为指定时区的 ZonedDateTime
        ZonedDateTime zonedDateTime = localDateTime.atZone(ZoneId.systemDefault())
                .withZoneSameInstant(ZoneId.of("GMT+8"));

        // 内存优化：使用静态格式化器，避免重复创建
        return zonedDateTime.format(DATE_FORMATTER);
    }

    /**
     * 记录日志
     * 内存优化：分批处理日志插入，避免一次性创建大量日志对象
     *
     * @param accountNumber
     * @param spuSet
     * @param message
     */
    private void batchInsertPublishOperationLog(String accountNumber, Integer ruleId, Set<String> spuSet, String message) {
        if (CollectionUtils.isEmpty(spuSet)) {
            return;
        }

        Timestamp timestamp = new Timestamp(System.currentTimeMillis());
        String ruleIdStr = ruleId == null ? "" : ruleId.toString();

        // 内存优化：分批处理日志插入，控制单次创建的对象数量
        List<String> spuList = new ArrayList<>(spuSet);
        for (int i = 0; i < spuList.size(); i += LOG_BATCH_SIZE) {
            int endIndex = Math.min(i + LOG_BATCH_SIZE, spuList.size());
            List<String> batchSpuList = spuList.subList(i, endIndex);

            List<OzonPublishOperationLog> ozonPublishOperationLogs = new ArrayList<>(batchSpuList.size());
            for (String spu : batchSpuList) {
                OzonPublishOperationLog publishOperationLog = new OzonPublishOperationLog();
                publishOperationLog.setOpType(OzonPublishOpTypeEnum.ONLINE_CONFIG.name());
                publishOperationLog.setModId(accountNumber);
                publishOperationLog.setRefKey(spu);
                publishOperationLog.setRefKey1(ruleIdStr);
                publishOperationLog.setRemark(message);
                publishOperationLog.setCreatedTime(timestamp);
                ozonPublishOperationLogs.add(publishOperationLog);
            }

            // 内存优化：分批插入数据库
            ozonPublishOperationLogService.batchInsert(ozonPublishOperationLogs);
            ozonPublishOperationLogs.clear(); // 及时清理集合
        }
    }

    /**
     * 内存优化的日期格式化方法
     * 重用静态格式化器，减少对象创建开销
     *
     * @param date
     * @return
     */
    private String formattedDateOptimized(Date date) {
        LocalDateTime localDateTime = date.toInstant()
                .atZone(ZoneId.systemDefault())
                .toLocalDateTime();

        // 将 LocalDateTime 转换为指定时区的 ZonedDateTime
        ZonedDateTime zonedDateTime = localDateTime.atZone(ZoneId.systemDefault())
                .withZoneSameInstant(ZoneId.of("GMT+8"));

        // 内存优化：使用静态格式化器，避免重复创建
        return zonedDateTime.format(DATE_FORMATTER);
    }
}

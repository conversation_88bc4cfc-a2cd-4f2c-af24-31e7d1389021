package com.estone.erp.publish.walmart.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2022/8/17 18:37
 */
@Getter
public enum WalmartTemplateStatusEnum {

    WAIT_PUBLISH(1, "待刊登"),

    WAIT_RELEASE(2, "待发布"),

    PUBLISHING(3, "刊登中"),

    PUBLISH_SUCCESS(4, "刊登成功"),

    PUBLISH_FAILED(5, "刊登失败"),

    PARTIAL_SUCCESS(6, "部分成功");

    private int code;

    private String name;

    private WalmartTemplateStatusEnum(int code, String name) {
        this.name = name;
        this.code = code;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public static WalmartTemplateStatusEnum build(int code) {
        WalmartTemplateStatusEnum[] values = values();
        for (WalmartTemplateStatusEnum type : values) {
            if (type.code == code) {
                return type;
            }
        }
        return null;
    }

    public static String getNameByCode(int code) {
        WalmartTemplateStatusEnum[] values = values();
        for (WalmartTemplateStatusEnum type : values) {
            if (type.code == code) {
                return type.getName();
            }
        }
        return null;
    }
}
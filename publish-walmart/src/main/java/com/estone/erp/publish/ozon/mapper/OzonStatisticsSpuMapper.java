package com.estone.erp.publish.ozon.mapper;

import com.estone.erp.publish.ozon.model.OzonStatisticsSpu;
import com.estone.erp.publish.ozon.model.OzonStatisticsSpuExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface OzonStatisticsSpuMapper {
    int countByExample(OzonStatisticsSpuExample example);

    int deleteByPrimaryKey(List<Integer> ids);

    int insert(OzonStatisticsSpu record);

    OzonStatisticsSpu selectByPrimaryKey(Integer id);

    List<OzonStatisticsSpu> selectByExample(OzonStatisticsSpuExample example);

    int updateByExampleSelective(@Param("record") OzonStatisticsSpu record, @Param("example") OzonStatisticsSpuExample example);

    int updateByPrimaryKeySelective(OzonStatisticsSpu record);

    void batchInsert(List<OzonStatisticsSpu> list);

}
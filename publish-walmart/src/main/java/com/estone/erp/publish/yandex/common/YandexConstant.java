package com.estone.erp.publish.yandex.common;

public class YandexConstant {
    public static final String YANDEX_ES_INDEX = "yandex_item";

    /**
     * 同步产品明细限流KEY
     */
    public static final String YANDEX_API_PRODUCT_DETAIL_LIST_LIMIT_KEY = "product_detail_list-";

    public static final String YANDEX_API_GET_STOCK_LIMIT_KEY = "get_stock-";
    /**
     * 在线列表查询字段
     */
    public static final String[] LISTING_FILES = {"id", "mainImage", "accountNumber", "sellerSku",
            "name", "categoryId", "categoryPath", "state", "stock", "weight", "price", "currencyCode",
            "sku", "spu", "skuStatus", "productCategoryId", "productCategoryIdPath", "forbidChannel", "infringementObjs",
            "infringementTypeNames", "prohibitionSites", "tagCodes", "specialGoodsCode", "promotion",
            "newState", "skuDataSource", "composeStatus", "createDate", "updateDate",
            "syncDate", "productCategoryCnName", "stockSyncDate", "actualWeight", "weightDifference", "onlineState", "length", "width", "height", "discountBase"
    };


    /**
     * 修改库存查询
     */
    public static final String[] UPDATE_STOCK_FILES = {"id", "accountNumber", "sellerSku", "spu", "sku", "skuStatus", "stock", "state", "createDate"};

}

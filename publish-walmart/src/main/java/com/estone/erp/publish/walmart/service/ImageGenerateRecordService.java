package com.estone.erp.publish.walmart.service;

import com.estone.erp.common.model.api.CQuery;
import com.estone.erp.common.model.api.CQueryResult;
import com.estone.erp.publish.walmart.model.ImageGenerateRecord;
import com.estone.erp.publish.walmart.model.ImageGenerateRecordCriteria;
import com.estone.erp.publish.walmart.model.ImageGenerateRecordExample;

import java.util.List;

/**
 * <AUTHOR> image_generate_record
 * 2021-03-25 11:06:17
 */
public interface ImageGenerateRecordService {
    int countByExample(ImageGenerateRecordExample example);

    CQueryResult<ImageGenerateRecord> search(CQuery<ImageGenerateRecordCriteria> cquery);

    List<ImageGenerateRecord> selectByExample(ImageGenerateRecordExample example);

    ImageGenerateRecord selectByPrimaryKey(Long id);

    int insert(ImageGenerateRecord record);

    int updateByPrimaryKeySelective(ImageGenerateRecord record);

    int updateByExampleSelective(ImageGenerateRecord record, ImageGenerateRecordExample example);

    int deleteByPrimaryKey(List<Long> ids);
}
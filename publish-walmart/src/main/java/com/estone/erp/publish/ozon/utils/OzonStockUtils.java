package com.estone.erp.publish.ozon.utils;

import com.alibaba.fastjson.JSON;
import com.estone.erp.common.constant.CurrencyConstant;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.publish.base.pms.enums.SkuStatusEnum;
import com.estone.erp.publish.elasticsearch4.model.EsOzonItem;
import com.estone.erp.publish.elasticsearch4.model.EsOzonStockInfo;
import com.estone.erp.publish.ozon.call.model.AccountWareHouseInfo;
import com.estone.erp.publish.ozon.enums.OzonLinkTagEnum;
import com.estone.erp.publish.ozon.enums.OzonStockRoleEnums;
import com.estone.erp.publish.ozon.enums.OzonWarehouseEnum;
import com.estone.erp.publish.ozon.model.OzonAccountConfig;
import com.estone.erp.publish.ozon.model.dto.OzonUpdateDO;
import com.estone.erp.publish.system.account.PriceCalculatedUtil;
import com.estone.erp.publish.system.product.bean.StockObj;
import com.estone.erp.publish.system.product.enums.SpecialTagEnum;
import com.estone.erp.publish.system.product.util.SkuStockUtils;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/3/22 11:13
 */
@Slf4j
public class OzonStockUtils {

    /**
     * 超过该重量无法使用优选仓库
     */
    private static final Double EMBARGO_WEIGHT = 0.48;

    /**
     * 超过该价格无法使用优选仓库
     */
    private static final Double EMBARGO_PRICE = 1400.00;

    /**
     * 获取调库存仓库信息
     */
    public static List<AccountWareHouseInfo> getUpdateStockWarehouse(OzonAccountConfig accountConfig) {
        String updateStockWarehouseIds = accountConfig.getUpdateStockWarehouseIds();
        if (StringUtils.isEmpty(updateStockWarehouseIds)) {
            throw new RuntimeException("未配置默认改库存仓库");
        }

        List<AccountWareHouseInfo> wareHouseInfos = JSON.parseArray(accountConfig.getFbsWarehouseInfo(), AccountWareHouseInfo.class);
        List<AccountWareHouseInfo> defaultWarehouse = wareHouseInfos.stream()
                .filter(wareHouseInfo -> null != wareHouseInfo.getWarehouseId()
                        && updateStockWarehouseIds.contains(wareHouseInfo.getWarehouseId().toString())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(defaultWarehouse)) {
            throw new RuntimeException("未匹配店铺仓库信息");
        }

        return defaultWarehouse;
    }

    /**
     * 匹配库存规则
     */
    public static List<OzonUpdateDO> matchStockRule(EsOzonItem item, List<String> specialSkus, List<AccountWareHouseInfo> updateStockWareHouses, OzonStockRoleEnums ozonStockRoleEnums, Integer stockType) {
        try {
            List<OzonUpdateDO> updateDOList = new ArrayList<>();

            // 获取链接库存详情
            List<EsOzonStockInfo> esOzonStockInfos = item.getWarehouseStockInfos();
            if (CollectionUtils.isEmpty(esOzonStockInfos)) {
                if ((item.getStock() != null && item.getStock() > 0) || OzonStockRoleEnums.PROMOTION.equals(ozonStockRoleEnums)) {
                    throw new RuntimeException("链接仓库详情为空，不调库存");
                }
                esOzonStockInfos = new ArrayList<>();
            }

            // 获取系统库存
            StockObj stockObjByType = SkuStockUtils.getStockObjByType(item.getSku(), stockType);
            Integer systemStock = stockObjByType.getResultStock();
            List<AccountWareHouseInfo> defaultWarehouses = new ArrayList<>(updateStockWareHouses);

            for (AccountWareHouseInfo defaultWarehouse : defaultWarehouses) {
                String name = defaultWarehouse.getName().replaceAll("\\s+", " ").trim();
                defaultWarehouse.setName(name);
            }
            // 优先调整优选仓库的库存, 只需要其中一个不为0
            boolean needNotESTDAzero = false;
            List<AccountWareHouseInfo> firstWarehouses = defaultWarehouses
                    .stream().filter(o -> OzonWarehouseEnum.allAutoWareHouseLowerCaseContains(o.getName()))
                    .collect(Collectors.toList());
            for (AccountWareHouseInfo preferredWarehouse : firstWarehouses) {
                EsOzonStockInfo esOzonStockInfo = esOzonStockInfos.stream()
                        .filter(wareHouseInfo -> preferredWarehouse.getWarehouseId().equals(wareHouseInfo.getWarehouseId()))
                        .findFirst()
                        .orElse(null);
                Integer beforeStock = esOzonStockInfo == null ? 0 : Math.toIntExact(esOzonStockInfo.getPresent());
                boolean existESTDA = esOzonStockInfo != null;
                Integer newStock;
                if (checkEmbargoESTDA(item)) {
                    newStock = 0;
                } else {
                    newStock = matchStockNumber(item, specialSkus, beforeStock, systemStock, ozonStockRoleEnums);
                }
                // 当 改后库存大于0 或 改后库存为空改前库存大于0，需要将ESTD仓库调0
                if ((newStock != null && newStock > 0) || (newStock == null && beforeStock > 0)) {
                    needNotESTDAzero = true;
                }
                // beforeStock 这里的0 可能是因为这个仓库不存在 需要判断 existESTDA 是必须存在的
                if (newStock == null) {
                    continue;
                }
                if (!existESTDA) {
                    beforeStock = null;
                }
                if (beforeStock != null && beforeStock.equals(newStock)) {
                    continue;
                }
                OzonUpdateDO ozonUpdateDO = buildUpdateStockDO(item, preferredWarehouse, beforeStock, newStock, stockObjByType);
                updateDOList.add(ozonUpdateDO);
            }
            defaultWarehouses.removeAll(firstWarehouses);

            boolean needESTDZero = false;
            List<AccountWareHouseInfo> preferredWarehouses = defaultWarehouses
                    .stream().filter(o -> OzonWarehouseEnum.ESTD_K.getCode().equalsIgnoreCase(o.getName())
                            || OzonWarehouseEnum.ESTD_L.getCode().equalsIgnoreCase(o.getName())).collect(Collectors.toList());
            for (AccountWareHouseInfo preferredWarehouse : preferredWarehouses) {
                EsOzonStockInfo esOzonStockInfo = esOzonStockInfos.stream()
                        .filter(wareHouseInfo -> preferredWarehouse.getWarehouseId().equals(wareHouseInfo.getWarehouseId()))
                        .findFirst()
                        .orElse(null);
                Integer beforeStock = esOzonStockInfo == null ? 0 : Math.toIntExact(esOzonStockInfo.getPresent());
                boolean existESTDA = esOzonStockInfo != null;
                Integer newStock;
                if (needNotESTDAzero || checkEmbargo(item)) {
                    newStock = 0;
                } else {
                    newStock = matchStockNumber(item, specialSkus, beforeStock, systemStock, ozonStockRoleEnums);
                }
                // 当 改后库存大于0 或 改后库存为空改前库存大于0，需要将ESTD仓库调0
                if ((newStock != null && newStock > 0) || (newStock == null && beforeStock > 0) || needNotESTDAzero) {
                    needESTDZero = true;
                }
                // beforeStock 这里的0 可能是因为这个仓库不存在 需要判断 existESTDA 是必须存在的
                if (newStock == null) {
                    continue;
                }
                if (!existESTDA) {
                    beforeStock = null;
                }
                if (beforeStock != null && beforeStock.equals(newStock)) {
                    continue;
                }
                OzonUpdateDO ozonUpdateDO = buildUpdateStockDO(item, preferredWarehouse, beforeStock, newStock, stockObjByType);
                updateDOList.add(ozonUpdateDO);
            }

            defaultWarehouses.removeAll(preferredWarehouses);

            // needESTDT_K_zero 全局调0时候，全部调0
            needESTDZero = needESTDZero || needNotESTDAzero;

            for (AccountWareHouseInfo defaultWarehouse : defaultWarehouses) {
                EsOzonStockInfo esOzonStockInfo = esOzonStockInfos.stream()
                        .filter(wareHouseInfo -> defaultWarehouse.getWarehouseId().equals(wareHouseInfo.getWarehouseId()))
                        .findFirst()
                        .orElse(null);
                Integer beforeStock = esOzonStockInfo == null ? 0 : Math.toIntExact(esOzonStockInfo.getPresent());
                boolean existESTDA = esOzonStockInfo != null;
                Integer newStock;
                if (checkPreferred(defaultWarehouse, needESTDZero)) {
                    newStock = 0;
                } else {
                    newStock = matchStockNumber(item, specialSkus, beforeStock, systemStock, ozonStockRoleEnums);
                }
                if (newStock == null) {
                    continue;
                }
                if (!existESTDA) {
                    beforeStock = null;
                }
                if (beforeStock != null && beforeStock.equals(newStock)) {
                    continue;
                }
                OzonUpdateDO ozonUpdateDO = buildUpdateStockDO(item, defaultWarehouse, beforeStock, newStock, stockObjByType);
                updateDOList.add(ozonUpdateDO);
            }

            return updateDOList;
        } catch (Exception e) {
            log.error("productId:{}, 匹配库存规则异常：{}", item.getProductId(), e.getMessage(), e);
            return null;
        }
    }

    private static Integer matchStockNumber(EsOzonItem item, List<String> specialSkus, Integer beforeStock, Integer systemStock, OzonStockRoleEnums ozonStockRoleEnums) {
        Integer stock;
        if (OzonStockRoleEnums.GOOD_SOURCE.equals(ozonStockRoleEnums)) {
            stock = matchGoodSourceStockNumber(item, specialSkus, beforeStock, systemStock);
        } else if (OzonStockRoleEnums.NORMAL.equals(ozonStockRoleEnums)) {
            stock = matchNormalStockNumber(item, specialSkus, beforeStock, systemStock);
        } else if (OzonStockRoleEnums.REAL_AND_IN_TRANSIT.equals(ozonStockRoleEnums)) {
            stock = matchRealStockNumber(item, beforeStock, systemStock);
        } else if (OzonStockRoleEnums.PROMOTION.equals(ozonStockRoleEnums)) {
            stock = matchPromotionStockNumber(systemStock);
        } else {
            throw new RuntimeException("请传入正确库存规则类型");
        }

        return stock;
    }

    private static Integer matchPromotionStockNumber(Integer systemStock) {
        // 可用-待发
        if (null == systemStock) {
            return null;
        }

        BigDecimal newStock = new BigDecimal(systemStock).divide(new BigDecimal(2), RoundingMode.UP);
        return newStock.intValue();
    }

    private static Integer matchNormalStockNumber(EsOzonItem item, List<String> customMadeList, Integer beforeStock, Integer stock) {
        String sku = item.getSku();
        if (StringUtils.isEmpty(sku)) {
            XxlJobLogger.log("id:{},sku为空", item.getId());
            return null;
        }

        boolean isCustom = customMadeList.contains(item.getSku());
        // 定制产品按 实际库存/2（不含在途）设置店铺库存。
        if (isCustom) {
            if (stock == null) {
                return null;
            }
            BigDecimal newStock = new BigDecimal(stock).divide(new BigDecimal(2), RoundingMode.UP);
            XxlJobLogger.log("[{}-{}, allStock:{}, itemStock:{}]: 定制产品按 可用库存/2 设置店铺库存:{}", item.getId(), item.getSku(), stock, beforeStock, newStock);
            return newStock.intValue();
        }

        // 单品状态为正常非定制产品，在线库存为0，恢复库存为9999
        if (SkuStatusEnum.NORMAL.getCode().equals(item.getSkuStatus())
                && Integer.valueOf(0).equals(beforeStock)) {
            XxlJobLogger.log("[{}-{}, itemStock:{}]:单品状态为正常非定制产品，在线库存为0，恢复库存为9999", item.getId(), item.getSku(), beforeStock);
            return 9999;
        }
        return null;
    }

    private static Integer matchRealStockNumber(EsOzonItem item, Integer beforeStock, Integer skuAllStock) {
        String sku = item.getSku();
        if (StringUtils.isEmpty(sku)) {
            XxlJobLogger.log("id:{},sku为空", item.getId());
            return null;
        }
        if (skuAllStock == null) {
            XxlJobLogger.log("id:{},sku:{},系统库存为空", item.getId(), sku);
            return null;
        }
        // 在线列表子SKU，可用库存+在途库存为0，在线列表库存不为0，则调整在线列表库存为0.
        if (skuAllStock == 0 && beforeStock > 0) {
            XxlJobLogger.log("[{}-{}, allStock:{}, itemStock:{}]: 在线列表子SKU，可用库存+在途库存为0，在线列表库存不为0，则调整在线列表库存为0.", item.getId(), sku, skuAllStock, beforeStock);
            return 0;
        }
        // 在线列表子SKU，可用库存+在途不为>10，在线列表库存小于1000，则调整在线列表库存为9999.
        if (skuAllStock > 10 && beforeStock < 1000) {
            XxlJobLogger.log("[{}-{}, allStock:{}, itemStock:{}]: 可用库存+在途不为>10，在线列表库存小于1000，则调整在线列表库存为9999..", item.getId(), sku, skuAllStock, beforeStock);
            return 9999;
        }
        // 可用库存+在途库存大于0小于等于10，则调整在线列表库存为(可用+在途库存)/2，向上取整
        if (skuAllStock > 0 && skuAllStock <=10) {
            BigDecimal newStock = new BigDecimal(skuAllStock).divide(new BigDecimal(2), RoundingMode.UP);
            XxlJobLogger.log("[{}-{}, allStock:{}, itemStock:{}]: 可用库存+在途库存大于0小于等于10，则调整在线列表库存为(可用+在途库存)/2，向上取整:{}", item.getId(), sku, skuAllStock, beforeStock, newStock);
            return newStock.intValue();
        }
        XxlJobLogger.log("[{}-{}, allStock:{}, itemStock:{}]: 未匹配到库存规则", item.getId(), sku, skuAllStock, beforeStock);
        return null;
    }

    private static Integer matchGoodSourceStockNumber(EsOzonItem item, List<String> supplyListSku, Integer beforeStock, Integer skuAllStock) {
        String sku = item.getSku();
        if (StringUtils.isEmpty(sku)) {
            XxlJobLogger.log("id:{},sku为空", item.getId());
            return null;
        }

        // 货源地是广东的，正常状态的，店铺库存为0时，自动补库存为99（新增）
        if (supplyListSku.contains(sku)
                && SkuStatusEnum.NORMAL.getCode().equals(item.getSkuStatus())) {
            if (Integer.valueOf(0).equals(beforeStock)) {
                XxlJobLogger.log("[{}-{}, allStock:{}, itemStock:{}]: 货源地是广东的，正常状态的，店铺库存为0时，自动补库存为99（新增）", item.getId(), sku, null, beforeStock);
                return 99;
            } else {
                return null;
            }
        }

        if (skuAllStock == null) {
            XxlJobLogger.log("id:{},sku:{},系统库存为空", item.getId(), sku);
            return null;
        }
        // 可用库存+在途库存为0，在线列表库存不为0，则调整在线列表库存为0.
        if (skuAllStock == 0 && !Integer.valueOf(0).equals(beforeStock)) {
            XxlJobLogger.log("[{}-{}, allStock:{}, itemStock:{}]: 货源地非广东正常状态，可用库存+在途库存为0，在线列表库存不为0，则调整在线列表库存为0.", item.getId(), sku, skuAllStock, beforeStock);
            return 0;
        }

        // 在线列表子SKU，可用库存+在途库存 >10，在线列表库存小于1000，则调整在线列表库存为9999.
        if (skuAllStock > 10 && beforeStock < 1000) {
            XxlJobLogger.log("[{}-{}, allStock:{}, itemStock:{}]: 在线列表子SKU，可用库存+在途库存 >10，在线列表库存小于1000，则调整在线列表库存为9999.", item.getId(), sku, skuAllStock, beforeStock);
            return 9999;
        }
        // 可用库存+在途库存大于0小于等于10，则调整在线列表库存为(可用+在途库存)/2，向上取整
        if (skuAllStock > 0 && skuAllStock <=10) {
            BigDecimal newStock = new BigDecimal(skuAllStock).divide(new BigDecimal(2), RoundingMode.UP);
            XxlJobLogger.log("[{}-{}, allStock:{}, itemStock:{}]: 可用库存+在途库存大于0小于等于10，则调整在线列表库存为(可用+在途库存)/2，向上取整:{}", item.getId(), sku, skuAllStock, beforeStock, newStock);
            return newStock.intValue();
        }
        XxlJobLogger.log("[{}-{}, allStock:{}, itemStock:{}]: 未匹配到库存规则", item.getId(), sku, skuAllStock, beforeStock);
        return null;
    }

    /**
     * 若ESTD K或ESTD L仓库库存调为非0，则默认仓库ESTD的库存调0
     */
    public static boolean checkPreferred(AccountWareHouseInfo defaultWarehouse, boolean needESTDZero) {
        if (!OzonWarehouseEnum.ESTD.getCode().equalsIgnoreCase(defaultWarehouse.getName())) {
            return false;
        }

        return needESTDZero;
    }

    /**
     * 校验当前连接是否禁运优选仓库
     */
    public static boolean checkEmbargo(EsOzonItem item) {
        // 校验重量
        Double weight = item.getWeight();
        if (weight != null && weight > EMBARGO_WEIGHT) {
            return true;
        }

        // 校验特殊标签
        List<Integer> specialGoodsCode = item.getSpecialGoodsCode();
        if (CollectionUtils.isNotEmpty(specialGoodsCode) && specialGoodsCode.contains(SpecialTagEnum.s_2029.code)) {
            return true;
        }

        // 校验链接标签
        Integer linkTag = item.getLinkTag();
        if (OzonLinkTagEnum.EMBARGO_PRODUCTS.getCode().equals(linkTag)) {
            return true;
        }

        // 校验价格
        Double priceNumber = item.getPriceNumber();
        String currencyCode = item.getCurrencyCode();
        if (!CurrencyConstant.RUB.equalsIgnoreCase(currencyCode)) {
            // 获取汇率
            ApiResult<Double> rubRateResult = PriceCalculatedUtil.getExchangeRate(currencyCode, CurrencyConstant.RUB);
            if (!rubRateResult.isSuccess()) {
                throw new RuntimeException(String.format("%s获取汇率失败：%s", CurrencyConstant.RUB, rubRateResult.getErrorMsg()));
            }
            Double rubRate = rubRateResult.getResult();
            priceNumber = priceNumber * rubRate;
        }
        return priceNumber > EMBARGO_PRICE;
    }

    /**
     * 校验当前连接是否禁运优选仓库
     */
    public static boolean checkEmbargoESTDA(EsOzonItem item) {
        // 校验特殊标签
        List<Integer> specialGoodsCode = item.getSpecialGoodsCode();
        if (CollectionUtils.isNotEmpty(specialGoodsCode) && specialGoodsCode.contains(SpecialTagEnum.s_2029.code)) {
            return true;
        }
        // 校验链接标签
        Integer linkTag = item.getLinkTag();
        if (OzonLinkTagEnum.EMBARGO_PRODUCTS.getCode().equals(linkTag)) {
            return true;
        } else {
            return false;
        }
    }

    public static OzonUpdateDO buildUpdateStockDO(EsOzonItem item, AccountWareHouseInfo defaultWarehouse, Integer beforeStock, Integer newStock, StockObj stockObj) {
        OzonUpdateDO ozonUpdateDO = new OzonUpdateDO();
        ozonUpdateDO.setProductId(item.getProductId());
        ozonUpdateDO.setAccountNumber(item.getAccountNumber());
        ozonUpdateDO.setSku(item.getSku());
        ozonUpdateDO.setSellerSku(item.getSellerSku());
        ozonUpdateDO.setUpdateBeforeStock(beforeStock);
        ozonUpdateDO.setUpdateAfterStock(newStock);
        ozonUpdateDO.setWarehouseId(defaultWarehouse.getWarehouseId());
        ozonUpdateDO.setWarehouseName(defaultWarehouse.getName());
        ozonUpdateDO.setStockObj(stockObj);
        return ozonUpdateDO;
    }
}

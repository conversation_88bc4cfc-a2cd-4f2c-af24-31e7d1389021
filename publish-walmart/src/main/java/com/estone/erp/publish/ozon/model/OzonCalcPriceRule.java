package com.estone.erp.publish.ozon.model;

import com.estone.erp.publish.common.util.CommonUtils;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.List;

@Data
public class OzonCalcPriceRule implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     *  database column ozon_calc_price_rule.id
     */
    private Integer id;

    /**
     * 店铺账号 database column ozon_calc_price_rule.account_number
     */
    private String accountNumber;

    /**
     * 仓库ID database column ozon_calc_price_rule.warehouse_id
     */
    private Long warehouseId;

    /**
     * 标签 database column ozon_calc_price_rule.label
     */
    private String label;

    /**
     * 重量价区间> database column ozon_calc_price_rule.from_weight
     */
    private Double fromWeight;

    /**
     * 重量区间<= database column ozon_calc_price_rule.to_weight
     */
    private Double toWeight;

    /**
     * 试算物流 database column ozon_calc_price_rule.calc_logistics
     */
    private String calcLogistics;

    /**
     * 毛利率 database column ozon_calc_price_rule.gross_profit_rate
     */
    private Double grossProfitRate;

    /**
     * 折扣率 database column ozon_calc_price_rule.discount_rate
     */
    private Double discountRate;

    /**
     * 创建时间 database column ozon_calc_price_rule.create_date
     */
    private Timestamp createDate;

    /**
     * 创建人 database column ozon_calc_price_rule.create_by
     */
    private String createBy;

    /**
     * 修改时间 database column ozon_calc_price_rule.update_date
     */
    private Timestamp updateDate;

    /**
     * 修改人 database column ozon_calc_price_rule.update_by
     */
    private String updateBy;

    /**
     * 是否自动 0 否 1 是
     */
    private Integer isAuto;

    public List<String> getLabelList() {
        return CommonUtils.splitList(this.label, ",");
    }
}
package com.estone.erp.publish.ozon.service;

import com.estone.erp.common.model.api.CQuery;
import com.estone.erp.common.model.api.CQueryResult;
import com.estone.erp.publish.ozon.model.OzonStatisticsListingCriteria;
import com.estone.erp.publish.ozon.model.OzonStatisticsListingExample;
import com.estone.erp.publish.ozon.model.OzonStatisticsListing;

import java.util.List;

/**
 * <AUTHOR>
 * 2024-12-27 10:25:58
 */
public interface OzonStatisticsListingService {
    int countByExample(OzonStatisticsListingExample example);

    CQueryResult<OzonStatisticsListing> search(CQuery<OzonStatisticsListingCriteria> cquery);

    List<OzonStatisticsListing> selectByExample(OzonStatisticsListingExample example);

    OzonStatisticsListing selectByPrimaryKey(Integer id);

    int insert(OzonStatisticsListing record);

    int updateByPrimaryKeySelective(OzonStatisticsListing record);

    int updateByExampleSelective(OzonStatisticsListing record, OzonStatisticsListingExample example);

    int deleteByPrimaryKey(List<Integer> ids);

    void batchInsert(List<OzonStatisticsListing> list);
}
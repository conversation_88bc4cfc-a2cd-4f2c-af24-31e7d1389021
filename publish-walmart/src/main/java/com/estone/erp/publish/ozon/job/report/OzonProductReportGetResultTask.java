package com.estone.erp.publish.ozon.job.report;

import com.alibaba.nacos.common.utils.CollectionUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.estone.erp.common.jobHandler.AbstractJobHandler;
import com.estone.erp.common.mq.RabbitMqSender;
import com.estone.erp.publish.ozon.call.OzonApiClient;
import com.estone.erp.publish.ozon.call.OzonResponseResult;
import com.estone.erp.publish.ozon.call.model.response.ReportInfoResponse;
import com.estone.erp.publish.ozon.job.report.enums.OzonOperatorStatusEnum;
import com.estone.erp.publish.ozon.job.report.enums.OzonReportTaskStatusEnum;
import com.estone.erp.publish.ozon.mq.OzonMqConfig;
import com.estone.erp.publish.ozon.mq.bean.OzonBizId;
import com.estone.erp.publish.tidb.publishtidb.model.OzonReportTask;
import com.estone.erp.publish.tidb.publishtidb.service.IOzonReportTaskService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 获取报告结果的任务，并解析
 */
@Component
public class OzonProductReportGetResultTask extends AbstractJobHandler {

    @Resource
    private IOzonReportTaskService iOzonReportTaskService;

    @Resource
    private OzonApiClient ozonApiClient;

    @Resource
    private RabbitMqSender rabbitMqSender;

    public OzonProductReportGetResultTask() {
        super("OzonProductReportGetResultTask");
    }

    @XxlJob("OzonProductReportGetResultTask")
    @Override
    public ReturnT<String> run(String param) throws Exception {
        getGenStatusResult();
        parseStatusResult();
        return ReturnT.SUCCESS;
    }

    /**
     * 获取报告结果并解析
     */
    private void parseStatusResult() {
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime beforeDay = now.plusDays(-1);
        LambdaQueryWrapper<OzonReportTask> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(OzonReportTask::getStatus, OzonReportTaskStatusEnum.SUCCESS.getCode());
        wrapper.eq(OzonReportTask::getGenResultStatus, OzonOperatorStatusEnum.SUCCESS.getStatus());
        wrapper.eq(OzonReportTask::getParseFileStatus, OzonOperatorStatusEnum.WAITING.getStatus());
        wrapper.gt(OzonReportTask::getCreatedTime, beforeDay);
        List<OzonReportTask> list = iOzonReportTaskService.list(wrapper);

        if (CollectionUtils.isEmpty(list)) {
            XxlJobLogger.log("没有需要解析平台报告的任务");
            return;
        }

        for (OzonReportTask ozonReportTask : list) {
            XxlJobLogger.log("开始解析平台报告，任务ID：{}, 店铺：{}， 日期：{}", ozonReportTask.getId(), ozonReportTask.getAccountNumber(), ozonReportTask.getDay());
            ozonReportTask.setParseFileStatus(OzonOperatorStatusEnum.EXECUTING.getStatus());
            ozonReportTask.setUpdatedTime(now);
            iOzonReportTaskService.updateById(ozonReportTask);

            OzonBizId ozonBizId = new OzonBizId();
            ozonBizId.setId(ozonReportTask.getId());
            rabbitMqSender.allPublishVHostRabbitTemplateSend(OzonMqConfig.OZON_API_DIRECT_EXCHANGE, OzonMqConfig.OZON_PARSE_PRODUCT_REPORT_QUEUE_KEY, ozonBizId);
        }
    }

    /**
     * 获取生成结果
     */
    private void getGenStatusResult() {
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime beforeDay = now.plusDays(-1);
        LambdaQueryWrapper<OzonReportTask> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(OzonReportTask::getStatus, List.of(OzonReportTaskStatusEnum.WAITING.getCode(), OzonReportTaskStatusEnum.PROCESSING.getCode()));
        wrapper.eq(OzonReportTask::getGenResultStatus, OzonOperatorStatusEnum.WAITING.getStatus());
        wrapper.gt(OzonReportTask::getCreatedTime, beforeDay);
        List<OzonReportTask> list = iOzonReportTaskService.list(wrapper);
        if (CollectionUtils.isEmpty(list)) {
            XxlJobLogger.log("没有需要获取平台报告生成结果的任务");
            return;
        }

        for (OzonReportTask ozonReportTask : list) {
            XxlJobLogger.log("开始获取平台报告生成结果，任务ID：{}, 店铺：{}， 日期：{}", ozonReportTask.getId(), ozonReportTask.getAccountNumber(), ozonReportTask.getDay());
            LocalDateTime updateTime = LocalDateTime.now();
            String code = ozonReportTask.getCode();
            String reportType = ozonReportTask.getReportType();
            String accountNumber = ozonReportTask.getAccountNumber();

            OzonResponseResult<ReportInfoResponse> reportInfo = ozonApiClient.getReportInfo(accountNumber, code);
            if (reportInfo.isSuccess()) {
                ReportInfoResponse data = reportInfo.getResult();
                String url = data.getFile();
                String status = data.getStatus();
                ozonReportTask.setStatus(status);
                ozonReportTask.setMessage(data.getError());
                if (status.equals(OzonReportTaskStatusEnum.SUCCESS.getCode())) {
                    ozonReportTask.setGenResultStatus(OzonOperatorStatusEnum.SUCCESS.getStatus());
                } else if (status.equals(OzonReportTaskStatusEnum.FAILED.getCode())) {
                    ozonReportTask.setGenResultStatus(OzonOperatorStatusEnum.FAILED.getStatus());
                } else {
                    ozonReportTask.setGenResultStatus(OzonOperatorStatusEnum.WAITING.getStatus());
                }

                ozonReportTask.setGenResultStatusTime(updateTime);
                ozonReportTask.setUpdatedTime(updateTime);
                ozonReportTask.setReportType(reportType);
                ozonReportTask.setUrl(url);
                iOzonReportTaskService.updateById(ozonReportTask);
            } else {
                XxlJobLogger.log("获取报告信息失败，原因：" + reportInfo.getMessage());
            }
        }
    }
}

package com.estone.erp.publish.ozon.service;

import com.estone.erp.common.model.api.CQuery;
import com.estone.erp.common.model.api.CQueryResult;
import com.estone.erp.publish.ozon.model.OzonAccountConfig;
import com.estone.erp.publish.ozon.model.OzonAccountGroup;
import com.estone.erp.publish.ozon.model.OzonAccountGroupCriteria;
import com.estone.erp.publish.ozon.model.OzonAccountGroupExample;
import com.estone.erp.publish.ozon.model.dto.OzonAccountGroupDto;
import com.estone.erp.publish.ozon.model.dto.OzonConfigGroupLinkDto;

import java.util.List;

/**
 * <AUTHOR>
 * 2025-03-27 11:03:29
 */
public interface OzonAccountGroupService {
    int countByExample(OzonAccountGroupExample example);

    CQueryResult<OzonAccountGroupDto> search(CQuery<OzonAccountGroupCriteria> cquery);

    List<OzonAccountGroup> selectByExample(OzonAccountGroupExample example);

    OzonAccountGroup selectByPrimaryKey(Integer id);

    int insert(OzonAccountGroup record);

    int updateByPrimaryKeySelective(OzonAccountGroup record);

    int updateByExampleSelective(OzonAccountGroup record, OzonAccountGroupExample example);

    int deleteByPrimaryKey(List<Integer> ids);

    List<String> getAccountNumberByGroupId(List<Integer> ids);

    void deleteOzonAccountGroup(Integer id);

    void sync(List<Integer> ids);

    void saveOrUpdate(OzonAccountGroup ozonAccountGroup);

    List<OzonConfigGroupLinkDto> getGroupConfigDetail(Integer id, boolean b);

    List<String> getNotGroupAccount();
}
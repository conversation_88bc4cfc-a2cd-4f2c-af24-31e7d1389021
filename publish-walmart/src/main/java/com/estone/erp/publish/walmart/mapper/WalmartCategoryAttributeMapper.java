package com.estone.erp.publish.walmart.mapper;

import com.estone.erp.publish.walmart.model.WalmartCategoryAttribute;
import com.estone.erp.publish.walmart.model.WalmartCategoryAttributeExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface WalmartCategoryAttributeMapper {
    int countByExample(WalmartCategoryAttributeExample example);

    int deleteByPrimaryKey(List<Integer> ids);

    int insert(WalmartCategoryAttribute record);

    WalmartCategoryAttribute selectByPrimaryKey(Integer id);

    WalmartCategoryAttribute selectBySubCategoryId(String subCategoryId);

    List<WalmartCategoryAttribute> selectByExample(WalmartCategoryAttributeExample example);

    int updateByExampleSelective(@Param("record") WalmartCategoryAttribute record, @Param("example") WalmartCategoryAttributeExample example);

    int updateByPrimaryKeySelective(WalmartCategoryAttribute record);
}
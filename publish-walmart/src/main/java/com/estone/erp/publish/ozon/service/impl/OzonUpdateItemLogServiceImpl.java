package com.estone.erp.publish.ozon.service.impl;

import com.estone.erp.common.model.api.CQuery;
import com.estone.erp.common.model.api.CQueryResult;
import com.estone.erp.publish.ozon.mapper.OzonUpdateItemLogMapper;
import com.estone.erp.publish.ozon.model.OzonUpdateItemLog;
import com.estone.erp.publish.ozon.model.OzonUpdateItemLogCriteria;
import com.estone.erp.publish.ozon.model.OzonUpdateItemLogExample;
import com.estone.erp.publish.ozon.service.OzonUpdateItemLogService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * 2024-06-18 14:40:15
 */
@Service("ozonUpdateItemLogService")
@Slf4j
public class OzonUpdateItemLogServiceImpl implements OzonUpdateItemLogService {
    @Resource
    private OzonUpdateItemLogMapper ozonUpdateItemLogMapper;

    @Override
    public int countByExample(OzonUpdateItemLogExample example) {
        Assert.notNull(example, "example is null!");
        return ozonUpdateItemLogMapper.countByExample(example);
    }

    @Override
    public CQueryResult<OzonUpdateItemLog> search(CQuery<OzonUpdateItemLogCriteria> cquery) {
        Assert.notNull(cquery, "cquery is null!");
        OzonUpdateItemLogCriteria query = cquery.getSearch();
        OzonUpdateItemLogExample example = query.getExample();
        long total = 0;
        int totalPages = 0;
        // 是否分页
        if (cquery.isPageReqired()) {
            total = ozonUpdateItemLogMapper.countByExample(example);
            totalPages = (int) Math.ceil((double) total / cquery.getLimit());
            example.setLimit(cquery.getLimit());
            example.setOffset(cquery.getOffset());
        }
        List<OzonUpdateItemLog> ozonUpdateItemLogs = ozonUpdateItemLogMapper.selectByExample(example);
        // 组装结果
        CQueryResult<OzonUpdateItemLog> result = new CQueryResult<>();
        result.setTotal(total);
        result.setTotalPages(totalPages);
        result.setRows(ozonUpdateItemLogs);
        return result;
    }

    @Override
    public OzonUpdateItemLog selectByPrimaryKey(Integer id) {
        Assert.notNull(id, "id is null!");
        return ozonUpdateItemLogMapper.selectByPrimaryKey(id);
    }

    @Override
    public List<OzonUpdateItemLog> selectByExample(OzonUpdateItemLogExample example) {
        Assert.notNull(example, "example is null!");
        return ozonUpdateItemLogMapper.selectByExample(example);
    }

    @Override
    public int insert(OzonUpdateItemLog record) {
        Assert.notNull(record, "record is null!");
        // 默认加时间和人
        return ozonUpdateItemLogMapper.insert(record);
    }

    @Override
    public int updateByPrimaryKeySelective(OzonUpdateItemLog record) {
        Assert.notNull(record, "record is null!");
        // 默认加修改时间和修改人
        return ozonUpdateItemLogMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public int updateByExampleSelective(OzonUpdateItemLog record, OzonUpdateItemLogExample example) {
        Assert.notNull(record, "record is null!");
        // 默认加修改时间和修改人
        return ozonUpdateItemLogMapper.updateByExampleSelective(record, example);
    }

    @Override
    public int deleteByPrimaryKey(List<Integer> ids) {
        Assert.notNull(ids, "ids is null!");
        return ozonUpdateItemLogMapper.deleteByPrimaryKey(ids);
    }
}
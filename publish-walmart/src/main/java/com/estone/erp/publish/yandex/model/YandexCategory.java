package com.estone.erp.publish.yandex.model;

import java.io.Serializable;
import java.sql.Timestamp;
import lombok.Data;

@Data
public class YandexCategory implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 
     */
    private Integer id;

    /**
     * 类目id
     */
    private Integer categoryId;

    /**
     * 类目名称
     */
    private String title;

    /**
     * 多语言
     */
    private String lang;

    /**
     * 父节点
     */
    private Integer parentId;

    /**
     * 根节点
     */
    private Integer rootId;

    /**
     * 是否为叶子类目
     */
    private Boolean isLeaf;

    /**
     * 类目id路径
     */
    private String cidPath;

    /**
     * 更新时间
     */
    private Timestamp updateTime;

    /**
     * 创建时间
     */
    private Timestamp createTime;
}
package com.estone.erp.publish.ozon.model.dto;

import com.estone.erp.publish.ozon.call.model.request.CreateProductRequest;
import com.estone.erp.publish.system.feedTask.model.FeedTask;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/5/14 10:15
 */
@Data
public class OzonUpdateItemDO {

    /**
     * 请求
     */
    private CreateProductRequest createProductRequest;

    /**
     * 处理报告
     */
    private FeedTask feedTask;

    /**
     * 处理报告2
     */
    private FeedTask feedTask2;

}

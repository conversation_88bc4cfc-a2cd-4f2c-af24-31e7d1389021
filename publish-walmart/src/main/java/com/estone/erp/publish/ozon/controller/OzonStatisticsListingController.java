package com.estone.erp.publish.ozon.controller;

import com.alibaba.fastjson.TypeReference;
import com.estone.erp.common.model.api.ApiRequestParam;
import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.common.model.api.CQuery;
import com.estone.erp.common.model.api.CQueryResult;
import com.estone.erp.common.util.Asserts;
import com.estone.erp.publish.common.ErrorCode;
import com.estone.erp.publish.ozon.model.OzonStatisticsListingCriteria;
import com.estone.erp.publish.ozon.service.OzonStatisticsListingService;
import com.estone.erp.publish.ozon.model.OzonStatisticsListing;

import javax.annotation.Resource;
import org.apache.commons.lang.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * 2024-12-27 10:25:58
 */
@RestController
@RequestMapping("ozonStatisticsListing")
public class OzonStatisticsListingController {
    @Resource
    private OzonStatisticsListingService ozonStatisticsListingService;

    @PostMapping
    public ApiResult<?> postOzonStatisticsListing(@RequestBody(required = true) ApiRequestParam<String> requestParam) {
        String method = requestParam.getMethod();
        if (StringUtils.isNotBlank(method)) {
            switch (method) {
                case "searchOzonStatisticsListing": // 查询列表
                    CQuery<OzonStatisticsListingCriteria> cquery = requestParam.getArgsValue(new TypeReference<CQuery<OzonStatisticsListingCriteria>>() {});
                    Asserts.isTrue(cquery != null, ErrorCode.PARAM_EMPTY_ERROR);
                    CQueryResult<OzonStatisticsListing> results = ozonStatisticsListingService.search(cquery);
                    return results;
                case "addOzonStatisticsListing": // 添加
                    OzonStatisticsListing ozonStatisticsListing = requestParam.getArgsValue(new TypeReference<OzonStatisticsListing>() {});
                    ozonStatisticsListingService.insert(ozonStatisticsListing);
                    return ApiResult.newSuccess(ozonStatisticsListing);
                }
        }
        return ApiResult.newSuccess();
    }

    @GetMapping(value = "/{id}")
    public ApiResult<?> getOzonStatisticsListing(@PathVariable(value = "id", required = true) Integer id) {
        OzonStatisticsListing ozonStatisticsListing = ozonStatisticsListingService.selectByPrimaryKey(id);
        return ApiResult.newSuccess(ozonStatisticsListing);
    }

    @PutMapping(value = "/{id}")
    public ApiResult<?> putOzonStatisticsListing(@PathVariable(value = "id", required = true) Integer id, @RequestBody ApiRequestParam<String> requestParam) {
        String method = requestParam.getMethod();
        if (StringUtils.isNotBlank(method)) {
            switch (method) {
                case "updateOzonStatisticsListing": // 单个修改
                    OzonStatisticsListing ozonStatisticsListing = requestParam.getArgsValue(new TypeReference<OzonStatisticsListing>() {});
                    ozonStatisticsListingService.updateByPrimaryKeySelective(ozonStatisticsListing);
                    return ApiResult.newSuccess(ozonStatisticsListing);
                }
        }
        return ApiResult.newSuccess();
    }
}
package com.estone.erp.publish.ozon.utils;

import com.estone.erp.common.model.api.ApiResult;
import com.estone.erp.publish.common.SaleChannel;
import com.estone.erp.publish.system.fms.FmsUtils;
import com.estone.erp.publish.system.fms.enums.VideoTypeEnum;
import com.estone.erp.publish.system.fms.model.VideoDetail;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 视频
 */
@Slf4j
public class OzonVideoUtil {

    /**
     * 获取视频链接
     * @param spu spu
     * @return 视频
     */
    public static String getVideoLink(String spu) {
        // 获取产品系统视频
        String videoLink = FmsUtils.getVideoLink(spu);
        if (StringUtils.isNotBlank(videoLink)) {
            return videoLink;
        }
        // 获取产品系统制作的视频
        videoLink = getProductVideoLink(spu);
        if (StringUtils.isNotBlank(videoLink)) {
            return videoLink;
        }
        // 不存在则查询 销售制作 刊登系统生成的 视频
        videoLink = getPublishVideoLink(spu);
        return videoLink;
    }

    /**
     * 获取产品系统制作视频
     *
     * @param spu spu
     * @return 视频
     */
    public static String getProductVideoLink(String spu) {
        ApiResult<List<VideoDetail>> apiResult = FmsUtils.getVideoDetailBySpu(spu);
        if (apiResult.isSuccess() && CollectionUtils.isNotEmpty(apiResult.getResult())) {
            List<VideoDetail> videoDetails = apiResult.getResult();
            List<String> videoList = videoDetails.stream().filter(o -> org.apache.commons.lang.StringUtils.isNotBlank(o.getVideoLink())).map(VideoDetail::getVideoLink).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(videoList)) {
                Collections.shuffle(videoList);
                return videoList.get(0);
            }
        }
        return null;
    }


    /**
     * 查询 销售制作 刊登系统生成的 视频
     *
     * @param spu spu
     * @return 视频
     */
    public static String getPublishVideoLink(String spu) {
        // 查询是否有销售
        String video = getVideo(spu, SaleChannel.CHANNEL_OZON, VideoTypeEnum.sale);
        if (StringUtils.isNotBlank(video)) {
            return video;
        }
        // 查询刊登生成的视频
        video = getVideo(spu, SaleChannel.CHANNEL_OZON, VideoTypeEnum.system);
        if (StringUtils.isNotBlank(video)) {
            return video;
        }
        // 获取smt平台的视频
        video = getVideo(spu, SaleChannel.CHANNEL_SMT, VideoTypeEnum.sale);
        if (StringUtils.isNotBlank(video)) {
            return video;
        }
        video = getVideo(spu, SaleChannel.CHANNEL_SMT, VideoTypeEnum.system);
        return video;
    }

    public static String getVideo(String spu, String platform, VideoTypeEnum videoTypeEnum) {
        try {
            String filePath = FmsUtils.getFilePath(platform, videoTypeEnum.getCode());
            ApiResult<List<String>> publishVideoResult = FmsUtils.getPublishVideo(spu, filePath);
            if (!publishVideoResult.isSuccess()) {
                log.error(String.format("[%s]获取文件系统刊登生成的视频异常:[%s]", spu, publishVideoResult.getErrorMsg()));
            }
            List<String> result = publishVideoResult.getResult();
            if (CollectionUtils.isNotEmpty(result)) {
                Collections.shuffle(result);
                return result.get(0);
            }
        } catch (Exception e) {
            log.error(String.format("[%s]获取文件系统刊登生成的视频异常Exception:[%s]", spu, e.getMessage()), e);
        }
        return null;
    }
}

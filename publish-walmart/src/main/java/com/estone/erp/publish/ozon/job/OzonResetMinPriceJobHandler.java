package com.estone.erp.publish.ozon.job;

import com.alibaba.fastjson.JSON;
import com.estone.erp.common.jobHandler.AbstractJobHandler;
import com.estone.erp.publish.ozon.service.OzonEsItemService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @Description: OZON最低价重设定时任务
 * @ClassName: OzonResetMinPriceJobHandler
 * @Author: system
 * @Date: 2024年12月19日
 * @Version: 1.0.0
 */
@Slf4j
@Component
public class OzonResetMinPriceJobHandler extends AbstractJobHandler {

    @Autowired
    private OzonEsItemService ozonEsItemService;

    @Data
    public static class InnerParam {
        /**
         * 指定店铺列表
         */
        private List<String> accountNumberList;
        
        /**
         * 指定SKU列表
         */
        private List<String> skuList;
    }

    public OzonResetMinPriceJobHandler() {
        super(OzonResetMinPriceJobHandler.class.getName());
    }

    /**
     * OZON最低价重设任务
     * 对于设置时间超过20天的商品，使用当前最低价重新设置最低价
     * 
     * 支持参数格式：
     * {
     *   "type": "all",  // all|account|sku
     *   "accountNumberList": ["account1", "account2"],  // 指定店铺
     *   "skuList": ["sku1", "sku2"],  // 指定SKU
     *   "sellerSkuList": ["sellerSku1", "sellerSku2"]  // 指定sellerSku
     * }
     */
    @XxlJob("OzonResetMinPriceJobHandler")
    @Override
    public ReturnT<String> run(String param) throws Exception {
        InnerParam innerParam = new InnerParam();
        if(StringUtils.isNotBlank(param)){
            try {
                innerParam = JSON.parseObject(param, InnerParam.class);
            } catch (Exception e) {
                XxlJobLogger.log("参数异常");
                return ReturnT.FAIL;
            }
        }

        List<String> skuList = innerParam.getSkuList();
        List<String> accountNumberList = innerParam.getAccountNumberList();
        
        XxlJobLogger.log("开始执行OZON最低价重设任务，参数：{}", JSON.toJSONString(innerParam));
        log.info("开始执行OZON最低价重设任务，店铺：{}，SKU：{}",
                accountNumberList, skuList);
        
        try {
            // 执行最低价重设逻辑
            ozonEsItemService.resetExpiredMinPrice(accountNumberList, skuList);
            
            XxlJobLogger.log("OZON最低价重设任务执行成功");
            log.info("OZON最低价重设任务执行完成");
            return ReturnT.SUCCESS;
            
        } catch (Exception e) {
            log.error("OZON最低价重设任务执行失败：", e);
            XxlJobLogger.log("OZON最低价重设任务执行失败：" + e.getMessage());
            return ReturnT.FAIL;
        }
    }
} 
package com.estone.erp.publish.ozon.model.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2023/11/20 11:36
 */
@Data
public class OzonUpdateLinkTagDO {

    /**
     * id
     */
    private String id;

    /**
     * 链接标签
     */
    private Integer linkTag;

    /**
     * 修改时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateDate;

}
